# Ollama Configuration Guide

This guide explains how to configure Ollama services in Calcounta.

## Overview

Calcounta includes Ollama integration by default that provides:
- Local AI model hosting with Ollama
- API gateway for secure access
- Web dashboard for API key management
- Rate limiting and authentication
- Automatic model downloading

Ollama services are included by default and the frontend automatically detects their availability.

## Configuration

### Environment Variables

Add the following **required** variables to your `.env` file:

```bash
# Base URL Configuration (REQUIRED)
BASE_URL=http://0.0.0.0

# Port Configuration (REQUIRED)
PORT=86

# Ollama Gateway Port (REQUIRED)
OLLAMA_GATEWAY_PORT=85

# Ollama Models (OPTIONAL)
# Comma-separated list of models to download automatically
OLLAMA_MODELS=llama2,codellama
```

> **Important**: All these variables are required for Ollama services to function properly.

### Base URL Configuration

The `BASE_URL` environment variable controls the base URL used for generating links and accessing services:

**Local Development:**
```bash
BASE_URL=http://localhost
```

**Network Access (other devices can connect):**
```bash
BASE_URL=http://*************  # Use your actual machine IP
```

> **Important**: Do NOT use `0.0.0.0` in BASE_URL! While `0.0.0.0` is used for server binding (making the server listen on all interfaces), it's not a valid URL for clients. Use `localhost` for local-only access or your machine's actual IP address for network access.

**Production with Domain:**
```bash
BASE_URL=https://yourdomain.com
```

**Production with IP:**
```bash
BASE_URL=http://*************
```

**Custom Port (if needed):**
```bash
BASE_URL=http://yourdomain.com:8080
```

Note: The `BASE_URL` should not include the port numbers for the main application or Ollama gateway, as these are configured separately via `PORT` and `OLLAMA_GATEWAY_PORT`.

### Model Configuration

The `OLLAMA_MODELS` environment variable allows you to specify which AI models should be automatically downloaded when the Ollama container starts:

**Examples:**
- `OLLAMA_MODELS=llama2` - Download only Llama 2
- `OLLAMA_MODELS=llama2,codellama,mistral` - Download multiple models
- `OLLAMA_MODELS=llama2:13b,codellama:7b` - Download specific model sizes
- `OLLAMA_MODELS=` - Skip automatic downloads (empty value)

**Available Models:**
- `llama2` - General purpose language model
- `codellama` - Code generation and understanding
- `mistral` - Fast and efficient language model
- `llama2:13b` - Larger Llama 2 variant
- `codellama:7b` - Smaller CodeLlama variant

Models are downloaded in the background during container startup and may take several minutes depending on your internet connection.

## Starting Services

### Using the Startup Script

The recommended way to start Calcounta is using the provided startup script:

```bash
./scripts/start.sh
```

This script automatically:
- Validates your `.env` configuration
- Reads your environment variables
- Starts all services (including Ollama)
- Downloads configured AI models
- Provides status information
- Shows access URLs

**Optional: Validate Configuration Only**

To validate your `.env` file without starting services:

```bash
./scripts/validate-env.sh
```

**Detect Your Machine's IP Address**

To find the correct IP address for network access:

```bash
./scripts/detect-ip.sh
```

This script will suggest the appropriate BASE_URL for your setup.

### Manual Docker Compose

If you prefer manual control:

```bash
docker compose up -d --build
```

All services (including Ollama) are included by default.

## Access Information

**All Services Available:**

- **Main Application**: {BASE_URL}:{PORT}
- **API Dashboard**: {BASE_URL}:{OLLAMA_GATEWAY_PORT}/dashboard
- **Ollama API**: {BASE_URL}:{OLLAMA_GATEWAY_PORT}/ai/

> **Note**: All URLs are dynamically generated from your `.env` configuration. The frontend automatically detects if Ollama services are available and shows/hides the API Dashboard menu item accordingly.

## Resource Usage

**Default Configuration (All Services):**
- ~6-8 containers running
- Higher memory usage (Ollama models)
- Additional storage for models (stored in `./ollama-models/` directory)
- Two PostgreSQL databases

**If You Remove Ollama Services:**
- ~3-4 containers running
- Lower memory usage
- Reduced storage requirements
- Single PostgreSQL database

## Removing Ollama Services

If you don't want Ollama services, you can manually remove them:

1. Edit `docker-compose.yml` and remove these services:
   - `ollama`, `ollama-db`, `ollama-gateway`
   - `ollama-auth-service`, `ollama-proxy`, `ollama-dashboard`, `redis`

2. Remove corresponding volumes and directories:
   - `ollama_postgres_data`, `redis_data` (Docker volumes)
   - `./ollama-models/` (host directory - can be safely deleted)

3. Remove Ollama environment variables from `.env`

4. Restart services:
   ```bash
   docker compose down
   ./scripts/start.sh
   ```

The frontend will automatically hide the API Dashboard menu item.

## Troubleshooting

### Configuration Issues
- Run `./scripts/validate-env.sh` to check your configuration
- Ensure all required variables are set in `.env`
- Check for typos in variable names or values

### Services Not Starting
- Validate your `.env` file with the validation script
- Verify Docker and Docker Compose are installed
- Check available system resources
- Ensure no port conflicts exist

### API Dashboard Not Visible
- Check if Ollama services are running: `docker compose ps`
- Verify `OLLAMA_GATEWAY_PORT` is set in `.env`
- Check that `BASE_URL` is correctly configured
- Ensure Ollama services weren't manually removed from docker-compose.yml
- Restart the frontend container
- Clear browser cache

### Port Conflicts
- Use `./scripts/validate-env.sh` to detect port conflicts
- Ensure `PORT` and `OLLAMA_GATEWAY_PORT` are different
- Check if ports are already in use by other applications
- Restart services after changing ports

### Environment Variable Issues
- All configuration comes from `.env` - no hardcoded defaults
- Use the validation script to identify missing variables
- Check for extra spaces or quotes in variable values
- Ensure `OLLAMA_MODELS` format is correct (comma-separated)

## Default Configuration

The following Ollama services are included by default:

| Service | Container Name | Purpose |
|---------|----------------|---------|
| Ollama | calcounta-ollama | AI model hosting |
| Gateway | calcounta-ollama-gateway | Nginx reverse proxy |
| Auth Service | calcounta-ollama-auth-service | API authentication |
| Proxy | calcounta-ollama-proxy | Request routing |
| Dashboard | calcounta-ollama-dashboard | Web interface |
| Database | calcounta-ollama-db | Ollama data storage |
| Redis | calcounta-redis | Session & rate limiting |

## Security Notes

- Ollama services use separate authentication
- API keys are required for access
- Rate limiting is enforced
- Services are isolated in Docker network
- Database credentials are separate from main app
