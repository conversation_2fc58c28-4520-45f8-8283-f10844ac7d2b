# Deployment Examples

This document provides examples of how to configure Calcounta for different deployment scenarios using the `BASE_URL` environment variable.

## Understanding Server Binding vs Client URLs

**Important Distinction:**
- **Server Binding**: How the server listens for connections (<PERSON><PERSON> handles this with `0.0.0.0`)
- **Client URLs**: What URLs clients use to access the application (set in BASE_URL)

**BASE_URL Options:**

**localhost (Local Access Only)**
- Only accessible from the same machine
- More secure for development
- Example: `BASE_URL=http://localhost`
- Access: `http://localhost:86`

**Machine IP (Network Access)**
- Accessible from other devices on your network
- Allows mobile testing and team development
- Example: `BASE_URL=http://*************`
- Access from any device: `http://*************:86`

**❌ Do NOT use 0.0.0.0 in BASE_URL:**
- `0.0.0.0` is only for server binding, not client access
- Browsers cannot connect to `http://0.0.0.0:86`
- Use your actual IP address instead

**🔧 Find Your IP:**
Run `./scripts/detect-ip.sh` to get the correct BASE_URL for your setup.

## Local Development

For local development on your machine:

```bash
# .env
BASE_URL=http://localhost
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Access URLs:**
- Main App: http://localhost:86
- API Dashboard: http://localhost:85/dashboard

## Network Access (Mobile/Other Devices)

To allow access from other devices on your network:

```bash
# .env
BASE_URL=http://*************  # Replace with your actual IP
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Find Your IP Address:**
```bash
./scripts/detect-ip.sh
```

**Access URLs:**
- From this machine: http://localhost:86 or http://*************:86
- From other devices: http://*************:86
- From mobile devices: http://*************:86
- API Dashboard: http://*************:85/dashboard

## Production with Domain

For production deployment with a custom domain:

```bash
# .env
BASE_URL=https://calcounta.yourdomain.com
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Access URLs:**
- Main App: https://calcounta.yourdomain.com:86
- API Dashboard: https://calcounta.yourdomain.com:85/dashboard

## Production with Reverse Proxy

When using a reverse proxy (nginx, Apache, etc.) that handles SSL and port mapping:

```bash
# .env
BASE_URL=https://calcounta.yourdomain.com
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Nginx Configuration Example:**
```nginx
server {
    listen 80;
    server_name calcounta.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name calcounta.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # Main application
    location / {
        proxy_pass http://localhost:86;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API Dashboard (optional subdomain)
    # Or you could use api.calcounta.yourdomain.com
}

server {
    listen 443 ssl;
    server_name api.calcounta.yourdomain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # Ollama API Dashboard
    location / {
        proxy_pass http://localhost:85;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

**Access URLs:**
- Main App: https://calcounta.yourdomain.com
- API Dashboard: https://api.calcounta.yourdomain.com

## LAN/Network Deployment

For deployment on a local network where other devices need access:

```bash
# .env
BASE_URL=http://*************
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Access URLs:**
- Main App: http://*************:86
- API Dashboard: http://*************:85/dashboard

## Docker Swarm/Kubernetes

For container orchestration platforms:

```bash
# .env
BASE_URL=https://calcounta-service.cluster.local
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

## Cloud Deployment (AWS, GCP, Azure)

For cloud deployments with load balancers:

```bash
# .env
BASE_URL=https://calcounta.example.com
PORT=86
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=85
```

**Notes:**
- Load balancer handles SSL termination
- Internal container ports remain the same
- External access through load balancer URLs

## Development with Custom Ports

If you need to run on different ports to avoid conflicts:

```bash
# .env
BASE_URL=http://localhost
PORT=3000
OLLAMA_ENABLED=true
OLLAMA_GATEWAY_PORT=3001
```

**Docker Compose Override:**
```yaml
# docker-compose.override.yml
services:
  frontend:
    ports:
      - "3000:80"
  
  ollama-gateway:
    ports:
      - "3001:80"
```

**Access URLs:**
- Main App: http://localhost:3000
- API Dashboard: http://localhost:3001/dashboard

## Disabling Ollama for Lightweight Deployment

For deployments where you don't need Ollama services:

```bash
# .env
BASE_URL=https://calcounta-lite.yourdomain.com
PORT=86
OLLAMA_ENABLED=false
```

**Benefits:**
- Reduced resource usage
- Faster startup time
- Simpler deployment
- No API Dashboard

**Access URLs:**
- Main App: https://calcounta-lite.yourdomain.com:86
- API Dashboard: Not available

## Environment-Specific Configuration

You can use different `.env` files for different environments:

```bash
# .env.development
BASE_URL=http://localhost
OLLAMA_ENABLED=true

# .env.staging
BASE_URL=https://staging.calcounta.com
OLLAMA_ENABLED=true

# .env.production
BASE_URL=https://calcounta.com
OLLAMA_ENABLED=false
```

Then copy the appropriate file:
```bash
cp .env.production .env
```
