# Client Secret Encryption Enhancement

## Current Implementation
- Client secrets are **hashed** using bcrypt (one-way encryption)
- Secrets cannot be retrieved once stored
- Users must regenerate secrets to get a new one

## Proposed Enhancement: Encrypted Storage

### Overview
Change from hashing to symmetric encryption to allow secret retrieval while maintaining security.

### Implementation Steps

#### 1. Add Encryption Functions

```python
import os
from cryptography.fernet import Fernet
import base64

def get_encryption_key():
    """Get or generate encryption key for client secrets"""
    key = os.getenv('CLIENT_SECRET_ENCRYPTION_KEY')
    if not key:
        # Generate new key (do this once and store in environment)
        key = Fernet.generate_key().decode()
        print(f"Generated new encryption key: {key}")
        print("Add this to your .env file as CLIENT_SECRET_ENCRYPTION_KEY")
    return key.encode()

def encrypt_client_secret(secret: str) -> str:
    """Encrypt client secret for storage"""
    key = get_encryption_key()
    f = Fernet(key)
    encrypted = f.encrypt(secret.encode())
    return base64.b64encode(encrypted).decode()

def decrypt_client_secret(encrypted_secret: str) -> str:
    """Decrypt client secret for display"""
    key = get_encryption_key()
    f = Fernet(key)
    encrypted_bytes = base64.b64decode(encrypted_secret.encode())
    decrypted = f.decrypt(encrypted_bytes)
    return decrypted.decode()
```

#### 2. Update Database Schema

```sql
-- Migration script
ALTER TABLE ollama_api_keys 
RENAME COLUMN client_secret_hash TO client_secret_encrypted;

-- Update column comment
COMMENT ON COLUMN ollama_api_keys.client_secret_encrypted IS 'Encrypted client secret (can be decrypted for viewing)';
```

#### 3. Update API Endpoints

```python
@app.route('/api/keys/<key_id>/secret', methods=['GET'])
@jwt_required()
def get_api_key_secret(key_id):
    """Get the client secret for viewing (requires recent authentication)"""
    try:
        user_id = get_jwt_identity()
        
        # Additional security: require recent authentication
        auth_time = session.get('auth_time', 0)
        if time.time() - auth_time > 300:  # 5 minutes
            return jsonify({'error': 'Recent authentication required'}), 401
        
        conn = get_db_connection()
        cur = conn.cursor()
        
        cur.execute("""
            SELECT id, name, client_id, client_secret_encrypted 
            FROM ollama_api_keys
            WHERE id = %s AND user_id = %s
        """, (key_id, user_id))
        
        key = cur.fetchone()
        if not key:
            return jsonify({'error': 'API key not found'}), 404
        
        # Decrypt the secret
        decrypted_secret = decrypt_client_secret(key[3])
        
        # Log access for security audit
        logger.info(f"Client secret accessed: {key[1]} (user: {user_id})")
        
        return jsonify({
            'client_secret': decrypted_secret,
            'warning': 'This secret is sensitive. Do not share it.'
        })
        
    except Exception as e:
        logger.error(f"Get client secret error: {e}")
        return jsonify({'error': 'Failed to retrieve secret'}), 500
```

#### 4. Update Frontend

```javascript
async function viewClientSecret(keyId, keyName) {
    try {
        const response = await fetch(`/api/keys/${keyId}/secret`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${sessionToken}`
            }
        });
        
        if (response.status === 401) {
            // Require re-authentication
            showReAuthModal(keyId, keyName);
            return;
        }
        
        if (response.ok) {
            const data = await response.json();
            showSecretModal(keyName, data.client_secret);
        } else {
            throw new Error('Failed to retrieve secret');
        }
    } catch (error) {
        alert('Error: ' + error.message);
    }
}
```

### Security Considerations

1. **Encryption Key Management**
   - Store encryption key securely (environment variable)
   - Consider key rotation strategy
   - Use strong encryption (AES-256 via Fernet)

2. **Access Controls**
   - Require recent authentication (5-minute window)
   - Log all secret access attempts
   - Rate limit secret viewing requests

3. **Migration Strategy**
   - Existing hashed secrets cannot be migrated
   - New secrets use encryption
   - Provide regeneration option for old keys

### Environment Variables

Add to `.env`:
```bash
# Client secret encryption key (generate once and keep secure)
CLIENT_SECRET_ENCRYPTION_KEY=your_generated_fernet_key_here
```

### Dependencies

Add to requirements.txt:
```
cryptography>=41.0.0
```

## Benefits
- Users can view their secrets without regeneration
- Maintains security through encryption
- Audit trail for secret access
- Backward compatible (with migration)

## Drawbacks
- More complex key management
- Slightly less secure than hashing (but still very secure)
- Requires careful encryption key handling
