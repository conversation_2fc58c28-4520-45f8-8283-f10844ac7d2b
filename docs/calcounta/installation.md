# Calcounta Installation Guide

## Prerequisites

- Docker and Docker Compose installed on your system
- Git (optional, for cloning the repository)

## Installation Steps

1. Clone the repository or download the source code:

```bash
git clone https://github.com/yourusername/calcounta.git
cd calcounta
```

2. Configure environment variables:

Copy the example environment file and modify it with your settings:

```bash
cp .env.example .env
```

Edit the `.env` file to set your port configuration, database credentials, and backend settings:

```
# Port Configuration
PORT=86  # Change this to use a different port

# PostgreSQL
POSTGRES_USER=calcounta_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=calcounta_db

# Backend
DATABASE_URL=********************************************************/calcounta_db
JWT_SECRET=your_jwt_secret_key
NODE_ENV=production
```

3. Build and start the Docker containers:

```bash
docker compose build
docker compose up -d
```

4. Access the application:

- Application UI: http://localhost:86
- API Endpoints: http://localhost:86/api

## Configuration Options

### AI Configuration

Calcounta uses client-side API configuration for food analysis. Users configure their own API keys and models through the application settings:

1. **OpenAI**: Supports GPT-4o, GPT-4o Mini, and other vision-capable models
2. **Google Gemini**: Supports Gemini 1.5 Pro, Gemini 1.5 Flash, and Gemini Pro Vision

API keys and model selection are managed through the user interface and stored securely in the database.

### Database

The application uses PostgreSQL for data storage. You can customize the database credentials in the `.env` file.

## Troubleshooting

### Container Startup Issues

If containers fail to start, check the logs:

```bash
docker compose logs
```

### Database Connection Issues

Ensure the database credentials in the `.env` file match those in the `docker-compose.yml` file.

### API Connection Issues

If the frontend cannot connect to the API, ensure the backend container is running and the ports are correctly mapped.

## Updating

To update to the latest version:

1. Pull the latest changes:

```bash
git pull
```

2. Rebuild and restart the containers:

```bash
docker compose down
docker compose build
docker compose up -d
```
