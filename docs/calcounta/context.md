### Project Overview

A cross-platform calorie-tracking UI delivered as Dockerized web and mobile-web applications. Users log meals via photo upload or camera capture (on supported devices); a local Ollama model (containerized alongside the UI) analyzes images to estimate calories and macronutrients. The app tracks daily intake against user-defined goals with an intuitive, responsive interface—all data stored and processed entirely on the user’s device or in local containers (no cloud services).

---

### Tech Stack

* **Framework:** Expo (React Native & React Native Web)
* **Language:** TypeScript
* **Routing:** React Router for web, Expo Router for mobile-web
* **UI Library:** Tailwind CSS (web) + styled-components (mobile)
* **Local Storage & Sync:**

  * Web: IndexedDB (via [idb-keyval](https://github.com/jakearchibald/idb-keyval))
  * Mobile-web: AsyncStorage
  * Shared: SQLite for structured data (using \[expo-sqlite])
* **Containerization:** Docker multi-stage builds for both web and mobile-web
* **Hosting:** Static file server (e.g., `serve`) inside each container

---

### Docker Setup

1. **Builder Stage**

   * Base: `node:18-alpine`
   * Install dependencies (`npm ci`)
   * Build web (`npm run build:web`) and mobile-web (`npm run build:web --config expo-web.config.js`)
2. **Runtime Stage**

   * Base: `nginx:alpine` or `node:18-alpine` + `serve`
   * Copy build artifacts (`/dist/web`, `/dist/mobile-web`)
   * Expose port `86`
   * CMD to start static server (`serve -s /dist/web`)

Environment variables only for local container configuration (e.g., Ollama endpoint); no external credentials or cloud keys.

---

### Configuration

* **Expo Web Config:** `app.web.json` overrides for public path, asset bundling
* **Local DB:** initialize SQLite schema at first run
* **Ollama or openai Endpoint:** ` curl -X POST "http://localhost:8000/analyze"   -H "accept: application/json"   -H "Content-Type: multipart/form-data"   -F "image=@34befdd9-a2cb-483c-bbb5-1c03c2935a01.jpg" ` in the same machine

---

### Core Features

#### 1. **User Onboarding & Goal Setup**

* Responsive multi-step form collects dietary goals and macronutrient preferences.
* Persisted locally in IndexedDB/AsyncStorage/SQLite.

#### 2. **Meal Logging & Photo Analysis**

* **Photo Input:** `<input type="file" accept="image/*" capture="environment" />` on web; Expo’s `ImagePicker` on mobile-web.
* **Ollama Analysis:** POST image Blob to local Ollama container; parse JSON response for calories, carbs, protein, fat.
* **Local Persistence:** Save each meal record in local DB immediately.

#### 3. **Daily Dashboard**

* **Summary Cards:** Total vs. remaining calories and macros.
* **Meal List:** Accordion-style logs with inline edit/delete.
* **Charts:** Recharts (web) and React Native SVG (mobile-web) for trends.

#### 4. **Offline-First & Local-Only**

* All data operations (CRUD on meals, goals, profiles) operate against local storage; no network required except for Ollama image analysis which runs in a local container.
* Sync queue isn’t needed—data writes directly to local DB and persists across sessions.

#### 5. **Settings & Profile**

* Modify goals or reset history.
* Profile and preferences stored locally.

---

### Navigation & Responsiveness

* **Web:** React Router v6 with browser history.
* **Mobile-Web:** Expo Router in hybrid mode.
* **Layout:** Flexible grid on desktop; single-column flex on narrow screens.
* **Safe Areas:** CSS env variables (`safe-area-inset-*`) on devices with notches.

---

### Performance & Testing

* **Web:** Code splitting with dynamic `import()`, lazy-load charts and heavy components.
* **Images:** Optimize during build with `sharp` to generate WebP.
* **Testing:**

  * Unit: Jest + React Testing Library
  * E2E: Playwright (web), Cypress Desktop + mobile-web

---

### Security & Deployment

* Serve all content over HTTPS via a local reverse-proxy container (e.g. Traefik or Nginx).
* Build images scanned for vulnerabilities (`docker scan`).
* CI/CD pipeline:

  1. Lint, type-check, test
  2. Build Docker images using "docker compose"
  3. Deploy container to local or on-prem environment

---


