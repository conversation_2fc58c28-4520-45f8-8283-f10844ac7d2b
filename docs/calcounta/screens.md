# Calcounta Screen Layouts

This document outlines the key screens and user interface components of the Calcounta application.

## Onboarding Flow

The onboarding process collects essential user information to personalize the experience and set initial goals.

### 1. Welcome Screen
- App logo and name
- Brief value proposition
- "Get Started" button

### 2. Gender Selection
- Options: Male, Female, Other
- Simple card-based selection

### 3. Workout Frequency
- Options: Never, 1-2 times/week, 3-4 times/week, 5+ times/week
- Visual selection cards

### 4. Height & Weight Input
- Numeric input fields with unit selection
- Visual representation of body type

### 5. Date of Birth
- Date picker component
- Age calculation display

### 6. Goal Selection
- Options: Lose weight, Maintain weight, Gain weight
- Visual cards with icons

### 7. Target Weight
- Numeric input with current weight reference
- Visual slider showing the difference

### 8. Goal Pace
- Options: Slow (0.25kg/week), Moderate (0.5kg/week), Fast (1kg/week)
- Timeline visualization

### 9. Diet Preferences
- Multiple selection: Vegetarian, Vegan, Keto, Paleo, etc.
- Checkboxes with icons

### 10. Goal Summary
- Calculated daily calorie target
- Macronutrient breakdown
- "Complete Setup" button

## Main Application Screens

### Home Screen
- Daily calorie summary (consumed vs. goal)
- Macronutrient progress rings (protein, carbs, fat)
- Recent meals list
- Quick-add meal button
- Water intake tracker
- Exercise summary

### Food Logging
- Search bar for food database
- Recent and favorite foods
- Camera/upload button for AI analysis
- Manual entry option
- Portion size selector
- Meal type categorization (breakfast, lunch, dinner, snack)

### Progress Screen
- Weight chart (over time)
- Calorie intake history (daily/weekly)
- Macronutrient distribution chart
- Measurements tracking
- Goal progress indicators

### Profile & Settings
- User information
- Goal adjustment
- AI provider configuration
- Units preference (metric/imperial)
- Data export options
- Account management

## Modal Components

### Add Food Modal
- Camera capture
- Photo upload
- Manual entry form
- Search food database

### Exercise Logging Modal
- Exercise type selection
- Duration input
- Intensity level
- Calorie calculation

### Water Logging Modal
- Quick-add buttons (100ml, 250ml, 500ml)
- Custom amount input
- Daily goal visualization

## Mobile Adaptations

The interface adapts responsively to mobile screens:

- Bottom navigation bar replaces side navigation
- Simplified charts and visualizations
- Touch-optimized input controls
- Full-screen modals for data entry
