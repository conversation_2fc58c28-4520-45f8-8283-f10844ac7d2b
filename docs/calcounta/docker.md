# Calcounta Docker Setup

Calcounta uses <PERSON><PERSON> and <PERSON>er Compose to containerize all components of the application, making it easy to deploy and run consistently across different environments.

## Container Architecture

The application consists of four main containers:

1. **Frontend**: React web application
2. **Backend**: Node.js Express API server
3. **Database**: PostgreSQL database
4. **AI Worker**: Optional service for processing AI tasks

## Docker Compose Configuration

The `docker-compose.yml` file defines the services, networks, and volumes for the application:

```yaml
version: '3.8'

services:
  backend:
    container_name: calcounta-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    # No external ports exposed - only accessible within Docker network
    env_file:
      - .env
    depends_on:
      - db
    volumes:
      - ./backend:/app
    restart: unless-stopped

  frontend:
    container_name: calcounta-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${PORT}:80"  # Expose frontend on the configured port externally
    env_file:
      - .env
    volumes:
      - ./frontend:/app
    depends_on:
      - backend
    restart: unless-stopped

  db:
    image: postgres:15
    container_name: calcounta-db
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    # No external ports exposed - only accessible within Docker network

  ai-worker:
    container_name: calcounta-ai-worker
    build:
      context: ./worker
      dockerfile: Dockerfile
    env_file:
      - .env
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
```

## Dockerfiles

### Frontend Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine AS build

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Backend Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .

EXPOSE 8000

CMD ["npm", "start"]
```

### AI Worker Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .

CMD ["npm", "run", "worker"]
```

## Environment Variables

The application uses a `.env` file to configure the containers:

```
# Port Configuration
PORT=86  # Change this to use a different port

# PostgreSQL
POSTGRES_USER=calcounta_user
POSTGRES_PASSWORD=securepassword
POSTGRES_DB=calcounta_db

# Backend
DATABASE_URL=**************************************************/calcounta_db
JWT_SECRET=supersecretkey
NODE_ENV=production
```

## Building and Running

To build and start the containers:

```bash
docker compose build
docker compose up -d
```

To stop the containers:

```bash
docker compose down
```

## Data Persistence

The PostgreSQL database data is stored in a Docker volume (`postgres_data`), ensuring that data persists even when containers are stopped or removed.

## Networking

The containers communicate with each other using Docker's internal networking:

- Frontend can access the backend at `http://backend:8000`
- Backend can access the database at `postgresql://db:5432`
- AI Worker can access the backend at `http://backend:8000`

## Port Mapping

- Application UI: Accessible at `http://localhost:86`
- API Endpoints: Accessible at `http://localhost:86/api`
- Database: Not directly accessible from outside the Docker network (for security)
