# Calcounta Documentation

Welcome to the Calcounta documentation. This guide provides comprehensive information about the Calcounta application, its architecture, setup, and usage.

## Table of Contents

1. [Overview](./overview.md)
2. [Installation Guide](./installation.md)
3. [Database Schema](./database.md)
4. [API Documentation](./api.md)
5. [Screen Layouts](./screens.md)
6. [Docker Setup](./docker.md)

## Quick Start

To get started with Calcounta:

1. Follow the [Installation Guide](./installation.md) to set up the application
2. Configure your environment variables in the `.env` file
3. Build and run the Docker containers with `docker compose build` and `docker compose up -d`
4. Access the application at http://localhost:3000

## Project Structure

```
calcounta/
├── backend/           # Node.js Express API server
├── frontend/          # React web application
├── worker/            # AI processing service
├── docs/              # Documentation
│   └── calcounta/     # Application documentation
├── docker-compose.yml # Docker Compose configuration
└── .env               # Environment variables
```

## Contributing

If you'd like to contribute to Calcounta, please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

Calcounta is licensed under the MIT License. See the LICENSE file for details.
