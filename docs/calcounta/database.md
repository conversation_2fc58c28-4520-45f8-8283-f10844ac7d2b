# Calcounta Database Schema

Calcounta uses PostgreSQL as its primary database. The schema is designed to efficiently store user data, food logs, and tracking information.

## Database Tables

### users

Stores user account information and profile data.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| email | VARCHAR | User's email address (unique) |
| password_hash | VARCHAR | Hashed password |
| gender | VARCHAR | User's gender (male/female/other) |
| dob | DATE | Date of birth |
| height_cm | FLOAT | Height in centimeters |
| weight_kg | FLOAT | Current weight in kilograms |
| goal_type | VARCHAR | Weight goal (lose/gain/maintain) |
| target_weight | FLOAT | Target weight in kilograms |
| activity_level | VARCHAR | Activity level (sedentary/light/moderate/active/very_active) |
| created_at | TIMESTAMP | Account creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### goals

Tracks user weight goals and progress.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| start_date | DATE | Goal start date |
| end_date | DATE | Goal end date |
| target_change | FLOAT | Target weight change in kg |
| pace | FLOAT | Desired pace (kg/week) |
| daily_calorie_goal | INTEGER | Calculated daily calorie goal |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### foods

Stores food items that can be reused across meal logs.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id (null for public foods) |
| name | VARCHAR | Food name |
| calories | INTEGER | Calories per 100g |
| protein_g | FLOAT | Protein in grams per 100g |
| carbs_g | FLOAT | Carbohydrates in grams per 100g |
| fats_g | FLOAT | Fats in grams per 100g |
| image_url | VARCHAR | Optional URL to food image |
| source | VARCHAR | Source of food data (manual/database/scan) |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### meals_logged

Records individual meal entries.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| food_id | UUID | Foreign key to foods.id (optional) |
| name | VARCHAR | Meal name (if food_id not provided) |
| calories | INTEGER | Total calories for this serving |
| protein_g | FLOAT | Protein in grams for this serving |
| carbs_g | FLOAT | Carbohydrates in grams for this serving |
| fats_g | FLOAT | Fats in grams for this serving |
| serving_size_g | FLOAT | Serving size in grams |
| image_url | VARCHAR | Optional URL to meal image |
| timestamp | TIMESTAMP | When the meal was consumed |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### exercises

Tracks exercise and physical activity.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| type | VARCHAR | Exercise type |
| description | TEXT | Exercise description |
| calories_burned | INTEGER | Estimated calories burned |
| duration_minutes | INTEGER | Exercise duration in minutes |
| timestamp | TIMESTAMP | When the exercise was performed |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### water_logs

Tracks water consumption.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| ml_consumed | INTEGER | Water consumed in milliliters |
| timestamp | TIMESTAMP | When the water was consumed |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### weight_logs

Tracks weight measurements over time.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| weight_kg | FLOAT | Weight in kilograms |
| timestamp | TIMESTAMP | When the weight was recorded |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

## Relationships

- A user can have multiple goals, meals, exercises, water logs, and weight logs
- A user can create custom foods
- A meal can reference a food item (optional)
