# Calcounta - AI-Powered Calorie Tracking App

## Project Overview

Calcounta is a cross-platform calorie-tracking application delivered as Dockerized web and mobile-web applications. Users can log meals via photo upload or camera capture (on supported devices). The app uses AI to analyze images and estimate calories and macronutrients. It tracks daily intake against user-defined goals with an intuitive, responsive interface.

## Key Features

* **AI-based Food Analysis**: Upload food photos for automatic calorie and macronutrient estimation
* **Personalized Goal Setting**: Set weight goals (gain, lose, maintain) with customized pace
* **Daily Tracking**: Monitor calories, macronutrients, water intake, and exercise
* **Progress Visualization**: Charts and statistics to track progress over time
* **Self-hosted**: All data stored locally in PostgreSQL database
* **Flexible AI Integration**: Support for both OpenAI and local Ollama models

## Tech Stack

* **Frontend**: React with TypeScript
  * UI Library: Tailwind CSS
  * Routing: React Router
  * State Management: React Context API

* **Backend**: Node.js with Express
  * Database: PostgreSQL
  * ORM: Prisma
  * Authentication: JWT

* **AI Integration**:
  * OpenAI API (configurable)
  * Ollama (local model, configurable)

* **Containerization**:
  * Docker multi-stage builds
  * Docker Compose for orchestration

## System Architecture

The application follows a standard three-tier architecture:

1. **Frontend**: React-based web application
2. **Backend API**: Express.js REST API
3. **Database**: PostgreSQL for data persistence

All components are containerized using Docker, making deployment simple and consistent across environments.

## Getting Started

See the [installation guide](./installation.md) for setup instructions.

## API Documentation

See the [API documentation](./api.md) for details on available endpoints.
