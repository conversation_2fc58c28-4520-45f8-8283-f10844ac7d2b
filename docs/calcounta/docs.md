# Calcounta App Documentation

## Table of Contents

* [app.md](#appmd)
* [database.md](#databasemd)
* [system-architecture.md](#system-architecturemd)
* [screens.md](#screensmd)

---

## app.md

**App Name:** Calcounta

**Objective:**
Calcounta is a self-hosted AI-powered health and fitness app for tracking nutrition, caloric intake, exercise, water consumption, and weight management. Users set personalized goals (gain, lose, maintain weight) and the app generates insights using OpenAI or Gemini API integrations.

**Features:**

* AI-based goal planning
* Onboarding with personalized metrics
* Daily calorie/macronutrient tracking
* Exercise logging
* BMI and progress tracking
* Water intake tracking
* Saved foods and food database
* Scan food via vision model
* API flexibility (OpenAI, ollama)

**Tech Stack:**

* Frontend: React Native or Flutter
* Backend: Node.js (Express) or FastAPI (Python)
* Database: PostgreSQL
* ML/AI API Integration: OpenAI GPT-4 / Gemini Pro
* Cloud Vision: Google Cloud Vision or Amazon Rekognition
* Auth: Firebase Auth or OAuth2
* Docker: Self-hosted containerized deployment

---

## database.md

**Tables:**

1. **users**

* id (UUID)
* email
* password\_hash
* gender
* dob
* height\_cm
* weight\_kg
* goal\_type (lose/gain/maintain)
* target\_weight
* activity\_level

2. **goals**

* id
* user\_id (FK)
* start\_date
* end\_date
* target\_change (kg)
* pace (kg/week)

3. **foods**

* id
* user\_id (FK/null for public foods)
* name
* calories
* protein\_g
* carbs\_g
* fats\_g
* image\_url
* source (manual/database/scan)

4. **meals\_logged**

* id
* user\_id (FK)
* food\_id (FK)
* timestamp
* serving\_size\_g
* total\_calories

5. **exercises**

* id
* user\_id (FK)
* type
* description
* calories\_burned
* timestamp

6. **water\_logs**

* id
* user\_id (FK)
* ml\_consumed
* timestamp

7. **weight\_logs**

* id
* user\_id (FK)
* weight\_kg
* timestamp

---

## system-architecture.md

**Frontend (React Native / Flutter)**

* Onboarding screens
* Tabbed UI: Home | Progress | Settings
* Local state management (Redux / Provider)
* API calls to backend

**Backend (Node.js / FastAPI)**

* RESTful API endpoints
* Authentication middleware
* AI services handler (OpenAI, Gemini)
* Image upload + processing (Cloud Vision API)
* ORM (Prisma or SQLAlchemy) to interact with PostgreSQL

**AI/ML Integration**

* OpenAI / Gemini API for:

  * Goal suggestions
  * Nutrition insights
  * Progress feedback
* Cloud Vision API:

  * OCR on food packages
  * Food recognition from images

**Docker Setup**

* Dockerfile
* docker-compose.yaml (for app + db)
* Volumes for data persistence

**Services**

* api: Express/FastAPI server
* frontend: React Native Web/Expo or Flutter Web (preview)
* db: PostgreSQL
* worker: AI task processor (if needed)

---

## screens.md

### Onboarding Screens

1. Welcome
2. Gender selection
3. Workout frequency
4. Referral source
5. Value proposition
6. Height & weight input
7. Date of birth
8. Goal type
9. Desired weight
10. Goal pace
11. Encouragement
12. Obstacle selector
13. Accomplishment goals
14. Diet preferences
15. Goal projection
16. Completion summary

### Home Screen

* Daily calorie goal
* Macronutrient rings
* Recently logged food
* Header day-selector (scrollable)
* Water tracker
* Step count & exercise burn
* Floating + button for logging

### Progress Screen

* Last weight
* Days logged
* Line graph for progress
* Bar chart for calorie macros
* BMI widget with range status

### Add (+) Button Modal

* Log Exercise
* Saved Foods
* Food Database
* Scan Food

### Log Exercise Screen

* Run / Cardio
* Weight Lifting
* Describe (text input)
* Manual entry (kcal burned)

### Settings Screen

* Profile management
* API key config (OpenAI/ollama)
* Units (Metric/Imperial)
* Notifications

---


the api server which takes in image and process it and outputs data is already running and can be accessed using:  curl -X POST "http://localhost:8000/analyze?provider=openai"   -H "accept: application/json"   -H "Content-Type: multipart/form-data"   -F "image=@34befdd9-a2cb-483c-bbb5-1c03c2935a01.jpg"