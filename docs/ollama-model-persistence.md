# Ollama Model Persistence Guide

This guide explains how Calcounta implements persistent model storage for Ollama, ensuring that downloaded models survive container restarts and are not re-downloaded unnecessarily.

## Overview

The Calcounta Ollama setup includes several features to ensure efficient model management:

1. **Persistent Volume Storage** - Models are stored in Docker volumes that persist across container restarts
2. **Smart Model Detection** - The system checks for existing models before attempting downloads
3. **Resume Capability** - Partial downloads can be resumed if interrupted
4. **Model Verification** - Downloaded models are verified to ensure they're working correctly

## How It Works

### Volume Persistence

The Ollama container uses a host directory bind mount to persist model data:

```yaml
volumes:
  - ./ollama-models:/root/.ollama
```

This host directory stores:
- Downloaded model files (`./ollama-models/models/`)
- Model manifests and metadata
- Download status tracking (`./ollama-models/download-status/`)

The bind mount provides direct access to model files from the host filesystem, making it easier to:
- Backup and restore models
- Monitor disk usage
- Manually manage model files
- Share models between different deployments

### Model Detection Logic

Before downloading any model, the system performs multiple checks:

1. **Registry Check**: Uses `ollama list` to see if the model is registered
2. **Tag Matching**: Handles different model tag formats (e.g., `model:tag` vs `model`)
3. **Filesystem Check**: Verifies model files exist on disk
4. **Corruption Detection**: Identifies partially downloaded or corrupted models

### Download Process

When a model needs to be downloaded:

1. **Pre-check**: Verify the model doesn't already exist
2. **Resume Detection**: Check for partial downloads and resume if possible
3. **Progress Tracking**: Monitor download progress with detailed logging
4. **Post-verification**: Ensure the downloaded model is functional
5. **Status Persistence**: Save download state for future reference

## Configuration

### Environment Variables

Set these in your `.env` file:

```bash
# Comma-separated list of models to download
OLLAMA_MODELS=gemma3:4b-it-qat,llama2:7b,codellama:13b

# Ollama service configuration
OLLAMA_KEEP_ALIVE=24h
OLLAMA_MAX_LOADED_MODELS=4
OLLAMA_FLASH_ATTENTION=1
```

**Note**: The `OLLAMA_MODELS` environment variable in your `.env` file specifies which models to download automatically. This is different from Ollama's built-in `OLLAMA_MODELS` environment variable, which specifies the models directory path.

### Model Specification

Models can be specified in several formats:

- `model_name` - Downloads the default/latest tag
- `model_name:tag` - Downloads a specific tag
- `model_name:size` - Downloads a specific size variant

Examples:
- `llama2` - Downloads llama2:latest
- `llama2:7b` - Downloads the 7B parameter version
- `codellama:13b-instruct` - Downloads the 13B instruct variant

## Verification

### Manual Verification

To verify that model persistence is working:

1. **Check current models**:
   ```bash
   docker exec calcounta-ollama ollama list
   ```

2. **Restart the container**:
   ```bash
   docker-compose restart ollama
   ```

3. **Check models again** (should be the same):
   ```bash
   docker exec calcounta-ollama ollama list
   ```

### Automated Verification

Use the provided verification script:

```bash
./scripts/verify-model-persistence.sh
```

This script will:
- Check volume mounts
- Verify model directory structure
- Test Ollama service connectivity
- Optionally test persistence by restarting the container

## Troubleshooting

### Models Being Re-downloaded

If models are being re-downloaded on every restart:

1. **Check volume mounts**:
   ```bash
   docker inspect calcounta-ollama --format '{{range .Mounts}}{{.Source}}->{{.Destination}}{{end}}'
   ```

2. **Verify volume exists**:
   ```bash
   docker volume ls | grep ollama_data
   ```

3. **Check model directory**:
   ```bash
   docker exec calcounta-ollama ls -la /root/.ollama/models/
   ```

### Slow Startup

If the container takes a long time to start:

1. **Check logs**:
   ```bash
   docker-compose logs ollama
   ```

2. **Monitor initialization**:
   ```bash
   docker-compose logs -f ollama
   ```

3. **Increase timeout** (if needed, modify `docker-compose.yml`):
   ```yaml
   # In the ollama service command, increase sleep time
   sleep 60  # Instead of sleep 30
   ```

### Corrupted Models

If models appear corrupted or don't work:

1. **Remove the problematic model**:
   ```bash
   docker exec calcounta-ollama ollama rm model_name
   ```

2. **Clear download status**:
   ```bash
   docker exec calcounta-ollama rm -f /root/.ollama/download-status/model_name.state
   ```

3. **Restart container** to trigger re-download:
   ```bash
   docker-compose restart ollama
   ```

### Complete Reset

To completely reset all models and start fresh:

1. **Stop services**:
   ```bash
   docker-compose down
   ```

2. **Remove the volume**:
   ```bash
   docker volume rm calcounta_ollama_data
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

## Best Practices

### Model Selection

- **Start small**: Begin with smaller models (7B parameters or less) for testing
- **Consider hardware**: Larger models require more RAM and storage
- **Use specific tags**: Specify exact model versions for reproducibility

### Storage Management

- **Monitor disk space**: Large models can consume significant storage
- **Regular cleanup**: Remove unused models with `ollama rm model_name`
- **Backup volumes**: Consider backing up the `ollama_data` volume for important setups

### Performance Optimization

- **Keep models loaded**: Set `OLLAMA_KEEP_ALIVE=24h` to avoid reload delays
- **Limit concurrent models**: Set `OLLAMA_MAX_LOADED_MODELS` based on available RAM
- **Use SSD storage**: Store Docker volumes on fast storage for better performance

## Advanced Configuration

### Host Directory Configuration

The default configuration uses a host directory bind mount:

1. **Default configuration** (already set):
   ```yaml
   volumes:
     - ./ollama-models:/root/.ollama
   ```

2. **Custom directory** (if you want to change the location):
   ```yaml
   volumes:
     - /path/to/your/custom/models:/root/.ollama
   ```

3. **Directory setup is automatic**:
   - The `scripts/setup-ollama-directory.sh` script creates the directory
   - Proper permissions are set automatically
   - Directory structure is created during startup

4. **Manual setup** (if needed):
   ```bash
   mkdir -p ./ollama-models/models
   mkdir -p ./ollama-models/download-status
   chmod -R 755 ./ollama-models
   ```

### Migration from Docker Volume

If you're upgrading from a previous version that used Docker named volumes:

1. **Stop the services**:
   ```bash
   docker-compose down
   ```

2. **Copy existing models** (if you have any):
   ```bash
   # Create the new directory
   mkdir -p ./ollama-models

   # Copy from the old volume (if it exists)
   docker run --rm \
     -v calcounta_ollama_data:/source \
     -v $(pwd)/ollama-models:/dest \
     alpine cp -r /source/. /dest/
   ```

3. **Start with new configuration**:
   ```bash
   docker-compose up -d
   ```

4. **Verify migration**:
   ```bash
   docker exec calcounta-ollama ollama list
   ```

### Network Storage

For shared model storage across multiple instances:

1. **Use NFS or similar**:
   ```yaml
   volumes:
     - type: nfs
       source: nfs-server:/path/to/models
       target: /root/.ollama
   ```

2. **Consider performance implications** of network storage

## Monitoring

### Log Analysis

Monitor model download progress:

```bash
# Follow logs during startup
docker-compose logs -f ollama

# Check for specific patterns
docker-compose logs ollama | grep "Model already exists"
docker-compose logs ollama | grep "Successfully downloaded"
```

### Status Tracking

Check download status files:

```bash
# List status files
docker exec calcounta-ollama ls -la /root/.ollama/download-status/

# Check specific model status
docker exec calcounta-ollama cat /root/.ollama/download-status/model_name.state
```

This persistence system ensures that your Ollama models are downloaded once and reused efficiently across container restarts, saving time and bandwidth while providing a reliable AI service.
