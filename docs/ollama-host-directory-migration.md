# Ollama Host Directory Migration

This document explains the migration from Docker named volumes to host directory bind mounts for Ollama model storage.

## Overview

The Ollama service configuration has been updated to use a host directory bind mount instead of a Docker named volume. This change provides several benefits:

- **Direct access** to model files from the host filesystem
- **Easier backup and restore** of models
- **Better disk space monitoring** and management
- **Simplified model sharing** between deployments
- **No dependency on Docker volume management**

## Changes Made

### 1. Docker Compose Configuration

**Before:**
```yaml
volumes:
  - ollama_data:/root/.ollama
```

**After:**
```yaml
volumes:
  - ./ollama-models:/root/.ollama
```

The `ollama_data` named volume has been removed from the volumes section.

### 2. Directory Structure

The new host directory structure:
```
./ollama-models/
├── models/              # Downloaded AI model files
├── download-status/     # Download progress tracking
├── logs/               # Service logs (if any)
├── .gitignore          # Git ignore rules
└── README.md           # Documentation
```

### 3. Automatic Setup

- **Directory creation**: Automatically created during startup
- **Permission handling**: Proper ownership and permissions set
- **Git integration**: Directory excluded from version control
- **Documentation**: README and .gitignore included

### 4. Scripts Added

- `scripts/setup-ollama-directory.sh` - Creates and configures the host directory
- `scripts/migrate-ollama-volume.sh` - Migrates existing models from Docker volumes

### 5. Updated Documentation

- `docs/ollama-model-persistence.md` - Updated with new configuration
- `docs/ollama-configuration.md` - Reflects host directory usage
- Migration instructions added

## Migration Process

### For New Installations

No action required - the directory will be created automatically when you start the services.

### For Existing Installations

1. **Automatic migration** (recommended):
   ```bash
   ./scripts/migrate-ollama-volume.sh
   ```

2. **Manual migration**:
   ```bash
   # Stop services
   docker-compose down
   
   # Create directory
   ./scripts/setup-ollama-directory.sh
   
   # Copy existing models (if any)
   docker run --rm \
     -v calcounta_ollama_data:/source \
     -v $(pwd)/ollama-models:/dest \
     alpine cp -r /source/. /dest/
   
   # Start services
   docker-compose up -d
   ```

## Benefits

### 1. Direct File Access
```bash
# View model files directly
ls -la ./ollama-models/models/

# Check disk usage
du -sh ./ollama-models/

# Backup models
tar -czf ollama-models-backup.tar.gz ./ollama-models/
```

### 2. Easier Management
```bash
# Remove specific model files (when service is stopped)
rm -rf ./ollama-models/models/specific-model/

# Copy models between deployments
rsync -av ./ollama-models/ /path/to/other/deployment/ollama-models/
```

### 3. Better Monitoring
```bash
# Monitor disk usage
watch du -sh ./ollama-models/

# Check download progress
tail -f ./ollama-models/download-status/*.state
```

## Verification

After migration, verify everything is working:

1. **Check directory structure**:
   ```bash
   ls -la ./ollama-models/
   ```

2. **Start services**:
   ```bash
   docker-compose up -d
   ```

3. **Verify models**:
   ```bash
   docker exec calcounta-ollama ollama list
   ```

4. **Test functionality**:
   ```bash
   # Test model download
   docker exec calcounta-ollama ollama pull llama2
   
   # Verify file appears on host
   ls -la ./ollama-models/models/
   ```

## Troubleshooting

### Permission Issues
```bash
# Fix ownership
sudo chown -R $(id -u):$(id -g) ./ollama-models/

# Fix permissions
chmod -R 755 ./ollama-models/
```

### Directory Not Created
```bash
# Run setup script manually
./scripts/setup-ollama-directory.sh
```

### Models Not Appearing
```bash
# Check container logs
docker logs calcounta-ollama

# Verify mount
docker inspect calcounta-ollama | grep -A 10 Mounts
```

## Cleanup

After successful migration, you can optionally remove the old Docker volume:

```bash
# List volumes
docker volume ls

# Remove old volume (only after verifying migration)
docker volume rm calcounta_ollama_data
```

## Rollback (if needed)

To rollback to Docker volumes:

1. Stop services: `docker-compose down`
2. Edit `docker-compose.yml`:
   - Change `./ollama-models:/root/.ollama` back to `ollama_data:/root/.ollama`
   - Add `ollama_data:` to volumes section
3. Start services: `docker-compose up -d`

## Support

If you encounter issues:

1. Check the logs: `docker logs calcounta-ollama`
2. Verify permissions: `ls -la ./ollama-models/`
3. Run verification script: `./scripts/verify-model-persistence.sh`
4. Consult the updated documentation in `docs/ollama-model-persistence.md`
