// Test script to verify frontend authentication flow
console.log('=== Frontend Authentication Test ===');

// Function to simulate login and test API dashboard
async function testAuthFlow() {
    try {
        console.log('1. Testing login...');
        
        // Simulate login
        const loginResponse = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'sudhamoy'
            })
        });
        
        if (!loginResponse.ok) {
            throw new Error(`Login failed: ${loginResponse.status}`);
        }
        
        const loginData = await loginResponse.json();
        console.log('✅ Login successful');
        console.log('Token preview:', loginData.token.substring(0, 20) + '...');
        
        // Store in localStorage (simulating what the app does)
        localStorage.setItem('token', loginData.token);
        localStorage.setItem('userId', loginData.id);
        
        console.log('2. Testing API keys endpoint...');
        
        // Test API keys endpoint
        const apiKeysResponse = await fetch('/api/keys/ollama/keys', {
            headers: {
                'Authorization': `Bearer ${loginData.token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!apiKeysResponse.ok) {
            throw new Error(`API keys request failed: ${apiKeysResponse.status}`);
        }
        
        const apiKeysData = await apiKeysResponse.json();
        console.log('✅ API keys endpoint working');
        console.log('API keys count:', apiKeysData.api_keys?.length || 0);
        
        console.log('3. Testing authentication state...');
        console.log('Token in localStorage:', !!localStorage.getItem('token'));
        console.log('User ID in localStorage:', !!localStorage.getItem('userId'));
        
        console.log('🎉 All tests passed! The API dashboard should work now.');
        console.log('Try refreshing the page or navigating to /api-dashboard');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testAuthFlow();
