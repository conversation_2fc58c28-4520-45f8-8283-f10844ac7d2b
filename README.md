# Calcounta - AI-Powered Calorie Tracking App

Calcounta is a cross-platform calorie-tracking application delivered as Dockerized web and mobile-web applications. Users can log meals via photo upload or camera capture (on supported devices). The app uses AI to analyze images and estimate calories and macronutrients. It tracks daily intake against user-defined goals with an intuitive, responsive interface.

## Features

* **AI-based Food Analysis**: Upload food photos for automatic calorie and macronutrient estimation
* **Personalized Goal Setting**: Set weight goals (gain, lose, maintain) with customized pace
* **Daily Tracking**: Monitor calories, macronutrients, water intake, and exercise
* **Progress Visualization**: Charts and statistics to track progress over time
* **Self-hosted**: All data stored locally in PostgreSQL database
* **Flexible AI Integration**: Support for both OpenAI and local Ollama models

## Tech Stack

* **Frontend**: React with TypeScript
  * UI Library: Tailwind CSS
  * Routing: React Router
  * State Management: React Context API

* **Backend**: Node.js with Express
  * Database: PostgreSQL
  * ORM: Prisma
  * Authentication: JWT

* **AI Integration**:
  * OpenAI API (configurable)
  * Ollama (local model, configurable)

* **Containerization**:
  * Docker multi-stage builds
  * Docker Compose for orchestration

## Getting Started

### Prerequisites

- Docker and Docker Compose installed on your system
- Git (optional, for cloning the repository)

### Installation

1. Clone the repository or download the source code:

```bash
git clone https://github.com/yourusername/calcounta.git
cd calcounta
```

2. Configure environment variables:

Copy the example environment file and modify it with your settings:

```bash
cp .env.example .env
```

Edit the `.env` file to set your database credentials and backend settings.

**Configure Base URL and Services**

- Set `BASE_URL` to your deployment domain:
  - `http://localhost` for local-only access
  - `http://YOUR_IP_ADDRESS` for network access (run `./scripts/detect-ip.sh` to find your IP)
  - `https://yourdomain.com` for production
- Set `PORT` for the main application (e.g., `86`)
- Set `OLLAMA_GATEWAY_PORT` for the API dashboard (e.g., `85`)
- Set `OLLAMA_MODELS` to automatically download AI models (e.g., `llama2,codellama`)

> **Note**: Ollama services are included by default. If you don't want them, see the "Removing Ollama Services" section below.

3. Build and start the Docker containers:

```bash
# Recommended: Use the startup script (includes validation)
./scripts/start.sh

# Optional: Validate configuration only
./scripts/validate-env.sh

# Optional: Detect your machine's IP for network access
./scripts/detect-ip.sh

# Or manually with Docker Compose
docker compose build
docker compose up -d
```

4. Access the application:

- Application UI: {BASE_URL}:{PORT} (as configured in your .env)
- API Endpoints: {BASE_URL}:{PORT}/api (as configured in your .env)
- API Dashboard: {BASE_URL}:{OLLAMA_GATEWAY_PORT}/dashboard (Ollama services included by default)

## Removing Ollama Services (Optional)

Ollama services are included by default to provide AI capabilities. If you don't need them, you can manually remove them from `docker-compose.yml`:

**Services to remove:**
- `ollama` - The main Ollama service
- `ollama-db` - PostgreSQL database for Ollama
- `ollama-gateway` - Nginx reverse proxy
- `ollama-auth-service` - Authentication service
- `ollama-proxy` - API proxy service
- `ollama-dashboard` - Web dashboard
- `redis` - Session storage and rate limiting

**Steps:**
1. Edit `docker-compose.yml`
2. Remove the above service definitions
3. Remove the corresponding volumes: `ollama_data`, `ollama_postgres_data`, `redis_data`
4. Remove Ollama-related environment variables from `.env`

After removal, the frontend will automatically hide the API Dashboard menu item.

## Ollama Model Persistence

Calcounta includes advanced model persistence features to ensure efficient use of downloaded AI models:

### Key Features

- **Persistent Storage**: Models are stored in Docker volumes that survive container restarts
- **Smart Detection**: Automatically detects existing models before attempting downloads
- **Resume Capability**: Interrupted downloads can be resumed from where they left off
- **Model Verification**: Downloaded models are tested to ensure they work correctly

### Verification Tools

Use these scripts to verify and test model persistence:

```bash
# Verify persistence setup
./scripts/verify-model-persistence.sh

# Test persistence by restarting containers
./scripts/test-model-persistence.sh
```

### Configuration

Set models to download automatically in your `.env` file:

```bash
# Comma-separated list of models
OLLAMA_MODELS=gemma3:4b-it-qat,llama2:7b,codellama:13b
```

For detailed information, see the [Ollama Model Persistence Guide](docs/ollama-model-persistence.md).

## Documentation

Detailed documentation is available in the `docs/calcounta` directory:

- [Overview](docs/calcounta/overview.md)
- [Installation Guide](docs/calcounta/installation.md)
- [API Documentation](docs/calcounta/api.md)
- [Database Schema](docs/calcounta/database.md)
- [Screen Layouts](docs/calcounta/screens.md)
- [Docker Setup](docs/calcounta/docker.md)
- [Ollama Configuration](docs/ollama-configuration.md)
- [Ollama Model Persistence](docs/ollama-model-persistence.md)
- [Deployment Examples](docs/deployment-examples.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
