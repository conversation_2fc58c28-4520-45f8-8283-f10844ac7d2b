# Port Configuration
PORT=86

# Base URL Configuration
# Set the base URL for your deployment (without port)
# Examples:
#   http://localhost (local development)
#   http://************* (network access using your machine's IP)
#   https://yourdomain.com (production)
BASE_URL=http://localhost

# Ollama Configuration
# Comma-separated list of models to download automatically
# Examples: llama2, codellama, mistral, llama2:13b, codellama:7b
# Leave empty to skip automatic model downloads
OLLAMA_MODELS=llama2

# PostgreSQL
POSTGRES_USER=calcounta_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=calcounta_db

# Backend
DATABASE_URL=********************************************************/calcounta_db
JWT_SECRET=your_jwt_secret_key
NODE_ENV=production

# Ollama Services Configuration
# PostgreSQL (Ollama API Database)
OLLAMA_POSTGRES_USER=ollama_user
OLLAMA_POSTGRES_PASSWORD=ollama_pass
OLLAMA_POSTGRES_DB=ollama_db

# Ollama Gateway Configuration
OLLAMA_GATEWAY_PORT=85

# Redis Configuration
REDIS_PASSWORD=your_redis_password

# Ollama JWT Configuration
OLLAMA_JWT_SECRET_KEY=your_ollama_jwt_secret_key
OLLAMA_JWT_ALGORITHM=HS256
OLLAMA_JWT_EXPIRATION_HOURS=24

# Ollama API Configuration
OLLAMA_API_KEY_LENGTH=32
OLLAMA_CLIENT_SECRET_LENGTH=64

# Ollama Rate Limiting
OLLAMA_RATE_LIMIT_PER_MINUTE=60
OLLAMA_RATE_LIMIT_PER_HOUR=1000
OLLAMA_RATE_LIMIT_PER_DAY=10000

# Ollama Security
OLLAMA_BCRYPT_ROUNDS=12

# Ollama Logging
OLLAMA_LOG_LEVEL=INFO

# Ollama Environment
OLLAMA_ENVIRONMENT=development
OLLAMA_DEBUG=true

# CORS Configuration for Ollama API
# Comma-separated list of allowed origins for CORS requests
# Examples: http://localhost:86,http://127.0.0.1:86,http://*************:86
CORS_ORIGINS=http://localhost:86,http://127.0.0.1:86
