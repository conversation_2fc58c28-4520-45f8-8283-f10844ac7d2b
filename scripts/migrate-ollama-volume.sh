#!/bin/bash

# Ollama Volume to Host Directory Migration Script
# This script migrates existing Ollama models from Docker named volume to host directory

set -e

echo "🔄 Ollama Volume Migration Script"
echo "================================="
echo ""
echo "This script will migrate your Ollama models from a Docker named volume"
echo "to a host directory bind mount for easier access and management."
echo ""

# Get the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
OLLAMA_DIR="$PROJECT_ROOT/ollama-models"
VOLUME_NAME="calcounta_ollama_data"

echo "🔍 Project root: $PROJECT_ROOT"
echo "📁 Target directory: $OLLAMA_DIR"
echo "📦 Source volume: $VOLUME_NAME"
echo ""

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if the volume exists
if ! docker volume inspect "$VOLUME_NAME" >/dev/null 2>&1; then
    echo "ℹ️  Docker volume '$VOLUME_NAME' not found."
    echo "   This is normal if you're setting up for the first time."
    echo "   No migration needed - proceeding with fresh setup."
    
    # Run the directory setup script
    if [ -f "$PROJECT_ROOT/scripts/setup-ollama-directory.sh" ]; then
        echo ""
        echo "📁 Setting up fresh Ollama directory..."
        "$PROJECT_ROOT/scripts/setup-ollama-directory.sh"
    fi
    exit 0
fi

echo "✅ Found existing Docker volume: $VOLUME_NAME"

# Check if target directory already exists
if [ -d "$OLLAMA_DIR" ]; then
    echo "⚠️  Target directory already exists: $OLLAMA_DIR"
    echo ""
    echo "Options:"
    echo "1. Backup existing directory and proceed with migration"
    echo "2. Skip migration (keep existing directory)"
    echo "3. Cancel migration"
    echo ""
    read -p "Choose option (1/2/3): " choice
    
    case $choice in
        1)
            BACKUP_DIR="${OLLAMA_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
            echo "📦 Creating backup: $BACKUP_DIR"
            mv "$OLLAMA_DIR" "$BACKUP_DIR"
            echo "✅ Backup created successfully"
            ;;
        2)
            echo "ℹ️  Keeping existing directory. Migration skipped."
            exit 0
            ;;
        3)
            echo "❌ Migration cancelled by user."
            exit 0
            ;;
        *)
            echo "❌ Invalid choice. Migration cancelled."
            exit 1
            ;;
    esac
fi

# Stop services if running
echo ""
echo "🛑 Stopping Calcounta services..."
if docker-compose ps | grep -q "Up"; then
    docker-compose down
    echo "✅ Services stopped"
else
    echo "ℹ️  Services were not running"
fi

# Create target directory
echo ""
echo "📁 Creating target directory..."
mkdir -p "$OLLAMA_DIR"

# Perform the migration
echo ""
echo "🔄 Migrating models from Docker volume to host directory..."
echo "   This may take several minutes depending on the size of your models..."
echo ""

# Use a temporary container to copy data
docker run --rm \
    -v "$VOLUME_NAME":/source \
    -v "$OLLAMA_DIR":/dest \
    alpine sh -c "
        echo '📋 Source volume contents:' &&
        ls -la /source/ &&
        echo '' &&
        echo '🔄 Copying files...' &&
        cp -r /source/. /dest/ &&
        echo '✅ Copy completed' &&
        echo '' &&
        echo '📋 Destination directory contents:' &&
        ls -la /dest/
    "

# Set proper permissions
echo ""
echo "🔧 Setting proper permissions..."
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)
chown -R "$CURRENT_UID:$CURRENT_GID" "$OLLAMA_DIR"
chmod -R 755 "$OLLAMA_DIR"

# Create .gitignore and README if they don't exist
if [ ! -f "$OLLAMA_DIR/.gitignore" ]; then
    echo "📝 Creating .gitignore..."
    cat > "$OLLAMA_DIR/.gitignore" << 'EOF'
# Ollama model files and data
models/
download-status/
logs/
*.log

# Keep the directory structure but ignore the contents
!.gitignore
!README.md
EOF
fi

if [ ! -f "$OLLAMA_DIR/README.md" ]; then
    echo "📝 Creating README..."
    cat > "$OLLAMA_DIR/README.md" << 'EOF'
# Ollama Models Directory

This directory stores Ollama AI models and related data for the Calcounta application.

## Directory Structure

- `models/` - Downloaded AI model files and blobs
- `download-status/` - Download progress and status tracking
- `logs/` - Ollama service logs (if any)

## Important Notes

- This directory is mounted as a bind mount in the Ollama Docker container
- Model files can be quite large (several GB each)
- The directory is excluded from git via .gitignore
- Models persist across container restarts and rebuilds
- You can safely backup this directory to preserve downloaded models

## Management

- View downloaded models: `docker exec calcounta-ollama ollama list`
- Remove a model: `docker exec calcounta-ollama ollama rm <model-name>`
- Check disk usage: `du -sh ./models/`
EOF
fi

# Display migration results
echo ""
echo "📊 Migration Results"
echo "==================="
echo "📁 Target directory: $OLLAMA_DIR"
echo "📏 Directory size: $(du -sh "$OLLAMA_DIR" 2>/dev/null | cut -f1 || echo "unknown")"
echo "👤 Owner: $(ls -ld "$OLLAMA_DIR" | awk '{print $3":"$4}')"
echo "🔧 Permissions: $(ls -ld "$OLLAMA_DIR" | awk '{print $1}')"

# Count migrated files
if [ -d "$OLLAMA_DIR/models" ]; then
    MODEL_FILES=$(find "$OLLAMA_DIR/models" -type f 2>/dev/null | wc -l)
    echo "📦 Model files migrated: $MODEL_FILES"
fi

echo ""
echo "✅ Migration completed successfully!"
echo ""
echo "💡 Next steps:"
echo "   1. Start services: docker-compose up -d"
echo "   2. Verify models: docker exec calcounta-ollama ollama list"
echo "   3. Test functionality with your applications"
echo ""
echo "🗑️  Optional cleanup:"
echo "   After verifying everything works, you can remove the old volume:"
echo "   docker volume rm $VOLUME_NAME"
echo ""
echo "🔍 Useful commands:"
echo "   • Check models: docker exec calcounta-ollama ollama list"
echo "   • Monitor logs: docker logs -f calcounta-ollama"
echo "   • Directory size: du -sh $OLLAMA_DIR"
