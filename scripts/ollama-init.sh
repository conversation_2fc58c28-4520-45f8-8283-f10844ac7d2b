#!/bin/bash

# Ollama Model Download Script
# This script downloads specified models when the Ollama container starts

echo "🤖 Ollama Model Initialization"
echo "=============================="

# Status file for tracking downloads
STATUS_DIR="/tmp/ollama-status"
PERSISTENT_STATUS_DIR="/root/.ollama/download-status"
mkdir -p "$STATUS_DIR"
mkdir -p "$PERSISTENT_STATUS_DIR"

# Function to save download state persistently
save_download_state() {
    local model_name="$1"
    local state="$2"  # "downloading", "completed", "failed"
    echo "$state" > "$PERSISTENT_STATUS_DIR/${model_name}.state"
}

# Function to get persistent download state
get_download_state() {
    local model_name="$1"
    if [ -f "$PERSISTENT_STATUS_DIR/${model_name}.state" ]; then
        cat "$PERSISTENT_STATUS_DIR/${model_name}.state"
    else
        echo "not_started"
    fi
}

# Function to update download status
update_download_status() {
    local model_name="$1"
    local status="$2"
    local progress="$3"

    # Clean status text by removing special characters and control characters
    local clean_status=$(echo "$status" | tr -d '\000-\037\177' | sed 's/[▕▏█]//g' | sed 's/\[K//g' | sed 's/\[?[0-9]*[a-zA-Z]//g' | sed 's/\[[0-9]*[A-Z]//g' | sed 's/  */ /g' | sed 's/^ *//;s/ *$//')

    cat > "$STATUS_DIR/${model_name}.json" << EOF
{
    "name": "$model_name",
    "status": "$clean_status",
    "progress": $progress,
    "timestamp": "$(date -Iseconds)"
}
EOF
}

# Function to clear download status
clear_download_status() {
    local model_name="$1"
    rm -f "$STATUS_DIR/${model_name}.json"
    echo "🧹 Cleared download status for: $model_name"
}

# Function to check if a model exists locally
model_exists() {
    local model_name="$1"

    # First, check if ollama list shows the model
    if ollama list 2>/dev/null | grep -q "^$model_name"; then
        echo "🔍 Model found in Ollama registry: $model_name"
        return 0
    fi

    # Also check for models with different tag formats (e.g., model:tag vs model)
    # Extract base model name (before colon) and tag (after colon)
    local base_name="${model_name%%:*}"
    local tag="${model_name#*:}"

    # If no tag specified, check for any version of the model
    if [ "$base_name" = "$tag" ]; then
        if ollama list 2>/dev/null | grep -q "^$base_name:"; then
            echo "🔍 Found variant of model: $base_name (checking for any tag)"
            return 0
        fi
    else
        # Check if base model exists with different tag
        if ollama list 2>/dev/null | grep -q "^$base_name:"; then
            echo "🔍 Found base model: $base_name (may have different tag)"
            # Check if the exact tag exists
            if ollama list 2>/dev/null | grep -q "^$model_name"; then
                echo "🔍 Exact model found: $model_name"
                return 0
            else
                echo "⚠️  Base model $base_name exists but not the specific tag: $tag"
                return 1
            fi
        fi
    fi

    # Additional filesystem check for model blobs
    if [ -d "/root/.ollama/models" ]; then
        local manifest_count=$(find /root/.ollama/models -name "*manifest*" 2>/dev/null | wc -l)
        local blob_count=$(find /root/.ollama/models/blobs -name "sha256-*" 2>/dev/null | wc -l)

        if [ "$manifest_count" -gt 0 ] && [ "$blob_count" -gt 0 ]; then
            echo "🔍 Found model files on disk (manifests: $manifest_count, blobs: $blob_count)"
            echo "   But model not registered in Ollama. This might indicate a corrupted installation."
            echo "   Proceeding with download to ensure model is properly registered."
        fi
    fi

    return 1
}

# Function to check if a model is partially downloaded
model_partially_downloaded() {
    local model_name="$1"
    # Check if there are any blob files for this model in the ollama directory
    if [ -d "/root/.ollama/models/blobs" ]; then
        # Count blob files (partial downloads create blob files)
        local blob_count=$(find /root/.ollama/models/blobs -name "sha256-*" 2>/dev/null | wc -l)
        if [ "$blob_count" -gt 0 ]; then
            return 0  # Partial download exists
        fi
    fi
    return 1  # No partial download
}

# Function to get model size info
get_model_info() {
    local model_name="$1"
    ollama list | grep "^$model_name" | awk '{print $2, $3}'
}

# Check if OLLAMA_MODEL is set and not empty
if [ -z "$OLLAMA_MODEL" ] || [ "$OLLAMA_MODEL" = "" ]; then
    echo "ℹ️  No models specified in OLLAMA_MODEL, skipping downloads"
    echo ""
    echo "📋 Checking for existing models..."
    if ollama list | grep -q "NAME"; then
        echo "✅ Found existing models:"
        ollama list
    else
        echo "ℹ️  No models currently installed"
    fi
    exit 0
fi

echo "📦 Models to download: $OLLAMA_MODEL"

# Wait for Ollama service to be ready
echo "⏳ Waiting for Ollama service to be ready..."
max_attempts=60  # Increased timeout for better reliability
attempt=0

while [ $attempt -lt $max_attempts ]; do
    # Test multiple endpoints to ensure Ollama is fully ready
    if ollama list >/dev/null 2>&1 && ollama ps >/dev/null 2>&1; then
        echo "✅ Ollama service is ready and responsive"

        # Additional check: ensure the models directory is properly mounted and accessible
        if [ -d "/root/.ollama/models" ]; then
            echo "✅ Models directory is accessible: /root/.ollama/models (host bind mount)"
            ls -la /root/.ollama/models/ 2>/dev/null || echo "   (Directory is empty - this is normal for first run)"
        else
            echo "⚠️  Models directory not found, creating it..."
            mkdir -p /root/.ollama/models
            mkdir -p /root/.ollama/download-status
            chmod -R 755 /root/.ollama
        fi

        # Verify host bind mount is working
        echo "🔍 Verifying host bind mount functionality..."
        touch /root/.ollama/.mount-test 2>/dev/null && rm -f /root/.ollama/.mount-test 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "✅ Host bind mount is working correctly"
        else
            echo "⚠️  Host bind mount may have permission issues"
        fi

        # Give Ollama a moment to fully initialize its model registry
        echo "⏳ Allowing Ollama to fully initialize model registry..."
        sleep 5
        break
    fi

    attempt=$((attempt + 1))
    if [ $((attempt % 10)) -eq 0 ]; then
        echo "   Still waiting... Attempt $attempt/$max_attempts (this may take a while on first startup)"
    else
        echo "   Attempt $attempt/$max_attempts..."
    fi
    sleep 3  # Slightly longer sleep for better stability
done

if [ $attempt -eq $max_attempts ]; then
    echo "❌ Ollama service failed to start within timeout"
    echo "🔍 Debugging information:"
    echo "   Checking if Ollama process is running..."
    ps aux | grep ollama || echo "   No Ollama process found"
    echo "   Checking Ollama directory..."
    ls -la /root/.ollama/ || echo "   Ollama directory not accessible"
    echo "   Checking network connectivity..."
    curl -s http://localhost:11434/api/version || echo "   Ollama API not responding"
    exit 1
fi

# First, show existing models
echo ""
echo "📋 Checking existing models..."
if ollama list | grep -q "NAME"; then
    echo "✅ Currently installed models:"
    ollama list
else
    echo "ℹ️  No models currently installed"
fi

# Parse and download models
echo ""
echo "🚀 Processing model list..."

# Convert comma-separated list to array
IFS=',' read -ra MODELS <<< "$OLLAMA_MODEL"

# Track download results
successful_downloads=0
failed_downloads=0
skipped_downloads=0

for model in "${MODELS[@]}"; do
    # Trim whitespace
    model=$(echo "$model" | xargs)

    if [ -n "$model" ]; then
        echo ""
        echo "🔍 Checking model: $model"

        if model_exists "$model"; then
            local info=$(get_model_info "$model")
            echo "✅ Model already exists: $model ($info)"
            echo "   Skipping download..."
            save_download_state "$model" "completed"
            skipped_downloads=$((skipped_downloads + 1))
        else
            local download_state=$(get_download_state "$model")

            # Check for partial downloads
            if model_partially_downloaded "$model" || [ "$download_state" = "downloading" ]; then
                echo "🔄 Resuming download for: $model (state: $download_state)"
                echo "   Ollama will continue from where it left off..."
            else
                echo "📥 Starting fresh download: $model"
            fi

            save_download_state "$model" "downloading"
            echo "   🔄 This may take several minutes depending on model size..."
            echo "   📊 Progress will be shown below:"
            echo "   💾 Downloads are persistent - restarts will resume from last position"
            echo ""

            # Initialize download status
            update_download_status "$model" "Starting download..." 0

            # Use ollama pull with verbose output to show progress
            echo "🔄 Starting download for model: $model"
            echo "   This will be saved to: /root/.ollama/models/ (host bind mount)"
            echo "   Host location: ./ollama-models/models/"
            echo "   Progress will be shown below:"
            echo ""

            if ollama pull "$model" 2>&1 | while IFS= read -r line; do
                # Add timestamp and formatting to progress lines
                timestamp=$(date '+%H:%M:%S')

                # Extract progress percentage if available
                progress=0
                if [[ "$line" =~ ([0-9]+)% ]]; then
                    progress="${BASH_REMATCH[1]}"
                fi

                # Update status for every line that contains progress info
                if [[ "$line" == *"pulling"* ]] || [[ "$line" == *"downloading"* ]] || [[ "$line" == *"%"* ]]; then
                    echo "   [$timestamp] 📊 $line"
                    update_download_status "$model" "Downloading: $line" "$progress"
                elif [[ "$line" == *"success"* ]] || [[ "$line" == *"complete"* ]]; then
                    echo "   [$timestamp] ✅ $line"
                    update_download_status "$model" "Completed" 100
                elif [[ "$line" == *"error"* ]] || [[ "$line" == *"failed"* ]]; then
                    echo "   [$timestamp] ❌ $line"
                    update_download_status "$model" "Error: $line" 0
                elif [[ "$line" == *"verifying"* ]] || [[ "$line" == *"writing"* ]]; then
                    echo "   [$timestamp] 🔄 $line"
                    update_download_status "$model" "Finalizing: $line" "$progress"
                else
                    echo "   [$timestamp] ℹ️  $line"
                    # Update status for any line with progress info
                    if [ "$progress" -gt 0 ] || [[ "$line" == *"MB"* ]] || [[ "$line" == *"GB"* ]]; then
                        update_download_status "$model" "$line" "$progress"
                    fi
                fi
            done; then
                echo ""
                echo "✅ Download completed for: $model"

                # Verify the model is actually usable
                echo "🔍 Verifying model installation..."
                sleep 2  # Give Ollama time to register the model

                if model_exists "$model"; then
                    local new_info=$(get_model_info "$model")
                    echo "✅ Model verified and ready: $model"
                    echo "   📋 Model info: $new_info"

                    # Test that the model can be loaded (optional quick test)
                    echo "🧪 Testing model responsiveness..."
                    if timeout 30 ollama run "$model" "Hello" >/dev/null 2>&1; then
                        echo "✅ Model responds correctly to test query"
                    else
                        echo "⚠️  Model downloaded but may not be fully functional (this is often normal)"
                    fi

                    save_download_state "$model" "completed"
                    clear_download_status "$model"
                    successful_downloads=$((successful_downloads + 1))
                else
                    echo "❌ Model download completed but verification failed: $model"
                    echo "   The model may not be properly registered. Marking as failed."
                    save_download_state "$model" "failed"
                    update_download_status "$model" "Download completed but verification failed" 0
                    failed_downloads=$((failed_downloads + 1))
                fi
            else
                echo ""
                echo "❌ Failed to download: $model"
                echo "   Check your internet connection and verify the model name"
                save_download_state "$model" "failed"
                update_download_status "$model" "Download failed" 0
                failed_downloads=$((failed_downloads + 1))
            fi
        fi
    fi
done

echo ""
echo "📊 Download Summary:"
echo "   ✅ Successful downloads: $successful_downloads"
echo "   ⏭️  Skipped (already exists): $skipped_downloads"
echo "   ❌ Failed downloads: $failed_downloads"
echo "   📦 Total models processed: $((successful_downloads + skipped_downloads + failed_downloads))"

if [ $failed_downloads -eq 0 ]; then
    if [ $successful_downloads -gt 0 ]; then
        echo "🎉 All new models downloaded successfully!"
    else
        echo "✅ All requested models were already available!"
    fi
else
    echo "⚠️  Some models failed to download. Check your internet connection and model names."
fi

echo ""
echo "🔍 Final model inventory:"
echo "========================"
if ollama list | grep -q "NAME"; then
    ollama list

    # Count total models
    model_count=$(ollama list | grep -v "NAME" | grep -v "^$" | wc -l)
    echo ""
    echo "📈 Total models available: $model_count"
else
    echo "ℹ️  No models currently installed"
fi

echo ""
echo "✅ Ollama initialization complete"
echo "🚀 Ollama service is ready for use!"

# Show some helpful information
echo ""
echo "💡 Helpful commands:"
echo "   • List models: ollama list"
echo "   • Run a model: ollama run <model-name>"
echo "   • Remove a model: ollama rm <model-name>"
echo "   • Show model info: ollama show <model-name>"
