#!/bin/bash

# Ollama Host Directory Setup Script
# This script creates and configures the host directory for Ollama model storage

set -e

echo "📁 Setting up Ollama host directory..."
echo "====================================="

# Get the project root directory (parent of scripts directory)
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
OLLAMA_DIR="$PROJECT_ROOT/ollama-models"

echo "🔍 Project root: $PROJECT_ROOT"
echo "📁 Ollama directory: $OLLAMA_DIR"

# Create the ollama-models directory if it doesn't exist
if [ ! -d "$OLLAMA_DIR" ]; then
    echo "📁 Creating ollama-models directory..."
    mkdir -p "$OLLAMA_DIR"
    echo "✅ Directory created: $OLLAMA_DIR"
else
    echo "✅ Directory already exists: $OLLAMA_DIR"
fi

# Create subdirectories that Ollama expects
echo "📁 Creating Ollama subdirectories..."
mkdir -p "$OLLAMA_DIR/models"
mkdir -p "$OLLAMA_DIR/download-status"
mkdir -p "$OLLAMA_DIR/logs"

# Set appropriate permissions
echo "🔧 Setting directory permissions..."
chmod -R 755 "$OLLAMA_DIR"

# Get current user info
CURRENT_USER=$(whoami)
CURRENT_UID=$(id -u)
CURRENT_GID=$(id -g)

echo "👤 Current user: $CURRENT_USER (UID: $CURRENT_UID, GID: $CURRENT_GID)"

# Set ownership to current user
echo "🔧 Setting directory ownership..."
chown -R "$CURRENT_UID:$CURRENT_GID" "$OLLAMA_DIR"

# Create a .gitignore file to exclude model files from git
echo "📝 Creating .gitignore for model files..."
cat > "$OLLAMA_DIR/.gitignore" << 'EOF'
# Ollama model files and data
models/
download-status/
logs/
*.log

# Keep the directory structure but ignore the contents
!.gitignore
!README.md
EOF

# Create a README file explaining the directory
echo "📝 Creating README for ollama-models directory..."
cat > "$OLLAMA_DIR/README.md" << 'EOF'
# Ollama Models Directory

This directory stores Ollama AI models and related data for the Calcounta application.

## Directory Structure

- `models/` - Downloaded AI model files and blobs
- `download-status/` - Download progress and status tracking
- `logs/` - Ollama service logs (if any)

## Important Notes

- This directory is mounted as a bind mount in the Ollama Docker container
- Model files can be quite large (several GB each)
- The directory is excluded from git via .gitignore
- Models persist across container restarts and rebuilds
- You can safely backup this directory to preserve downloaded models

## Management

- View downloaded models: `docker exec calcounta-ollama ollama list`
- Remove a model: `docker exec calcounta-ollama ollama rm <model-name>`
- Check disk usage: `du -sh ./models/`

## Migration from Docker Volume

If you're migrating from a Docker named volume, you can copy existing models:

```bash
# Stop the services
docker-compose down

# Copy from the old volume (if it exists)
docker run --rm -v calcounta_ollama_data:/source -v $(pwd)/ollama-models:/dest alpine cp -r /source/. /dest/

# Start services with new bind mount
docker-compose up -d
```
EOF

# Display directory information
echo ""
echo "📊 Directory setup complete!"
echo "================================"
echo "📁 Location: $OLLAMA_DIR"
echo "📏 Current size: $(du -sh "$OLLAMA_DIR" 2>/dev/null | cut -f1 || echo "0B")"
echo "👤 Owner: $(ls -ld "$OLLAMA_DIR" | awk '{print $3":"$4}')"
echo "🔧 Permissions: $(ls -ld "$OLLAMA_DIR" | awk '{print $1}')"

# List contents
echo ""
echo "📋 Directory contents:"
ls -la "$OLLAMA_DIR" || echo "   (Directory is empty)"

echo ""
echo "✅ Ollama directory setup complete!"
echo ""
echo "💡 Next steps:"
echo "   1. Run 'docker-compose up -d' to start services"
echo "   2. Models will be downloaded to: $OLLAMA_DIR/models/"
echo "   3. Check progress with: docker logs calcounta-ollama"
echo ""
echo "🔍 Useful commands:"
echo "   • Check models: docker exec calcounta-ollama ollama list"
echo "   • Monitor logs: docker logs -f calcounta-ollama"
echo "   • Directory size: du -sh $OLLAMA_DIR"
