#!/bin/bash

# Test script to demonstrate Ollama model persistence
# This script shows how models persist across container restarts

set -e

echo "🧪 Ollama Model Persistence Test"
echo "================================"

CONTAINER_NAME="calcounta-ollama"
TEST_MODEL="gemma3:4b-it-qat"  # Use the model from .env

# Function to check if container is running
check_container() {
    if ! docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        echo "❌ Container $CONTAINER_NAME is not running"
        echo "   Please start it with: docker-compose up -d ollama"
        exit 1
    fi
}

# Function to wait for Ollama to be ready
wait_for_ollama() {
    echo "⏳ Waiting for Ollama service to be ready..."
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if docker exec $CONTAINER_NAME ollama list >/dev/null 2>&1; then
            echo "✅ Ollama service is ready"
            return 0
        fi
        
        attempt=$((attempt + 1))
        echo "   Attempt $attempt/$max_attempts..."
        sleep 2
    done
    
    echo "❌ Ollama service failed to become ready"
    return 1
}

# Function to list models
list_models() {
    echo "📋 Current models:"
    docker exec $CONTAINER_NAME ollama list || echo "   No models found"
}

# Function to check if a specific model exists
model_exists() {
    local model="$1"
    docker exec $CONTAINER_NAME ollama list | grep -q "^$model"
}

echo "Step 1: Initial setup check"
echo "============================"

check_container
wait_for_ollama

echo ""
echo "Step 2: Check current model state"
echo "================================="

list_models

echo ""
echo "Step 3: Ensure test model is available"
echo "======================================"

if model_exists "$TEST_MODEL"; then
    echo "✅ Test model '$TEST_MODEL' is already available"
else
    echo "📥 Test model '$TEST_MODEL' not found. This test requires the model to be downloaded first."
    echo "   The model should be automatically downloaded based on your OLLAMA_MODELS setting."
    echo "   If not, you can manually download it with:"
    echo "   docker exec $CONTAINER_NAME ollama pull $TEST_MODEL"
    echo ""
    read -p "Do you want to download the test model now? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📥 Downloading test model..."
        docker exec $CONTAINER_NAME ollama pull "$TEST_MODEL"
        echo "✅ Test model downloaded"
    else
        echo "⏭️  Skipping model download. Test will continue with existing models."
        # Use the first available model for testing
        AVAILABLE_MODEL=$(docker exec $CONTAINER_NAME ollama list | grep -v "NAME" | head -1 | awk '{print $1}')
        if [ -n "$AVAILABLE_MODEL" ]; then
            TEST_MODEL="$AVAILABLE_MODEL"
            echo "   Using existing model for test: $TEST_MODEL"
        else
            echo "❌ No models available for testing. Please download a model first."
            exit 1
        fi
    fi
fi

echo ""
echo "Step 4: Record current state"
echo "============================"

echo "📊 Recording current state before restart..."
MODELS_BEFORE=$(docker exec $CONTAINER_NAME ollama list)
MODEL_COUNT_BEFORE=$(echo "$MODELS_BEFORE" | grep -v "NAME" | grep -v "^$" | wc -l)

echo "   Models before restart: $MODEL_COUNT_BEFORE"
echo "$MODELS_BEFORE"

# Also check the filesystem
echo ""
echo "📁 Checking model files on disk..."
BLOB_COUNT_BEFORE=$(docker exec $CONTAINER_NAME find /root/.ollama/models/blobs -name "sha256-*" 2>/dev/null | wc -l || echo "0")
echo "   Model blob files: $BLOB_COUNT_BEFORE"

echo ""
echo "Step 5: Test model functionality"
echo "================================"

echo "🧪 Testing model '$TEST_MODEL' before restart..."
if timeout 30 docker exec $CONTAINER_NAME ollama run "$TEST_MODEL" "Hello, respond with just 'OK'" 2>/dev/null | grep -q "OK"; then
    echo "✅ Model responds correctly before restart"
else
    echo "⚠️  Model test failed or timed out (this may be normal for large models)"
fi

echo ""
echo "Step 6: Restart container"
echo "========================="

echo "🔄 Restarting Ollama container to test persistence..."
docker-compose restart ollama

echo "⏳ Waiting for container to restart and initialize..."
sleep 10

check_container
wait_for_ollama

echo ""
echo "Step 7: Verify persistence"
echo "=========================="

echo "📊 Checking state after restart..."
MODELS_AFTER=$(docker exec $CONTAINER_NAME ollama list)
MODEL_COUNT_AFTER=$(echo "$MODELS_AFTER" | grep -v "NAME" | grep -v "^$" | wc -l)

echo "   Models after restart: $MODEL_COUNT_AFTER"
echo "$MODELS_AFTER"

# Check filesystem again
echo ""
echo "📁 Checking model files on disk after restart..."
BLOB_COUNT_AFTER=$(docker exec $CONTAINER_NAME find /root/.ollama/models/blobs -name "sha256-*" 2>/dev/null | wc -l || echo "0")
echo "   Model blob files: $BLOB_COUNT_AFTER"

echo ""
echo "Step 8: Test model functionality after restart"
echo "=============================================="

echo "🧪 Testing model '$TEST_MODEL' after restart..."
if timeout 30 docker exec $CONTAINER_NAME ollama run "$TEST_MODEL" "Hello, respond with just 'OK'" 2>/dev/null | grep -q "OK"; then
    echo "✅ Model responds correctly after restart"
else
    echo "⚠️  Model test failed or timed out (this may be normal for large models)"
fi

echo ""
echo "Step 9: Results Analysis"
echo "======================="

echo "📊 Persistence Test Results:"
echo "   Models before restart: $MODEL_COUNT_BEFORE"
echo "   Models after restart:  $MODEL_COUNT_AFTER"
echo "   Blob files before:     $BLOB_COUNT_BEFORE"
echo "   Blob files after:      $BLOB_COUNT_AFTER"

if [ "$MODEL_COUNT_BEFORE" -eq "$MODEL_COUNT_AFTER" ] && [ "$BLOB_COUNT_BEFORE" -eq "$BLOB_COUNT_AFTER" ]; then
    echo ""
    echo "🎉 PERSISTENCE TEST PASSED!"
    echo "   ✅ Model count remained the same"
    echo "   ✅ Model files remained on disk"
    echo "   ✅ Models are properly persisted across restarts"
    
    # Additional verification
    if echo "$MODELS_BEFORE" | grep -q "$TEST_MODEL" && echo "$MODELS_AFTER" | grep -q "$TEST_MODEL"; then
        echo "   ✅ Test model '$TEST_MODEL' survived restart"
    fi
    
elif [ "$MODEL_COUNT_AFTER" -gt "$MODEL_COUNT_BEFORE" ]; then
    echo ""
    echo "ℹ️  PERSISTENCE TEST PASSED (with additions)"
    echo "   ✅ Original models were preserved"
    echo "   ℹ️  Additional models were downloaded (this is normal)"
    echo "   ✅ Persistence is working correctly"
    
else
    echo ""
    echo "❌ PERSISTENCE TEST FAILED!"
    echo "   ❌ Model count decreased or blob files were lost"
    echo "   ❌ Models may not be properly persisted"
    echo ""
    echo "🔍 Troubleshooting suggestions:"
    echo "   1. Check volume mounts: docker inspect $CONTAINER_NAME"
    echo "   2. Verify volume exists: docker volume ls | grep ollama"
    echo "   3. Check logs: docker-compose logs ollama"
    echo "   4. Run verification script: ./scripts/verify-model-persistence.sh"
fi

echo ""
echo "📋 Test Summary:"
echo "   Container: $CONTAINER_NAME"
echo "   Test model: $TEST_MODEL"
echo "   Volume: calcounta_ollama_data"
echo "   Data directory: /root/.ollama"
echo ""
echo "💡 To run this test again: ./scripts/test-model-persistence.sh"
echo "🔍 For detailed verification: ./scripts/verify-model-persistence.sh"
echo ""
echo "✅ Persistence test complete!"
