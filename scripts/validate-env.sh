#!/bin/bash

# Calcounta Environment Validation Script
# This script validates your .env configuration before starting services

set -e

echo "🔍 Validating Calcounta Environment Configuration..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found"
    echo "   Please create one from .env.example:"
    echo "   cp .env.example .env"
    exit 1
fi

echo "✅ .env file found"

# Source the .env file
export $(grep -v '^#' .env | grep -v '^$' | xargs)

# Track validation status
VALIDATION_FAILED=false

# Function to validate required variable
validate_required() {
    local var_name=$1
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo "❌ $var_name is not set or empty"
        VALIDATION_FAILED=true
    else
        echo "✅ $var_name = $var_value"
    fi
}

# Function to validate conditional variable
validate_conditional() {
    local var_name=$1
    local condition=$2
    local var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo "❌ $var_name is required when $condition"
        VALIDATION_FAILED=true
    else
        echo "✅ $var_name = $var_value"
    fi
}

echo ""
echo "📋 Validating Required Variables:"
echo "--------------------------------"

# Validate core required variables
validate_required "BASE_URL"
validate_required "PORT"

# Validate database variables
validate_required "POSTGRES_USER"
validate_required "POSTGRES_PASSWORD"
validate_required "POSTGRES_DB"
validate_required "DATABASE_URL"
validate_required "JWT_SECRET"

echo ""
echo "🔧 Validating Ollama Configuration:"
echo "-----------------------------------"

echo "ℹ️  Ollama services are included by default, validating Ollama variables..."

# Validate Ollama-specific variables (all required since Ollama is included)
validate_required "OLLAMA_GATEWAY_PORT"
validate_required "OLLAMA_POSTGRES_USER"
validate_required "OLLAMA_POSTGRES_PASSWORD"
validate_required "OLLAMA_POSTGRES_DB"
validate_required "REDIS_PASSWORD"
validate_required "OLLAMA_JWT_SECRET_KEY"

echo ""
echo "🌐 Validating URL Configuration:"
echo "-------------------------------"

# Validate BASE_URL format
if [[ "$BASE_URL" =~ ^https?:// ]]; then
    echo "✅ BASE_URL has valid protocol"

    # Check for 0.0.0.0 which is not valid for client URLs
    if [[ "$BASE_URL" == *"0.0.0.0"* ]]; then
        echo "⚠️  WARNING: 0.0.0.0 is not a valid client URL!"
        echo "   0.0.0.0 is only for server binding, not client access"
        echo "   Use 'localhost' for local access or your machine's IP for network access"
        echo "   Run './scripts/detect-ip.sh' for suggestions"
        VALIDATION_FAILED=true
    fi
else
    echo "❌ BASE_URL must start with http:// or https://"
    VALIDATION_FAILED=true
fi

# Validate PORT is numeric
if [[ "$PORT" =~ ^[0-9]+$ ]]; then
    echo "✅ PORT is numeric"
    
    # Check if port is in valid range
    if [ "$PORT" -ge 1 ] && [ "$PORT" -le 65535 ]; then
        echo "✅ PORT is in valid range (1-65535)"
    else
        echo "❌ PORT must be between 1 and 65535"
        VALIDATION_FAILED=true
    fi
else
    echo "❌ PORT must be numeric"
    VALIDATION_FAILED=true
fi

# Validate OLLAMA_GATEWAY_PORT
if [ -n "$OLLAMA_GATEWAY_PORT" ]; then
    if [[ "$OLLAMA_GATEWAY_PORT" =~ ^[0-9]+$ ]]; then
        echo "✅ OLLAMA_GATEWAY_PORT is numeric"
        
        if [ "$OLLAMA_GATEWAY_PORT" -ge 1 ] && [ "$OLLAMA_GATEWAY_PORT" -le 65535 ]; then
            echo "✅ OLLAMA_GATEWAY_PORT is in valid range"
        else
            echo "❌ OLLAMA_GATEWAY_PORT must be between 1 and 65535"
            VALIDATION_FAILED=true
        fi
        
        # Check for port conflicts
        if [ "$PORT" = "$OLLAMA_GATEWAY_PORT" ]; then
            echo "❌ PORT and OLLAMA_GATEWAY_PORT cannot be the same"
            VALIDATION_FAILED=true
        else
            echo "✅ No port conflicts detected"
        fi
    else
        echo "❌ OLLAMA_GATEWAY_PORT must be numeric"
        VALIDATION_FAILED=true
    fi
fi

echo ""
echo "📊 Configuration Summary:"
echo "------------------------"

if [ "$VALIDATION_FAILED" = true ]; then
    echo "❌ Validation FAILED"
    echo ""
    echo "Please fix the errors above and run this script again."
    echo "You can also refer to .env.example for the correct format."
    exit 1
else
    echo "✅ Validation PASSED"
    echo ""
    echo "🎯 Your configuration:"
    echo "• Main Application: $BASE_URL:$PORT"
    echo "• API Dashboard: $BASE_URL:$OLLAMA_GATEWAY_PORT/dashboard"
    echo "• Ollama Services: INCLUDED"
    
    echo ""
    echo "🚀 Ready to start Calcounta!"
    echo "   Run: ./scripts/start.sh"
fi
