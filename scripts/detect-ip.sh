#!/bin/bash

# Calcounta IP Detection Script
# This script helps detect your machine's IP address for network access

echo "🔍 Detecting Network Configuration..."
echo ""

# Function to detect primary network interface IP
get_primary_ip() {
    # Try different methods to get the primary IP
    
    # Method 1: Using ip route (Linux)
    if command -v ip >/dev/null 2>&1; then
        IP=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
        if [ -n "$IP" ] && [ "$IP" != "127.0.0.1" ]; then
            echo "$IP"
            return
        fi
    fi
    
    # Method 2: Using route (macOS/BSD)
    if command -v route >/dev/null 2>&1; then
        INTERFACE=$(route get default 2>/dev/null | grep interface | awk '{print $2}')
        if [ -n "$INTERFACE" ]; then
            IP=$(ifconfig "$INTERFACE" 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1)
            if [ -n "$IP" ]; then
                echo "$IP"
                return
            fi
        fi
    fi
    
    # Method 3: Using hostname (fallback)
    if command -v hostname >/dev/null 2>&1; then
        IP=$(hostname -I 2>/dev/null | awk '{print $1}')
        if [ -n "$IP" ] && [ "$IP" != "127.0.0.1" ]; then
            echo "$IP"
            return
        fi
    fi
    
    # Method 4: Parse ifconfig output
    if command -v ifconfig >/dev/null 2>&1; then
        IP=$(ifconfig 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | head -1 | awk '{print $2}' | sed 's/addr://')
        if [ -n "$IP" ]; then
            echo "$IP"
            return
        fi
    fi
    
    # If all methods fail
    echo ""
}

# Get the primary IP
PRIMARY_IP=$(get_primary_ip)

echo "📋 Network Information:"
echo "----------------------"

if [ -n "$PRIMARY_IP" ]; then
    echo "✅ Primary IP Address: $PRIMARY_IP"
    echo ""
    echo "🌐 Suggested BASE_URL configurations:"
    echo ""
    echo "For LOCAL access only:"
    echo "  BASE_URL=http://localhost"
    echo ""
    echo "For NETWORK access (other devices can connect):"
    echo "  BASE_URL=http://$PRIMARY_IP"
    echo ""
    echo "📱 With network access, you can use these URLs:"
    echo "  • From this machine: http://localhost:86 or http://$PRIMARY_IP:86"
    echo "  • From other devices: http://$PRIMARY_IP:86"
    echo "  • From mobile devices: http://$PRIMARY_IP:86"
else
    echo "⚠️  Could not detect primary IP address"
    echo ""
    echo "🔧 Manual detection:"
    echo "  Run: ip addr show (Linux) or ifconfig (macOS)"
    echo "  Look for your network interface IP (usually starts with 192.168.x.x or 10.x.x.x)"
    echo ""
    echo "🌐 Suggested BASE_URL configurations:"
    echo ""
    echo "For LOCAL access only:"
    echo "  BASE_URL=http://localhost"
    echo ""
    echo "For NETWORK access:"
    echo "  BASE_URL=http://YOUR_IP_ADDRESS"
    echo "  (Replace YOUR_IP_ADDRESS with your actual IP)"
fi

echo ""
echo "🔧 How to update your configuration:"
echo "-----------------------------------"
echo "1. Edit your .env file:"
echo "   nano .env"
echo ""
echo "2. Update the BASE_URL line with your chosen option"
echo ""
echo "3. Validate your configuration:"
echo "   ./scripts/validate-env.sh"
echo ""
echo "4. Start Calcounta:"
echo "   ./scripts/start.sh"

# Check current .env configuration
echo ""
echo "📄 Current .env Configuration:"
echo "-----------------------------"

if [ -f .env ]; then
    CURRENT_BASE_URL=$(grep "^BASE_URL=" .env | cut -d'=' -f2)
    if [ -n "$CURRENT_BASE_URL" ]; then
        echo "Current BASE_URL: $CURRENT_BASE_URL"
        
        # Provide specific advice based on current setting
        if [ "$CURRENT_BASE_URL" = "http://localhost" ]; then
            echo "ℹ️  Currently configured for local access only"
            if [ -n "$PRIMARY_IP" ]; then
                echo "💡 To enable network access, change to: BASE_URL=http://$PRIMARY_IP"
            fi
        elif [ "$CURRENT_BASE_URL" = "http://0.0.0.0" ]; then
            echo "⚠️  0.0.0.0 is not a valid client URL!"
            echo "🔧 Change to one of the suggested URLs above"
        elif [ -n "$PRIMARY_IP" ] && [ "$CURRENT_BASE_URL" = "http://$PRIMARY_IP" ]; then
            echo "✅ Configured for network access"
        else
            echo "ℹ️  Custom configuration detected"
        fi
    else
        echo "❌ BASE_URL not found in .env"
    fi
else
    echo "❌ .env file not found"
    echo "💡 Create one from the example: cp .env.example .env"
fi
