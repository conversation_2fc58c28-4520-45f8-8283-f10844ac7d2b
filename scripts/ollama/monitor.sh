#!/bin/bash

# Calcounta Ollama Gateway Monitoring Script
# This script monitors the health and status of all services

BASE_URL="http://localhost:85"

echo "🔍 Calcounta Ollama Gateway - Service Monitor"
echo "=============================================="

# Function to check HTTP endpoint
check_endpoint() {
    local url=$1
    local name=$2
    local expected_code=${3:-200}
    
    response=$(curl -s -w "%{http_code}" -o /tmp/response.json "$url" 2>/dev/null)
    http_code="${response: -3}"
    
    if [ "$http_code" = "$expected_code" ]; then
        echo "✅ $name: Healthy (HTTP $http_code)"
        if [ -f /tmp/response.json ]; then
            if command -v jq &> /dev/null; then
                jq -r '.status // .service // "OK"' /tmp/response.json 2>/dev/null || echo "   Response received"
            fi
        fi
    else
        echo "❌ $name: Unhealthy (HTTP $http_code)"
        if [ -f /tmp/response.json ]; then
            echo "   Response: $(cat /tmp/response.json)"
        fi
    fi
    
    rm -f /tmp/response.json
}

# Function to check Docker container status
check_container() {
    local container_name=$1
    local service_name=$2
    
    if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name.*Up"; then
        echo "✅ $service_name: Container running"
        
        # Get container stats
        stats=$(docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}" "$container_name" 2>/dev/null | tail -n 1)
        if [ -n "$stats" ]; then
            echo "   Stats: $stats"
        fi
    else
        echo "❌ $service_name: Container not running"
        
        # Check if container exists but is stopped
        if docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name"; then
            status=$(docker ps -a --format "table {{.Names}}\t{{.Status}}" | grep "$container_name" | awk '{print $2}')
            echo "   Status: $status"
        else
            echo "   Status: Container not found"
        fi
    fi
}

echo ""
echo "📊 Service Health Checks"
echo "------------------------"

# Check main gateway
check_endpoint "$BASE_URL/health" "Main Gateway"

# Check auth service
check_endpoint "$BASE_URL/api/health" "Auth Service"

# Try to check ollama proxy health (might not be exposed)
check_endpoint "http://localhost:8002/health" "Ollama Proxy (Direct)"

echo ""
echo "🐳 Container Status"
echo "------------------"

# Check container status
check_container "calcounta-ollama-gateway" "Nginx Gateway"
check_container "calcounta-auth-service" "Auth Service"
check_container "calcounta-ollama-proxy" "Ollama Proxy"
check_container "calcounta-postgres" "PostgreSQL Database"
check_container "calcounta-redis" "Redis Cache"

echo ""
echo "🔗 Network Connectivity"
echo "----------------------"

# Check if Ollama is accessible from host
if curl -s -f "http://localhost:11434/api/tags" > /dev/null 2>&1; then
    echo "✅ Ollama: Accessible on localhost:11434"
else
    echo "❌ Ollama: Not accessible on localhost:11434"
    echo "   Make sure Ollama is running: ollama serve"
fi

# Check database connectivity
if docker exec calcounta-postgres pg_isready -U gateway_user -d calcounta_gateway > /dev/null 2>&1; then
    echo "✅ PostgreSQL: Database ready"
else
    echo "❌ PostgreSQL: Database not ready"
fi

# Check Redis connectivity
if docker exec calcounta-redis redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis: Cache ready"
else
    echo "❌ Redis: Cache not ready"
fi

echo ""
echo "📈 Resource Usage"
echo "----------------"

# Show Docker system info
echo "Docker system usage:"
docker system df 2>/dev/null || echo "Unable to get Docker system info"

echo ""
echo "💾 Volume Usage"
echo "--------------"

# Show volume usage
docker volume ls --format "table {{.Name}}\t{{.Driver}}" | grep "olamaca" 2>/dev/null || echo "No volumes found"

echo ""
echo "📝 Recent Logs (Last 10 lines)"
echo "------------------------------"

# Show recent logs from each service
services=("calcounta-ollama-gateway" "calcounta-auth-service" "calcounta-ollama-proxy")

for service in "${services[@]}"; do
    echo ""
    echo "🔍 $service logs:"
    docker logs --tail 5 "$service" 2>/dev/null | sed 's/^/   /' || echo "   No logs available"
done

echo ""
echo "🎯 Quick Actions"
echo "---------------"
echo "• View all logs: docker-compose logs -f"
echo "• Restart services: docker-compose restart"
echo "• Stop services: docker-compose down"
echo "• Update services: docker-compose up -d --build"
echo "• Test API: ./scripts/test-api.sh"

echo ""
echo "Monitor completed at $(date)"
