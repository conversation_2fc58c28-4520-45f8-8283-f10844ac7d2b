#!/bin/bash

# Calcounta Ollama Gateway API Test Script
# This script tests the API gateway functionality

set -e

BASE_URL="http://localhost:85"
USERNAME="testuser"
EMAIL="<EMAIL>"
PASSWORD="securepass123"

echo "🧪 Testing Calcounta Ollama Gateway API..."

# Test 1: Health check
echo "1️⃣  Testing health endpoints..."
curl -f "$BASE_URL/health" | jq '.' || echo "❌ Main health check failed"
curl -f "$BASE_URL/api/health" | jq '.' || echo "❌ Auth service health check failed"

# Test 2: User registration
echo "2️⃣  Testing user registration..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/register" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"email\":\"$EMAIL\",\"password\":\"$PASSWORD\"}")

echo "Registration response: $REGISTER_RESPONSE"

if echo "$REGISTER_RESPONSE" | jq -e '.user.id' > /dev/null; then
    echo "✅ User registration successful"
else
    echo "⚠️  User registration failed or user already exists"
fi

# Test 3: User login
echo "3️⃣  Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

echo "Login response: $LOGIN_RESPONSE"

ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.access_token')

if [ "$ACCESS_TOKEN" != "null" ] && [ "$ACCESS_TOKEN" != "" ]; then
    echo "✅ User login successful"
    echo "🔑 Access token: ${ACCESS_TOKEN:0:20}..."
else
    echo "❌ User login failed"
    exit 1
fi

# Test 4: Get user profile
echo "4️⃣  Testing user profile..."
PROFILE_RESPONSE=$(curl -s -X GET "$BASE_URL/api/auth/profile" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "Profile response: $PROFILE_RESPONSE"

if echo "$PROFILE_RESPONSE" | jq -e '.user.username' > /dev/null; then
    echo "✅ User profile retrieval successful"
else
    echo "❌ User profile retrieval failed"
fi

# Test 5: Create API key
echo "5️⃣  Testing API key creation..."
API_KEY_RESPONSE=$(curl -s -X POST "$BASE_URL/api/keys" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test API Key","description":"API key for testing"}')

echo "API key response: $API_KEY_RESPONSE"

CLIENT_ID=$(echo "$API_KEY_RESPONSE" | jq -r '.api_key.client_id')
CLIENT_SECRET=$(echo "$API_KEY_RESPONSE" | jq -r '.api_key.client_secret')

if [ "$CLIENT_ID" != "null" ] && [ "$CLIENT_SECRET" != "null" ]; then
    echo "✅ API key creation successful"
    echo "🔑 Client ID: $CLIENT_ID"
    echo "🔐 Client Secret: ${CLIENT_SECRET:0:20}..."
else
    echo "❌ API key creation failed"
    exit 1
fi

# Test 6: List API keys
echo "6️⃣  Testing API key listing..."
LIST_KEYS_RESPONSE=$(curl -s -X GET "$BASE_URL/api/keys" \
  -H "Authorization: Bearer $ACCESS_TOKEN")

echo "List keys response: $LIST_KEYS_RESPONSE"

if echo "$LIST_KEYS_RESPONSE" | jq -e '.api_keys[0].client_id' > /dev/null; then
    echo "✅ API key listing successful"
else
    echo "❌ API key listing failed"
fi

# Test 7: Test Ollama proxy without credentials (should fail)
echo "7️⃣  Testing Ollama proxy without credentials (should fail)..."
UNAUTHORIZED_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null "$BASE_URL/ai/api/tags")

if [ "$UNAUTHORIZED_RESPONSE" = "401" ]; then
    echo "✅ Unauthorized access properly blocked"
else
    echo "❌ Unauthorized access not properly blocked (got $UNAUTHORIZED_RESPONSE)"
fi

# Test 8: Test Ollama proxy with credentials
echo "8️⃣  Testing Ollama proxy with credentials..."
OLLAMA_RESPONSE=$(curl -s -w "%{http_code}" \
  -H "X-Client-ID: $CLIENT_ID" \
  -H "X-Client-Secret: $CLIENT_SECRET" \
  "$BASE_URL/ai/api/tags")

HTTP_CODE="${OLLAMA_RESPONSE: -3}"
RESPONSE_BODY="${OLLAMA_RESPONSE%???}"

echo "HTTP Code: $HTTP_CODE"
echo "Response: $RESPONSE_BODY"

if [ "$HTTP_CODE" = "200" ]; then
    echo "✅ Ollama proxy access successful"
    if echo "$RESPONSE_BODY" | jq -e '.models' > /dev/null 2>&1; then
        echo "✅ Ollama models retrieved successfully"
    else
        echo "⚠️  Ollama response format unexpected"
    fi
elif [ "$HTTP_CODE" = "503" ]; then
    echo "⚠️  Ollama service not available (this is expected if Ollama is not running)"
else
    echo "❌ Ollama proxy access failed with code $HTTP_CODE"
fi

# Test 9: Test rate limiting (make multiple requests quickly)
echo "9️⃣  Testing rate limiting..."
echo "Making 5 quick requests to test rate limiting..."

for i in {1..5}; do
    RATE_TEST_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
      -H "X-Client-ID: $CLIENT_ID" \
      -H "X-Client-Secret: $CLIENT_SECRET" \
      "$BASE_URL/ai/api/tags")
    echo "Request $i: HTTP $RATE_TEST_RESPONSE"
    sleep 0.1
done

echo ""
echo "🎉 API testing complete!"
echo ""
echo "📊 Summary:"
echo "- Gateway is accessible at: $BASE_URL"
echo "- Auth endpoints: $BASE_URL/api/*"
echo "- Ollama proxy: $BASE_URL/ai/*"
echo "- Your API credentials:"
echo "  Client ID: $CLIENT_ID"
echo "  Client Secret: ${CLIENT_SECRET:0:20}..."
echo ""
echo "💡 You can now use these credentials to access Ollama through the gateway!"
