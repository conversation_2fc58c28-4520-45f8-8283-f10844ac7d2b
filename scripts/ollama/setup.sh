#!/bin/bash

# Calcounta Ollama Gateway Setup Script
# This script sets up the API gateway for the first time

set -e

echo "🚀 Setting up Calcounta Ollama Gateway..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    
    # Generate secure secrets
    echo "🔐 Generating secure secrets..."
    
    # Generate JWT secret
    JWT_SECRET=$(openssl rand -base64 64 | tr -d '\n')
    sed -i "s/your_super_secret_jwt_key_change_me_in_production/$JWT_SECRET/" .env
    
    # Generate database password
    DB_PASSWORD=$(openssl rand -base64 32 | tr -d '\n')
    sed -i "s/secure_password_change_me/$DB_PASSWORD/" .env
    
    # Generate Redis password
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d '\n')
    sed -i "s/redis_password_change_me/$REDIS_PASSWORD/" .env
    
    echo "✅ .env file created with secure secrets"
    echo "⚠️  Please review and customize the .env file if needed"
else
    echo "✅ .env file already exists"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p gateway/nginx/conf.d
mkdir -p logs

# Build and start services
echo "🏗️  Building and starting services..."
docker-compose up -d --build

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check service health
echo "🔍 Checking service health..."

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "✅ Services are running"
else
    echo "❌ Some services failed to start"
    docker-compose logs
    exit 1
fi

# Test health endpoints
echo "🩺 Testing health endpoints..."

# Test auth service
if curl -f http://localhost:85/api/health > /dev/null 2>&1; then
    echo "✅ Auth service is healthy"
else
    echo "⚠️  Auth service health check failed"
fi

# Test ollama proxy
if curl -f http://localhost:85/ai/health > /dev/null 2>&1; then
    echo "✅ Ollama proxy is healthy"
else
    echo "⚠️  Ollama proxy health check failed"
fi

# Test main gateway
if curl -f http://localhost:85/health > /dev/null 2>&1; then
    echo "✅ Main gateway is healthy"
else
    echo "⚠️  Main gateway health check failed"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Register a user: curl -X POST http://localhost:85/api/auth/register -H 'Content-Type: application/json' -d '{\"username\":\"testuser\",\"email\":\"<EMAIL>\",\"password\":\"securepass123\"}'"
echo "2. Login: curl -X POST http://localhost:85/api/auth/login -H 'Content-Type: application/json' -d '{\"username\":\"testuser\",\"password\":\"securepass123\"}'"
echo "3. Create API keys: curl -X POST http://localhost:85/api/keys -H 'Authorization: Bearer <your_jwt_token>' -H 'Content-Type: application/json' -d '{\"name\":\"My API Key\"}'"
echo "4. Test Ollama access: curl -X GET http://localhost:85/ai/api/tags -H 'X-Client-ID: <client_id>' -H 'X-Client-Secret: <client_secret>'"
echo ""
echo "📖 For more information, see the README.md file"
echo "🔧 To view logs: docker-compose logs -f"
echo "🛑 To stop services: docker-compose down"
