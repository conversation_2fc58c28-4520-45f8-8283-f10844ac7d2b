#!/bin/bash

# Calcounta Ollama Model Persistence Verification Script
# This script helps verify that model persistence is working correctly

set -e

echo "🔍 Calcounta Ollama Model Persistence Verification"
echo "=================================================="

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if the Ollama container exists
CONTAINER_NAME="calcounta-ollama"
if ! docker ps -a --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    echo "❌ Ollama container '$CONTAINER_NAME' not found."
    echo "   Please run 'docker-compose up -d' first."
    exit 1
fi

# Check if the container is running
if ! docker ps --format "table {{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
    echo "⚠️  Ollama container '$CONTAINER_NAME' is not running."
    echo "   Starting the container..."
    docker-compose up -d ollama
    echo "   Waiting for container to start..."
    sleep 10
fi

echo "✅ Ollama container is running"

# Check volume mounts
echo ""
echo "🔍 Checking volume mounts..."
VOLUME_INFO=$(docker inspect $CONTAINER_NAME --format '{{range .Mounts}}{{.Type}}:{{.Source}}->{{.Destination}} {{end}}')
echo "   Volume mounts: $VOLUME_INFO"

if echo "$VOLUME_INFO" | grep -q "/root/.ollama"; then
    echo "✅ Ollama data directory is properly mounted"

    # Check if it's a bind mount (host directory)
    if echo "$VOLUME_INFO" | grep -q "bind:"; then
        echo "✅ Using host directory bind mount"
        HOST_PATH=$(echo "$VOLUME_INFO" | grep -o 'bind:[^-]*' | cut -d: -f2)
        echo "   Host directory: $HOST_PATH"

        # Check if host directory exists and is accessible
        if [ -d "$HOST_PATH" ]; then
            echo "✅ Host directory exists and is accessible"
            echo "   Host directory size: $(du -sh "$HOST_PATH" 2>/dev/null | cut -f1 || echo "unknown")"
        else
            echo "❌ Host directory not found: $HOST_PATH"
        fi
    else
        echo "ℹ️  Using Docker named volume"
    fi
else
    echo "❌ Ollama data directory mount not found"
    echo "   This may cause models to be re-downloaded on every restart"
fi

# Check models directory inside container
echo ""
echo "🔍 Checking models directory inside container..."
if docker exec $CONTAINER_NAME test -d "/root/.ollama/models"; then
    echo "✅ Models directory exists inside container"
    
    # List contents
    MODEL_FILES=$(docker exec $CONTAINER_NAME find /root/.ollama/models -type f 2>/dev/null | wc -l)
    echo "   Model files found: $MODEL_FILES"
    
    if [ "$MODEL_FILES" -gt 0 ]; then
        echo "   📁 Model directory contents:"
        docker exec $CONTAINER_NAME ls -la /root/.ollama/models/ 2>/dev/null || echo "   (Unable to list contents)"
    else
        echo "   📁 Models directory is empty (normal if no models downloaded yet)"
    fi
else
    echo "❌ Models directory not found inside container"
fi

# Check Ollama service status
echo ""
echo "🔍 Checking Ollama service status..."
if docker exec $CONTAINER_NAME ollama list >/dev/null 2>&1; then
    echo "✅ Ollama service is responding"
    
    # List current models
    echo "   📋 Currently installed models:"
    docker exec $CONTAINER_NAME ollama list || echo "   (No models installed)"
else
    echo "❌ Ollama service is not responding"
    echo "   The service may still be starting up. Wait a few minutes and try again."
fi

# Check download status directory
echo ""
echo "🔍 Checking download status persistence..."
if docker exec $CONTAINER_NAME test -d "/root/.ollama/download-status"; then
    echo "✅ Download status directory exists"
    
    STATUS_FILES=$(docker exec $CONTAINER_NAME find /root/.ollama/download-status -name "*.state" 2>/dev/null | wc -l)
    echo "   Status files found: $STATUS_FILES"
    
    if [ "$STATUS_FILES" -gt 0 ]; then
        echo "   📁 Download status files:"
        docker exec $CONTAINER_NAME ls -la /root/.ollama/download-status/ 2>/dev/null || echo "   (Unable to list contents)"
    fi
else
    echo "⚠️  Download status directory not found (will be created on first run)"
fi

# Check Docker volume
echo ""
echo "🔍 Checking Docker volume..."
VOLUME_NAME="calcounta_ollama_data"
if docker volume ls --format "table {{.Name}}" | grep -q "^$VOLUME_NAME$"; then
    echo "✅ Docker volume '$VOLUME_NAME' exists"
    
    # Get volume info
    VOLUME_PATH=$(docker volume inspect $VOLUME_NAME --format '{{.Mountpoint}}' 2>/dev/null)
    if [ -n "$VOLUME_PATH" ]; then
        echo "   📁 Volume path: $VOLUME_PATH"
        
        # Check if we can access the volume (requires root on most systems)
        if sudo test -d "$VOLUME_PATH" 2>/dev/null; then
            VOLUME_SIZE=$(sudo du -sh "$VOLUME_PATH" 2>/dev/null | cut -f1)
            echo "   📊 Volume size: $VOLUME_SIZE"
        else
            echo "   📊 Volume size: (requires root access to check)"
        fi
    fi
else
    echo "❌ Docker volume '$VOLUME_NAME' not found"
    echo "   This will cause models to be lost on container restart"
fi

# Test persistence by simulating restart
echo ""
echo "🧪 Testing model persistence (optional)..."
read -p "Do you want to test persistence by restarting the Ollama container? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔄 Restarting Ollama container to test persistence..."
    
    # Get current model list
    echo "   📋 Models before restart:"
    MODELS_BEFORE=$(docker exec $CONTAINER_NAME ollama list 2>/dev/null || echo "No models")
    echo "$MODELS_BEFORE"
    
    # Restart container
    echo "   🔄 Restarting container..."
    docker-compose restart ollama
    
    # Wait for restart
    echo "   ⏳ Waiting for container to restart..."
    sleep 30
    
    # Check models after restart
    echo "   📋 Models after restart:"
    MODELS_AFTER=$(docker exec $CONTAINER_NAME ollama list 2>/dev/null || echo "No models")
    echo "$MODELS_AFTER"
    
    # Compare
    if [ "$MODELS_BEFORE" = "$MODELS_AFTER" ]; then
        echo "✅ Model persistence test PASSED - models survived restart"
    else
        echo "❌ Model persistence test FAILED - models were lost or changed"
        echo "   This indicates a problem with volume mounting or model storage"
    fi
else
    echo "   Skipping persistence test"
fi

echo ""
echo "📋 Summary:"
echo "=========="
echo "✅ = Working correctly"
echo "⚠️  = Warning (may work but could be improved)"
echo "❌ = Problem detected (needs attention)"
echo ""
echo "💡 Tips for troubleshooting:"
echo "   • If models are being re-downloaded, check volume mounts"
echo "   • If Ollama service is not responding, wait longer for startup"
echo "   • If volume is missing, run 'docker-compose down && docker-compose up -d'"
echo "   • Check logs with: docker-compose logs ollama"
echo ""
echo "🔍 Verification complete!"
