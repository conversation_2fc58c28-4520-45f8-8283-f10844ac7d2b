#!/bin/bash

# Calcounta Startup Script
# This script starts the appropriate services based on environment configuration

set -e

echo "🚀 Starting Calcounta..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Use docker compose if available, otherwise fall back to docker-compose
DOCKER_COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
fi

# Validate environment configuration
echo "🔍 Validating environment configuration..."
if [ -f "scripts/validate-env.sh" ]; then
    ./scripts/validate-env.sh
    if [ $? -ne 0 ]; then
        echo "❌ Environment validation failed. Please fix the errors and try again."
        exit 1
    fi
else
    echo "⚠️  Validation script not found, proceeding with basic checks..."

    # Basic validation fallback
    if [ ! -f .env ]; then
        echo "❌ .env file not found. Please create one from .env.example"
        echo "   cp .env.example .env"
        exit 1
    fi

    # Source the .env file
    export $(grep -v '^#' .env | xargs)

    if [ -z "$BASE_URL" ] || [ -z "$PORT" ] || [ -z "$OLLAMA_ENABLED" ]; then
        echo "❌ Required environment variables missing. Please check your .env file."
        exit 1
    fi
fi

# Re-source environment variables after validation
export $(grep -v '^#' .env | xargs)

# Setup Ollama host directory before starting services
echo "📁 Setting up Ollama host directory..."
if [ -f "scripts/setup-ollama-directory.sh" ]; then
    ./scripts/setup-ollama-directory.sh
else
    echo "⚠️  Ollama directory setup script not found, creating basic directory..."
    mkdir -p ./ollama-models/models
    mkdir -p ./ollama-models/download-status
    chmod -R 755 ./ollama-models
fi

# All services are included by default (including Ollama)
echo "🚀 Starting all Calcounta services (including Ollama)..."

# Build and start services
echo "🏗️  Building and starting services..."
$DOCKER_COMPOSE_CMD up -d --build

# Wait for core services to be ready
echo "⏳ Waiting for core services to start..."
sleep 10

# Check core service health
echo "🔍 Checking core service health..."

# Check if core services are running
if $DOCKER_COMPOSE_CMD ps | grep -q "calcounta-backend.*Up"; then
    echo "✅ Backend service is running"
else
    echo "❌ Backend service failed to start"
    $DOCKER_COMPOSE_CMD logs calcounta-backend
    exit 1
fi

if $DOCKER_COMPOSE_CMD ps | grep -q "calcounta-frontend.*Up"; then
    echo "✅ Frontend service is running"
else
    echo "❌ Frontend service failed to start"
    $DOCKER_COMPOSE_CMD logs calcounta-frontend
    exit 1
fi

# Check Ollama services
echo "🔍 Checking Ollama service health..."

# Wait a bit more for Ollama services
sleep 20

if $DOCKER_COMPOSE_CMD ps | grep -q "calcounta-ollama-gateway.*Up"; then
    echo "✅ Ollama gateway is running"
else
    echo "⚠️  Ollama gateway failed to start"
    $DOCKER_COMPOSE_CMD logs calcounta-ollama-gateway
fi

if $DOCKER_COMPOSE_CMD ps | grep -q "calcounta-ollama.*Up"; then
    echo "✅ Ollama service is running"
    echo "📦 Downloading models in background (if configured)..."
else
    echo "⚠️  Ollama service failed to start"
    $DOCKER_COMPOSE_CMD logs calcounta-ollama
fi

echo ""
echo "🎉 Startup complete!"
echo ""
echo "📋 Access Information:"
echo "• Main Application: ${BASE_URL}:${PORT}"
echo "• API Dashboard: ${BASE_URL}:${OLLAMA_GATEWAY_PORT}/dashboard"

echo ""
echo "🔧 Management Commands:"
echo "• View logs: $DOCKER_COMPOSE_CMD logs -f"
echo "• Stop services: $DOCKER_COMPOSE_CMD down"
echo "• Restart: $0"
