<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Dashboard Fix Verification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>🔧 API Dashboard Fix Verification</h1>
    <p>This page will test the authentication flow and API dashboard functionality.</p>
    
    <div>
        <button onclick="testAuthFlow()">🧪 Test Authentication Flow</button>
        <button onclick="testApiDashboard()">📊 Test API Dashboard</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            results.innerHTML = '';
        }
        
        async function testAuthFlow() {
            addResult('🔄 Starting authentication flow test...', 'info');
            
            try {
                // Test 1: Check if user is already logged in
                const token = localStorage.getItem('token');
                const userId = localStorage.getItem('userId');
                
                if (token && userId) {
                    addResult('✅ User already logged in with stored credentials', 'success');
                    addResult(`Token preview: ${token.substring(0, 20)}...`, 'info');
                    return true;
                }
                
                // Test 2: Attempt login
                addResult('🔐 Attempting login...', 'info');
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'sudhamoy'
                    })
                });
                
                if (!loginResponse.ok) {
                    throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText}`);
                }
                
                const loginData = await loginResponse.json();
                addResult('✅ Login successful!', 'success');
                
                // Store credentials
                localStorage.setItem('token', loginData.token);
                localStorage.setItem('userId', loginData.id);
                addResult('💾 Credentials stored in localStorage', 'success');
                
                return true;
                
            } catch (error) {
                addResult(`❌ Authentication test failed: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testApiDashboard() {
            addResult('🔄 Starting API dashboard test...', 'info');
            
            try {
                // Ensure we're authenticated first
                const authSuccess = await testAuthFlow();
                if (!authSuccess) {
                    throw new Error('Authentication required for API dashboard test');
                }
                
                const token = localStorage.getItem('token');
                
                // Test API keys endpoint
                addResult('📡 Testing API keys endpoint...', 'info');
                const apiKeysResponse = await fetch('/api/keys/ollama/keys', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                });
                
                if (!apiKeysResponse.ok) {
                    throw new Error(`API keys request failed: ${apiKeysResponse.status} ${apiKeysResponse.statusText}`);
                }
                
                const apiKeysData = await apiKeysResponse.json();
                addResult(`✅ API keys endpoint working! Found ${apiKeysData.api_keys?.length || 0} keys`, 'success');
                
                // Test frontend route
                addResult('🌐 Testing frontend route...', 'info');
                addResult('✅ API dashboard should now be accessible at <a href="/api-dashboard" target="_blank">/api-dashboard</a>', 'success');
                
                // Test user profile endpoint
                addResult('👤 Testing user profile endpoint...', 'info');
                const profileResponse = await fetch('/api/users/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (profileResponse.ok) {
                    const profileData = await profileResponse.json();
                    addResult(`✅ User profile accessible: ${profileData.email}`, 'success');
                } else {
                    addResult(`⚠️ User profile endpoint returned: ${profileResponse.status}`, 'error');
                }
                
                addResult('🎉 All API dashboard tests completed successfully!', 'success');
                addResult('💡 The loading issue should now be fixed. Try refreshing the API dashboard page.', 'info');
                
            } catch (error) {
                addResult(`❌ API dashboard test failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('🚀 Page loaded. Ready to test!', 'info');
            addResult('💡 Click the buttons above to run tests, or they will run automatically in 2 seconds...', 'info');
            
            setTimeout(() => {
                testApiDashboard();
            }, 2000);
        });
    </script>
</body>
</html>
