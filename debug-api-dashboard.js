// Debug script to test API dashboard authentication
console.log('=== API Dashboard Debug Script ===');

// Check if user is logged in
const token = localStorage.getItem('token');
const userId = localStorage.getItem('userId');

console.log('Token exists:', !!token);
console.log('User ID exists:', !!userId);

if (token) {
    console.log('Token preview:', token.substring(0, 20) + '...');
}

// Test API call directly
if (token) {
    console.log('Testing API call with token...');
    
    fetch('/api/keys/ollama/keys', {
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
        }
    })
    .then(response => {
        console.log('API Response status:', response.status);
        console.log('API Response headers:', [...response.headers.entries()]);
        return response.text();
    })
    .then(text => {
        console.log('API Response body:', text);
        try {
            const json = JSON.parse(text);
            console.log('Parsed JSON:', json);
        } catch (e) {
            console.log('Could not parse as JSON');
        }
    })
    .catch(error => {
        console.error('API call failed:', error);
    });
} else {
    console.log('No token found - user not logged in');
}

// Check axios defaults
if (window.axios) {
    console.log('Axios defaults:', window.axios.defaults.headers.common);
}

// Check if React app is loaded
console.log('React app loaded:', !!window.React);
console.log('Current URL:', window.location.href);
console.log('Current pathname:', window.location.pathname);
