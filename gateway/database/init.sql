-- Calcounta Ollama Gateway Database Schema
-- This script initializes the database with required tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Ollama Users table (separate from main Calcounta users)
CREATE TABLE IF NOT EXISTS ollama_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_admin BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT ollama_username_length CHECK (char_length(username) >= 3),
    CONSTRAINT ollama_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- API Keys table
CREATE TABLE IF NOT EXISTS ollama_api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES ollama_users(id) ON DELETE CASCADE,
    client_id VARCHAR(64) UNIQUE NOT NULL,
    client_secret_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    rate_limit_per_minute INTEGER DEFAULT 60,
    rate_limit_per_hour INTEGER DEFAULT 1000,
    rate_limit_per_day INTEGER DEFAULT 10000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT name_length CHECK (char_length(name) >= 1),
    CONSTRAINT positive_rate_limits CHECK (
        rate_limit_per_minute > 0 AND 
        rate_limit_per_hour > 0 AND 
        rate_limit_per_day > 0
    )
);

-- API Usage Logs table
CREATE TABLE IF NOT EXISTS ollama_api_usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID REFERENCES ollama_api_keys(id) ON DELETE SET NULL,
    client_id VARCHAR(64),
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER NOT NULL,
    response_time_ms INTEGER,
    request_size_bytes INTEGER,
    response_size_bytes INTEGER,
    ip_address INET,
    user_agent TEXT,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX idx_api_usage_logs_api_key_id (api_key_id),
    INDEX idx_api_usage_logs_client_id (client_id),
    INDEX idx_api_usage_logs_created_at (created_at),
    INDEX idx_api_usage_logs_endpoint (endpoint)
);

-- Rate Limiting table
CREATE TABLE IF NOT EXISTS ollama_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES ollama_api_keys(id) ON DELETE CASCADE,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    window_type VARCHAR(10) NOT NULL, -- 'minute', 'hour', 'day'
    request_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint to prevent duplicate windows
    UNIQUE(api_key_id, window_start, window_type),
    
    -- Indexes
    INDEX idx_rate_limits_api_key_window (api_key_id, window_start, window_type)
);

-- User Sessions table (for JWT token blacklisting)
CREATE TABLE IF NOT EXISTS ollama_user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES ollama_users(id) ON DELETE CASCADE,
    token_jti VARCHAR(255) UNIQUE NOT NULL, -- JWT ID claim
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_revoked BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Index for performance
    INDEX idx_user_sessions_token_jti (token_jti),
    INDEX idx_user_sessions_user_id (user_id),
    INDEX idx_user_sessions_expires_at (expires_at)
);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers to automatically update updated_at
CREATE TRIGGER update_ollama_users_updated_at
    BEFORE UPDATE ON ollama_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ollama_api_keys_updated_at
    BEFORE UPDATE ON ollama_api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ollama_rate_limits_updated_at
    BEFORE UPDATE ON ollama_rate_limits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ollama_users_username ON ollama_users(username);
CREATE INDEX IF NOT EXISTS idx_ollama_users_email ON ollama_users(email);
CREATE INDEX IF NOT EXISTS idx_ollama_users_is_active ON ollama_users(is_active);

CREATE INDEX IF NOT EXISTS idx_ollama_api_keys_user_id ON ollama_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_ollama_api_keys_client_id ON ollama_api_keys(client_id);
CREATE INDEX IF NOT EXISTS idx_ollama_api_keys_is_active ON ollama_api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_ollama_api_keys_expires_at ON ollama_api_keys(expires_at);

-- Insert default admin user (password: admin123 - CHANGE IN PRODUCTION!)
-- Password hash for 'admin123' with bcrypt rounds=12
INSERT INTO ollama_users (username, email, password_hash, is_admin) VALUES
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5W', true)
ON CONFLICT (email) DO NOTHING;

-- Create a view for API key statistics
CREATE OR REPLACE VIEW ollama_api_key_stats AS
SELECT
    ak.id,
    ak.client_id,
    ak.name,
    ak.user_id,
    u.username,
    ak.is_active,
    ak.created_at,
    ak.last_used,
    COUNT(aul.id) as total_requests,
    COUNT(CASE WHEN aul.status_code >= 200 AND aul.status_code < 300 THEN 1 END) as successful_requests,
    COUNT(CASE WHEN aul.status_code >= 400 THEN 1 END) as error_requests,
    AVG(aul.response_time_ms) as avg_response_time_ms
FROM ollama_api_keys ak
LEFT JOIN ollama_users u ON ak.user_id = u.id
LEFT JOIN ollama_api_usage_logs aul ON ak.id = aul.api_key_id
GROUP BY ak.id, ak.client_id, ak.name, ak.user_id, u.username, ak.is_active, ak.created_at, ak.last_used;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gateway_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gateway_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO gateway_user;
