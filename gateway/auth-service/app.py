import os
import logging
import secrets
import uuid
from datetime import datetime, timedelta
from functools import wraps

import bcrypt
import psycopg2
import redis
from flask import Flask, request, jsonify
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager, create_access_token, jwt_required, get_jwt_identity, get_jwt
from marshmallow import Schema, fields, ValidationError
from email_validator import validate_email, EmailNotValidError

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'dev-secret-key')
app.config['JWT_ALGORITHM'] = os.getenv('JWT_ALGORITHM', 'HS256')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=int(os.getenv('JWT_EXPIRATION_HOURS', 24)))

# Initialize extensions
CORS(app)
jwt = JWTManager(app)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('POSTGRES_HOST', 'localhost'),
    'port': os.getenv('POSTGRES_PORT', 5432),
    'database': os.getenv('POSTGRES_DB', 'calcounta_gateway'),
    'user': os.getenv('POSTGRES_USER', 'gateway_user'),
    'password': os.getenv('POSTGRES_PASSWORD', 'password')
}

# Redis configuration
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', 'localhost'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'password': os.getenv('REDIS_PASSWORD', None),
    'decode_responses': True
}

# Initialize Redis connection
try:
    redis_client = redis.Redis(**REDIS_CONFIG)
    redis_client.ping()
    logger.info("Redis connection established")
except Exception as e:
    logger.error(f"Redis connection failed: {e}")
    redis_client = None

# Database connection helper
def get_db_connection():
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        return conn
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise

# Validation schemas
class UserRegistrationSchema(Schema):
    username = fields.Str(required=True, validate=lambda x: len(x) >= 3)
    email = fields.Email(required=True)
    password = fields.Str(required=True, validate=lambda x: len(x) >= 8)

class UserLoginSchema(Schema):
    username = fields.Str(required=True)
    password = fields.Str(required=True)

class APIKeySchema(Schema):
    name = fields.Str(required=True, validate=lambda x: len(x) >= 1)
    description = fields.Str(missing="")
    rate_limit_per_minute = fields.Int(missing=60, validate=lambda x: x > 0)
    rate_limit_per_hour = fields.Int(missing=1000, validate=lambda x: x > 0)
    rate_limit_per_day = fields.Int(missing=10000, validate=lambda x: x > 0)

# JWT token blacklist check
@jwt.token_in_blocklist_loader
def check_if_token_revoked(jwt_header, jwt_payload):
    if not redis_client:
        return False
    
    jti = jwt_payload['jti']
    token_in_redis = redis_client.get(f"revoked_token:{jti}")
    return token_in_redis is not None

# Utility functions
def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    rounds = int(os.getenv('BCRYPT_ROUNDS', 12))
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt(rounds=rounds)).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def generate_api_key() -> str:
    """Generate secure API key"""
    length = int(os.getenv('API_KEY_LENGTH', 32))
    return secrets.token_urlsafe(length)

def generate_client_secret() -> str:
    """Generate secure client secret"""
    length = int(os.getenv('CLIENT_SECRET_LENGTH', 64))
    return secrets.token_urlsafe(length)

def hash_client_secret(secret: str) -> str:
    """Hash client secret"""
    return hash_password(secret)

# Error handlers
@app.errorhandler(ValidationError)
def handle_validation_error(e):
    return jsonify({'error': 'Validation error', 'messages': e.messages}), 400

@app.errorhandler(404)
def handle_not_found(e):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def handle_internal_error(e):
    logger.error(f"Internal server error: {e}")
    return jsonify({'error': 'Internal server error'}), 500

# Routes
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        conn = get_db_connection()
        conn.close()
        
        # Check Redis connection
        redis_status = "connected" if redis_client and redis_client.ping() else "disconnected"
        
        return jsonify({
            'status': 'healthy',
            'service': 'auth-service',
            'database': 'connected',
            'redis': redis_status,
            'timestamp': datetime.utcnow().isoformat()
        })
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503

@app.route('/api/auth/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        # Validate input
        schema = UserRegistrationSchema()
        data = schema.load(request.json)
        
        # Validate email format
        try:
            validate_email(data['email'])
        except EmailNotValidError:
            return jsonify({'error': 'Invalid email format'}), 400
        
        # Hash password
        password_hash = hash_password(data['password'])
        
        # Insert user into database
        conn = get_db_connection()
        cur = conn.cursor()
        
        try:
            cur.execute("""
                INSERT INTO ollama_users (username, email, password_hash)
                VALUES (%s, %s, %s)
                RETURNING id, username, email, created_at
            """, (data['username'], data['email'], password_hash))
            
            user = cur.fetchone()
            conn.commit()
            
            logger.info(f"User registered: {data['username']}")
            
            return jsonify({
                'message': 'User registered successfully',
                'user': {
                    'id': str(user[0]),
                    'username': user[1],
                    'email': user[2],
                    'created_at': user[3].isoformat()
                }
            }), 201
            
        except psycopg2.IntegrityError as e:
            conn.rollback()
            if 'username' in str(e):
                return jsonify({'error': 'Username already exists'}), 409
            elif 'email' in str(e):
                return jsonify({'error': 'Email already exists'}), 409
            else:
                return jsonify({'error': 'User already exists'}), 409
        finally:
            cur.close()
            conn.close()
            
    except ValidationError as e:
        return jsonify({'error': 'Validation error', 'messages': e.messages}), 400
    except Exception as e:
        logger.error(f"Registration error: {e}")
        return jsonify({'error': 'Registration failed'}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """User login"""
    try:
        # Validate input
        schema = UserLoginSchema()
        data = schema.load(request.json)

        # Get user from database
        conn = get_db_connection()
        cur = conn.cursor()

        cur.execute("""
            SELECT id, username, email, password_hash, is_active
            FROM ollama_users
            WHERE username = %s
        """, (data['username'],))

        user = cur.fetchone()

        if not user or not verify_password(data['password'], user[3]):
            return jsonify({'error': 'Invalid credentials'}), 401

        if not user[4]:  # is_active
            return jsonify({'error': 'Account is disabled'}), 401

        # Update last login
        cur.execute("""
            UPDATE ollama_users SET last_login = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (user[0],))
        conn.commit()

        # Create JWT token
        additional_claims = {
            'username': user[1],
            'email': user[2]
        }
        access_token = create_access_token(
            identity=str(user[0]),
            additional_claims=additional_claims
        )

        logger.info(f"User logged in: {data['username']}")

        cur.close()
        conn.close()

        return jsonify({
            'message': 'Login successful',
            'access_token': access_token,
            'user': {
                'id': str(user[0]),
                'username': user[1],
                'email': user[2]
            }
        })

    except ValidationError as e:
        return jsonify({'error': 'Validation error', 'messages': e.messages}), 400
    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({'error': 'Login failed'}), 500

@app.route('/api/auth/logout', methods=['POST'])
@jwt_required()
def logout():
    """User logout - revoke JWT token"""
    try:
        if redis_client:
            jti = get_jwt()['jti']
            # Add token to blacklist with expiration
            redis_client.setex(
                f"revoked_token:{jti}",
                app.config['JWT_ACCESS_TOKEN_EXPIRES'],
                "true"
            )

        return jsonify({'message': 'Logout successful'})
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return jsonify({'error': 'Logout failed'}), 500

@app.route('/api/auth/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile"""
    try:
        user_id = get_jwt_identity()

        conn = get_db_connection()
        cur = conn.cursor()

        cur.execute("""
            SELECT id, username, email, is_active, is_admin, created_at, last_login
            FROM ollama_users
            WHERE id = %s
        """, (user_id,))

        user = cur.fetchone()

        if not user:
            return jsonify({'error': 'User not found'}), 404

        cur.close()
        conn.close()

        return jsonify({
            'user': {
                'id': str(user[0]),
                'username': user[1],
                'email': user[2],
                'is_active': user[3],
                'is_admin': user[4],
                'created_at': user[5].isoformat() if user[5] else None,
                'last_login': user[6].isoformat() if user[6] else None
            }
        })

    except Exception as e:
        logger.error(f"Profile error: {e}")
        return jsonify({'error': 'Failed to get profile'}), 500

@app.route('/api/keys', methods=['GET'])
@jwt_required()
def list_api_keys():
    """List user's API keys"""
    try:
        user_id = get_jwt_identity()

        conn = get_db_connection()
        cur = conn.cursor()

        cur.execute("""
            SELECT id, client_id, name, description, is_active,
                   rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day,
                   created_at, last_used, expires_at
            FROM ollama_api_keys
            WHERE user_id = %s
            ORDER BY created_at DESC
        """, (user_id,))

        keys = cur.fetchall()

        api_keys = []
        for key in keys:
            api_keys.append({
                'id': str(key[0]),
                'client_id': key[1],
                'name': key[2],
                'description': key[3],
                'is_active': key[4],
                'rate_limits': {
                    'per_minute': key[5],
                    'per_hour': key[6],
                    'per_day': key[7]
                },
                'created_at': key[8].isoformat() if key[8] else None,
                'last_used': key[9].isoformat() if key[9] else None,
                'expires_at': key[10].isoformat() if key[10] else None
            })

        cur.close()
        conn.close()

        return jsonify({'api_keys': api_keys})

    except Exception as e:
        logger.error(f"List API keys error: {e}")
        return jsonify({'error': 'Failed to list API keys'}), 500

@app.route('/api/keys', methods=['POST'])
@jwt_required()
def create_api_key():
    """Create new API key"""
    try:
        user_id = get_jwt_identity()

        # Validate input
        schema = APIKeySchema()
        data = schema.load(request.json)

        # Generate API credentials
        client_id = generate_api_key()
        client_secret = generate_client_secret()
        client_secret_hash = hash_client_secret(client_secret)

        # Insert into database
        conn = get_db_connection()
        cur = conn.cursor()

        cur.execute("""
            INSERT INTO ollama_api_keys (
                user_id, client_id, client_secret_hash, name, description,
                rate_limit_per_minute, rate_limit_per_hour, rate_limit_per_day
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id, created_at
        """, (
            user_id, client_id, client_secret_hash, data['name'], data['description'],
            data['rate_limit_per_minute'], data['rate_limit_per_hour'], data['rate_limit_per_day']
        ))

        result = cur.fetchone()
        conn.commit()

        logger.info(f"API key created for user {user_id}: {data['name']}")

        cur.close()
        conn.close()

        return jsonify({
            'message': 'API key created successfully',
            'api_key': {
                'id': str(result[0]),
                'client_id': client_id,
                'client_secret': client_secret,  # Only returned once!
                'name': data['name'],
                'description': data['description'],
                'rate_limits': {
                    'per_minute': data['rate_limit_per_minute'],
                    'per_hour': data['rate_limit_per_hour'],
                    'per_day': data['rate_limit_per_day']
                },
                'created_at': result[1].isoformat()
            },
            'warning': 'Store the client_secret securely. It will not be shown again.'
        }), 201

    except ValidationError as e:
        return jsonify({'error': 'Validation error', 'messages': e.messages}), 400
    except Exception as e:
        logger.error(f"Create API key error: {e}")
        return jsonify({'error': 'Failed to create API key'}), 500

@app.route('/api/keys/<key_id>', methods=['DELETE'])
@jwt_required()
def delete_api_key(key_id):
    """Delete/revoke API key"""
    try:
        user_id = get_jwt_identity()

        conn = get_db_connection()
        cur = conn.cursor()

        # Check if key belongs to user
        cur.execute("""
            SELECT id, name FROM ollama_api_keys
            WHERE id = %s AND user_id = %s
        """, (key_id, user_id))

        key = cur.fetchone()
        if not key:
            return jsonify({'error': 'API key not found'}), 404

        # Delete the key
        cur.execute("DELETE FROM ollama_api_keys WHERE id = %s", (key_id,))
        conn.commit()

        logger.info(f"API key deleted: {key[1]} (user: {user_id})")

        cur.close()
        conn.close()

        return jsonify({'message': 'API key deleted successfully'})

    except Exception as e:
        logger.error(f"Delete API key error: {e}")
        return jsonify({'error': 'Failed to delete API key'}), 500

@app.route('/api/keys/<key_id>/toggle', methods=['PUT'])
@jwt_required()
def toggle_api_key(key_id):
    """Enable/disable API key"""
    try:
        user_id = get_jwt_identity()

        conn = get_db_connection()
        cur = conn.cursor()

        # Check if key belongs to user and get current status
        cur.execute("""
            SELECT id, name, is_active FROM ollama_api_keys
            WHERE id = %s AND user_id = %s
        """, (key_id, user_id))

        key = cur.fetchone()
        if not key:
            return jsonify({'error': 'API key not found'}), 404

        # Toggle status
        new_status = not key[2]
        cur.execute("""
            UPDATE ollama_api_keys SET is_active = %s
            WHERE id = %s
        """, (new_status, key_id))
        conn.commit()

        status_text = "enabled" if new_status else "disabled"
        logger.info(f"API key {status_text}: {key[1]} (user: {user_id})")

        cur.close()
        conn.close()

        return jsonify({
            'message': f'API key {status_text} successfully',
            'is_active': new_status
        })

    except Exception as e:
        logger.error(f"Toggle API key error: {e}")
        return jsonify({'error': 'Failed to toggle API key'}), 500

@app.route('/api/keys/<key_id>/regenerate-secret', methods=['PUT'])
@jwt_required()
def regenerate_api_key_secret(key_id):
    """Regenerate client secret for an existing API key"""
    try:
        user_id = get_jwt_identity()

        conn = get_db_connection()
        cur = conn.cursor()

        # Check if key belongs to user
        cur.execute("""
            SELECT id, name, client_id FROM ollama_api_keys
            WHERE id = %s AND user_id = %s
        """, (key_id, user_id))

        key = cur.fetchone()
        if not key:
            return jsonify({'error': 'API key not found'}), 404

        # Generate new client secret
        new_client_secret = generate_client_secret()
        new_client_secret_hash = hash_client_secret(new_client_secret)

        # Update the key with new secret
        cur.execute("""
            UPDATE ollama_api_keys
            SET client_secret_hash = %s, last_used = NULL
            WHERE id = %s
        """, (new_client_secret_hash, key_id))
        conn.commit()

        logger.info(f"API key secret regenerated: {key[1]} (user: {user_id})")

        cur.close()
        conn.close()

        return jsonify({
            'message': 'Client secret regenerated successfully',
            'api_key': {
                'id': str(key[0]),
                'name': key[1],
                'client_id': key[2],
                'client_secret': new_client_secret  # Only returned once!
            },
            'warning': 'Store the new client_secret securely. The old secret is now invalid.'
        })

    except Exception as e:
        logger.error(f"Regenerate API key secret error: {e}")
        return jsonify({'error': 'Failed to regenerate client secret'}), 500

@app.route('/api/keys/validate', methods=['POST'])
def validate_api_key():
    """Validate API key credentials (used by proxy service)"""
    try:
        data = request.json
        if not data or 'client_id' not in data or 'client_secret' not in data:
            return jsonify({'valid': False, 'error': 'Missing credentials'}), 400

        client_id = data['client_id']
        client_secret = data['client_secret']

        conn = get_db_connection()
        cur = conn.cursor()

        # Get API key details
        cur.execute("""
            SELECT ak.id, ak.client_secret_hash, ak.is_active, ak.expires_at,
                   ak.rate_limit_per_minute, ak.rate_limit_per_hour, ak.rate_limit_per_day,
                   u.id as user_id, u.username, u.is_active as user_active
            FROM ollama_api_keys ak
            JOIN ollama_users u ON ak.user_id = u.id
            WHERE ak.client_id = %s
        """, (client_id,))

        key_data = cur.fetchone()

        if not key_data:
            return jsonify({'valid': False, 'error': 'Invalid client_id'}), 401

        # Verify client secret
        if not verify_password(client_secret, key_data[1]):
            return jsonify({'valid': False, 'error': 'Invalid client_secret'}), 401

        # Check if key is active
        if not key_data[2]:
            return jsonify({'valid': False, 'error': 'API key is disabled'}), 401

        # Check if user is active
        if not key_data[9]:
            return jsonify({'valid': False, 'error': 'User account is disabled'}), 401

        # Check expiration
        if key_data[3] and datetime.utcnow() > key_data[3]:
            return jsonify({'valid': False, 'error': 'API key has expired'}), 401

        # Update last used timestamp
        cur.execute("""
            UPDATE ollama_api_keys SET last_used = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (key_data[0],))
        conn.commit()

        cur.close()
        conn.close()

        return jsonify({
            'valid': True,
            'api_key_id': str(key_data[0]),
            'user_id': str(key_data[7]),
            'username': key_data[8],
            'rate_limits': {
                'per_minute': key_data[4],
                'per_hour': key_data[5],
                'per_day': key_data[6]
            }
        })

    except Exception as e:
        logger.error(f"Validate API key error: {e}")
        return jsonify({'valid': False, 'error': 'Validation failed'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8001, debug=os.getenv('DEBUG', 'false').lower() == 'true')
