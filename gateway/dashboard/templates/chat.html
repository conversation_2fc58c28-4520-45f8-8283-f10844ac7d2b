{% extends "base.html" %}

{% block title %}AI Playground - Calcounta AI Gateway{% endblock %}

{% block content %}
<!-- Modern Playground Header -->
<div class="playground-header" style="margin-bottom: 2rem;">
    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem;">
        <div>
            <h1 style="font-size: 2rem; font-weight: 600; color: #111827; margin: 0 0 0.5rem 0; letter-spacing: -0.025em;">
                AI Playground
            </h1>
            <p style="color: #6b7280; font-size: 1rem; margin: 0; font-weight: 400;">
                Interact with AI models through chat and vision analysis
            </p>
        </div>
        <div style="display: flex; gap: 0.75rem;">
            <button id="clearChatBtn" onclick="clearChat()" style="background: #f3f4f6; color: #374151; border: 1px solid #d1d5db; padding: 0.75rem 1rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease; display: none; align-items: center; gap: 0.5rem;"
                    onmouseover="this.style.backgroundColor='#e5e7eb'"
                    onmouseout="this.style.backgroundColor='#f3f4f6'">
                <i class="fas fa-trash" style="font-size: 0.875rem;"></i>
                Clear Chat
            </button>
        </div>
    </div>
</div>

<!-- Modern Playground Layout -->
<div class="playground-container" style="display: grid; grid-template-columns: 320px 1fr; gap: 2rem; height: calc(100vh - 200px); min-height: 600px;">

    <!-- Left Sidebar - Configuration -->
    <div class="playground-sidebar" style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1.5rem; overflow-y: auto; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">

        <!-- API Configuration -->
        <div class="config-section" style="margin-bottom: 2rem;">
            <h3 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                <div style="background: #3b82f6; border-radius: 0.375rem; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-key" style="color: white; font-size: 0.75rem;"></i>
                </div>
                API Setup
            </h3>

            {% if api_keys %}
            <div style="margin-bottom: 1rem;">
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Quick Select</label>
                <select id="existing_keys" onchange="selectExistingKey()" style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;">
                    <option value="">Choose API key...</option>
                    {% for key in api_keys %}
                    <option value="{{ key.id }}" data-client-id="{{ key.client_id }}" data-name="{{ key.name }}">
                        {{ key.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            {% endif %}

            <div style="margin-bottom: 1rem;">
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Client ID</label>
                <input type="text" id="client_id" name="client_id" value="{{ client_id or '' }}" placeholder="Enter client ID"
                       style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; font-family: 'SF Mono', Monaco, monospace;">
            </div>

            <div style="margin-bottom: 1rem;">
                <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Client Secret</label>
                <div style="position: relative;">
                    <input type="password" id="client_secret" name="client_secret" value="{{ client_secret or '' }}" placeholder="Enter client secret"
                           style="width: 100%; padding: 0.5rem 2.5rem 0.5rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; font-family: 'SF Mono', Monaco, monospace;">
                    <button type="button" onclick="togglePasswordVisibility()" style="position: absolute; right: 0.5rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: #6b7280; cursor: pointer; padding: 0.25rem;">
                        <i class="fas fa-eye" id="passwordToggleIcon" style="font-size: 0.875rem;"></i>
                    </button>
                </div>
            </div>

            <button onclick="testConnection()" id="testConnectionBtn" style="width: 100%; background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; border: none; padding: 0.75rem; border-radius: 0.375rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;"
                    onmouseover="this.style.transform='translateY(-1px)'"
                    onmouseout="this.style.transform='translateY(0)'">
                <i class="fas fa-plug" style="font-size: 0.875rem;"></i>
                Test Connection
            </button>
        </div>

        <!-- Model Selection -->
        <div class="config-section" style="margin-bottom: 2rem;">
            <h3 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                <div style="background: #8b5cf6; border-radius: 0.375rem; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-robot" style="color: white; font-size: 0.75rem;"></i>
                </div>
                Model
            </h3>

            <div style="margin-bottom: 1rem;">
                <select id="selected_model" style="width: 100%; padding: 0.5rem 0.75rem; border: 1px solid #d1d5db; border-radius: 0.375rem; font-size: 0.875rem; background: white;">
                    <option value="">Select a model...</option>
                    {% if models %}
                        {% for model in models %}
                        <option value="{{ model.name }}">{{ model.name }}</option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>

            <div id="availableModels" style="max-height: 200px; overflow-y: auto;">
                {% if models %}
                    {% for model in models %}
                    <div onclick="selectModel('{{ model.name }}')" style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; margin-bottom: 0.5rem; cursor: pointer; transition: all 0.2s ease; background: white;"
                         onmouseover="this.style.backgroundColor='#f9fafb'; this.style.borderColor='#3b82f6'"
                         onmouseout="this.style.backgroundColor='white'; this.style.borderColor='#e5e7eb'">
                        <div style="font-weight: 500; color: #111827; font-size: 0.875rem; margin-bottom: 0.25rem;">{{ model.name }}</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">
                            Size: {{ model.size if model.size else 'Unknown' }}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div style="text-align: center; padding: 2rem 1rem; color: #6b7280;">
                        <div style="background: #f3f4f6; border-radius: 50%; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem auto;">
                            <i class="fas fa-robot" style="font-size: 1.25rem; color: #9ca3af;"></i>
                        </div>
                        <p style="font-size: 0.875rem; margin: 0 0 0.5rem 0; font-weight: 500; color: #374151;">No models available</p>
                        <p style="font-size: 0.75rem; margin: 0; line-height: 1.4;">Test your API connection to load available models</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Mode Selection -->
        <div class="config-section">
            <h3 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
                <div style="background: #10b981; border-radius: 0.375rem; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-cog" style="color: white; font-size: 0.75rem;"></i>
                </div>
                Mode
            </h3>

            <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                <label style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; cursor: pointer; transition: all 0.2s ease;"
                       onmouseover="this.style.backgroundColor='#f9fafb'"
                       onmouseout="this.style.backgroundColor='white'">
                    <input type="radio" name="mode" id="chatMode" value="chat" checked onchange="switchMode()" style="margin: 0;">
                    <div>
                        <div style="font-weight: 500; color: #111827; font-size: 0.875rem;">Chat</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Text conversations</div>
                    </div>
                </label>

                <label style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 0.375rem; cursor: pointer; transition: all 0.2s ease;"
                       onmouseover="this.style.backgroundColor='#f9fafb'"
                       onmouseout="this.style.backgroundColor='white'">
                    <input type="radio" name="mode" id="imageMode" value="image" onchange="switchMode()" style="margin: 0;">
                    <div>
                        <div style="font-weight: 500; color: #111827; font-size: 0.875rem;">Vision</div>
                        <div style="font-size: 0.75rem; color: #6b7280;">Image analysis</div>
                    </div>
                </label>
            </div>
        </div>
    </div>

    <!-- Right Main Area - Chat Interface -->
    <div class="playground-main" style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; display: flex; flex-direction: column; overflow: hidden; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">

        <!-- Chat Interface -->
        <div id="chatInterface" style="display: flex; flex-direction: column; height: 100%;">
            <!-- Chat Header -->
            <div style="padding: 1.5rem; border-bottom: 1px solid #e5e7eb; background: #f9fafb;">
                <div style="display: flex; align-items: center; justify-content: between; gap: 1rem;">
                    <div style="display: flex; align-items: center; gap: 0.75rem;">
                        <div style="background: #3b82f6; border-radius: 0.5rem; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                            <i class="fas fa-comments" style="color: white; font-size: 0.875rem;"></i>
                        </div>
                        <div>
                            <h3 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0;">Chat Interface</h3>
                            <p style="font-size: 0.875rem; color: #6b7280; margin: 0;" id="selectedModelDisplay">Select a model to start chatting</p>
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div id="connectionStatus" style="display: flex; align-items: center; gap: 0.5rem; padding: 0.375rem 0.75rem; background: #fee2e2; color: #dc2626; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500;">
                            <div style="width: 6px; height: 6px; background: #dc2626; border-radius: 50%;"></div>
                            Not Connected
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Area -->
            <div id="chatMessages" style="flex: 1; padding: 1.5rem; overflow-y: auto; background: #fafafa;">
                <div id="welcomeMessage" style="text-align: center; padding: 3rem 2rem; color: #6b7280;">
                    <div style="background: #f3f4f6; border-radius: 50%; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem auto;">
                        <i class="fas fa-robot" style="font-size: 1.5rem; color: #9ca3af;"></i>
                    </div>
                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin: 0 0 0.5rem 0;">Welcome to AI Playground</h3>
                    <p style="font-size: 0.875rem; margin: 0;">Configure your API credentials and select a model to start chatting</p>
                </div>

                <div id="chatHistory" style="display: none;">
                    <!-- Chat messages will be dynamically added here -->
                </div>
            </div>

            <!-- Chat Input Area -->
            <div style="padding: 1.5rem; border-top: 1px solid #e5e7eb; background: white;">
                <div style="display: flex; gap: 1rem; align-items: end;">
                    <div style="flex: 1;">
                        <textarea id="chat_message" placeholder="Type your message here..."
                                  style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 0.875rem; resize: none; min-height: 44px; max-height: 120px; line-height: 1.5;"
                                  rows="1" onkeydown="handleChatKeydown(event)"></textarea>
                    </div>
                    <button onclick="sendMessage()" id="sendButton" disabled style="background: #d1d5db; color: #9ca3af; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: not-allowed; transition: all 0.2s ease; display: flex; align-items: center; gap: 0.5rem; white-space: nowrap;">
                        <i class="fas fa-paper-plane" style="font-size: 0.875rem;"></i>
                        Send
                    </button>
                </div>
            </div>
        </div>

        <!-- Image Analysis Interface -->
        <div id="imageInterface" style="display: none; flex-direction: column; height: 100%;">
            <!-- Image Header -->
            <div style="padding: 1.5rem; border-bottom: 1px solid #e5e7eb; background: #f9fafb;">
                <div style="display: flex; align-items: center; gap: 0.75rem;">
                    <div style="background: #8b5cf6; border-radius: 0.5rem; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-camera" style="color: white; font-size: 0.875rem;"></i>
                    </div>
                    <div>
                        <h3 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0;">Vision Analysis</h3>
                        <p style="font-size: 0.875rem; color: #6b7280; margin: 0;">Upload and analyze food images</p>
                    </div>
                </div>
            </div>

            <!-- Image Upload Area -->
            <div style="flex: 1; padding: 1.5rem; overflow-y: auto;">
                <div id="imageUploadArea" style="border: 2px dashed #d1d5db; border-radius: 0.75rem; padding: 3rem 2rem; text-align: center; transition: all 0.2s ease; cursor: pointer;"
                     onclick="document.getElementById('food_image').click()"
                     onmouseover="this.style.borderColor='#3b82f6'; this.style.backgroundColor='#f8fafc'"
                     onmouseout="this.style.borderColor='#d1d5db'; this.style.backgroundColor='transparent'">
                    <div style="background: #f3f4f6; border-radius: 50%; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem auto;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 1.5rem; color: #9ca3af;"></i>
                    </div>
                    <h3 style="font-size: 1.125rem; font-weight: 500; color: #374151; margin: 0 0 0.5rem 0;">Upload Food Image</h3>
                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0 0 1rem 0;">Click to select or drag and drop</p>
                    <p style="font-size: 0.75rem; color: #9ca3af; margin: 0;">Supports JPG, PNG, GIF, WebP (max 10MB)</p>
                    <input type="file" id="food_image" accept="image/*" onchange="previewFoodImage(this)" style="display: none;">
                </div>

                <!-- Image Preview -->
                <div id="foodImagePreviewContainer" style="display: none; margin-top: 1.5rem;">
                    <div style="border: 1px solid #e5e7eb; border-radius: 0.75rem; overflow: hidden;">
                        <div style="padding: 1rem; border-bottom: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">
                            <h4 style="font-size: 0.875rem; font-weight: 500; color: #111827; margin: 0;">Image Preview</h4>
                            <button onclick="clearFoodImage()" style="background: #fee2e2; color: #dc2626; border: none; padding: 0.375rem 0.75rem; border-radius: 0.375rem; font-size: 0.75rem; cursor: pointer; transition: all 0.2s ease;"
                                    onmouseover="this.style.backgroundColor='#fecaca'"
                                    onmouseout="this.style.backgroundColor='#fee2e2'">
                                <i class="fas fa-times" style="margin-right: 0.25rem;"></i>Remove
                            </button>
                        </div>
                        <div style="padding: 1rem; text-align: center;">
                            <img id="foodImagePreview" src="" alt="Food image preview" style="max-width: 100%; max-height: 300px; border-radius: 0.5rem;">
                        </div>
                    </div>

                    <div style="margin-top: 1rem; display: flex; gap: 0.75rem;">
                        <button onclick="analyzeFoodImage()" id="analyzeImageButton" style="flex: 1; background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white; border: none; padding: 0.75rem 1rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;"
                                onmouseover="this.style.transform='translateY(-1px)'"
                                onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-search" style="font-size: 0.875rem;"></i>
                            Analyze Nutrition
                        </button>
                        <button onclick="clearFoodImage()" style="background: #f3f4f6; color: #374151; border: 1px solid #d1d5db; padding: 0.75rem 1rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease;"
                                onmouseover="this.style.backgroundColor='#e5e7eb'"
                                onmouseout="this.style.backgroundColor='#f3f4f6'">
                            <i class="fas fa-trash" style="margin-right: 0.5rem;"></i>Clear
                        </button>
                    </div>
                </div>

                <!-- Analysis Results -->
                <div id="imageAnalysisResults" style="display: none; margin-top: 1.5rem;">
                    <h4 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0 0 1rem 0;">Nutritional Analysis</h4>
                    <div id="image_analysis_response" style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 1rem; min-height: 100px;">
                        <em style="color: #6b7280;">Analysis results will appear here...</em>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Modern Playground JavaScript

// Global state
let isConnected = false;
let currentModel = '';
let chatHistory = [];

// Initialize playground
document.addEventListener('DOMContentLoaded', function() {
    updateSendButtonState();
    setupTextareaAutoResize();
});

function selectExistingKey() {
    const select = document.getElementById('existing_keys');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        const clientId = selectedOption.getAttribute('data-client-id');
        const keyName = selectedOption.getAttribute('data-name');

        // Fill the client ID
        document.getElementById('client_id').value = clientId;

        // Clear the client secret for security
        document.getElementById('client_secret').value = '';
        document.getElementById('client_secret').focus();

        // Show success message
        showToast(`Auto-filled Client ID from "${keyName}". Please enter the Client Secret.`, 'info');
        updateSendButtonState();
    }
}

function togglePasswordVisibility() {
    const passwordField = document.getElementById('client_secret');
    const toggleIcon = document.getElementById('passwordToggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordField.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

async function testConnection() {
    const clientId = document.getElementById('client_id').value;
    const clientSecret = document.getElementById('client_secret').value;

    if (!clientId || !clientSecret) {
        showToast('Please enter both Client ID and Client Secret', 'error');
        return;
    }

    const btn = document.getElementById('testConnectionBtn');
    const originalText = btn.innerHTML;

    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Testing...';

    try {
        // Test connection and fetch models
        const response = await fetch('/ai/api/tags', {
            method: 'GET',
            headers: {
                'X-Client-ID': clientId,
                'X-Client-Secret': clientSecret
            }
        });

        if (response.ok) {
            const data = await response.json();
            const models = data.models || [];

            isConnected = true;
            updateConnectionStatus(true);
            populateModels(models);
            updateSendButtonState();

            showToast(`Connection successful! Found ${models.length} models.`, 'success');
        } else {
            const errorData = await response.json();
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }
    } catch (error) {
        isConnected = false;
        updateConnectionStatus(false);
        showToast(`Connection failed: ${error.message}`, 'error');
    } finally {
        btn.disabled = false;
        btn.innerHTML = originalText;
    }
}

function updateConnectionStatus(connected) {
    const statusEl = document.getElementById('connectionStatus');
    if (connected) {
        statusEl.innerHTML = `
            <div style="width: 6px; height: 6px; background: #10b981; border-radius: 50%;"></div>
            Connected
        `;
        statusEl.style.background = '#d1fae5';
        statusEl.style.color = '#065f46';
    } else {
        statusEl.innerHTML = `
            <div style="width: 6px; height: 6px; background: #dc2626; border-radius: 50%;"></div>
            Not Connected
        `;
        statusEl.style.background = '#fee2e2';
        statusEl.style.color = '#dc2626';
    }
}

function populateModels(models) {
    const modelSelect = document.getElementById('selected_model');
    const availableModelsDiv = document.getElementById('availableModels');

    // Clear existing options
    modelSelect.innerHTML = '<option value="">Select a model...</option>';

    if (availableModelsDiv) {
        availableModelsDiv.innerHTML = '';
    }

    if (models && models.length > 0) {
        models.forEach(model => {
            // Add to dropdown
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = model.name;
            modelSelect.appendChild(option);

            // Add to visual cards if container exists
            if (availableModelsDiv) {
                const modelCard = document.createElement('div');
                modelCard.onclick = () => selectModel(model.name);
                modelCard.style.cssText = `
                    padding: 0.75rem;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.375rem;
                    margin-bottom: 0.5rem;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    background: white;
                `;
                modelCard.onmouseover = () => {
                    modelCard.style.backgroundColor = '#f9fafb';
                    modelCard.style.borderColor = '#3b82f6';
                };
                modelCard.onmouseout = () => {
                    modelCard.style.backgroundColor = 'white';
                    modelCard.style.borderColor = '#e5e7eb';
                };

                modelCard.innerHTML = `
                    <div style="font-weight: 500; color: #111827; font-size: 0.875rem; margin-bottom: 0.25rem;">${model.name}</div>
                    <div style="font-size: 0.75rem; color: #6b7280;">
                        Size: ${model.size || 'Unknown'}
                    </div>
                `;

                availableModelsDiv.appendChild(modelCard);
            }
        });

        showToast(`Loaded ${models.length} models`, 'success');
    } else {
        showToast('No models found', 'info');
    }
}

function updateSendButtonState() {
    const sendBtn = document.getElementById('sendButton');
    const clientId = document.getElementById('client_id').value;
    const clientSecret = document.getElementById('client_secret').value;
    const model = document.getElementById('selected_model').value;
    const message = document.getElementById('chat_message').value;

    const canSend = isConnected && clientId && clientSecret && model && message.trim();

    if (canSend) {
        sendBtn.disabled = false;
        sendBtn.style.background = 'linear-gradient(135deg, #3b82f6, #2563eb)';
        sendBtn.style.color = 'white';
        sendBtn.style.cursor = 'pointer';
    } else {
        sendBtn.disabled = true;
        sendBtn.style.background = '#d1d5db';
        sendBtn.style.color = '#9ca3af';
        sendBtn.style.cursor = 'not-allowed';
    }
}

function setupTextareaAutoResize() {
    const textarea = document.getElementById('chat_message');
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        updateSendButtonState();
    });
}

function showToast(message, type = 'info') {
    // Create a simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'info' ? 'info' : type === 'success' ? 'success' : 'warning'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'info' ? 'info-circle' : type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function selectModel(modelName) {
    document.getElementById('selected_model').value = modelName;
    currentModel = modelName;

    // Update display
    const displayEl = document.getElementById('selectedModelDisplay');
    displayEl.textContent = `Using ${modelName}`;

    updateSendButtonState();

    // Focus on message input if in chat mode
    if (document.getElementById('chatMode').checked) {
        document.getElementById('chat_message').focus();
    }

    showToast(`Selected model: ${modelName}`, 'success');
}

function switchMode() {
    const chatMode = document.getElementById('chatMode').checked;
    const imageMode = document.getElementById('imageMode').checked;

    const chatInterface = document.getElementById('chatInterface');
    const imageInterface = document.getElementById('imageInterface');

    if (chatMode) {
        chatInterface.style.display = 'flex';
        imageInterface.style.display = 'none';
        document.getElementById('chat_message').focus();
    } else if (imageMode) {
        chatInterface.style.display = 'none';
        imageInterface.style.display = 'flex';
    }
}

function clearChat() {
    chatHistory = [];
    const chatHistoryEl = document.getElementById('chatHistory');
    const welcomeEl = document.getElementById('welcomeMessage');

    chatHistoryEl.innerHTML = '';
    chatHistoryEl.style.display = 'none';
    welcomeEl.style.display = 'block';

    document.getElementById('clearChatBtn').style.display = 'none';
    document.getElementById('chat_message').value = '';
    updateSendButtonState();

    showToast('Chat cleared', 'info');
}

function handleChatKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (!document.getElementById('sendButton').disabled) {
            sendMessage();
        }
    }
}

function previewFoodImage(input) {
    const preview = document.getElementById('foodImagePreview');
    const container = document.getElementById('foodImagePreviewContainer');
    const uploadArea = document.getElementById('imageUploadArea');

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.src = e.target.result;
            container.style.display = 'block';
            uploadArea.style.display = 'none';
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        clearFoodImage();
    }
}

function clearFoodImage() {
    document.getElementById('food_image').value = '';
    document.getElementById('foodImagePreviewContainer').style.display = 'none';
    document.getElementById('imageUploadArea').style.display = 'block';
    document.getElementById('imageAnalysisResults').style.display = 'none';
    document.getElementById('image_analysis_response').innerHTML = '<em style="color: #6b7280;">Analysis results will appear here...</em>';
}

function addChatMessage(message, isUser = false, isLoading = false) {
    const chatHistoryEl = document.getElementById('chatHistory');
    const welcomeEl = document.getElementById('welcomeMessage');

    // Hide welcome message and show chat history
    welcomeEl.style.display = 'none';
    chatHistoryEl.style.display = 'block';
    document.getElementById('clearChatBtn').style.display = 'flex';

    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
        display: flex;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        ${isUser ? 'flex-direction: row-reverse;' : ''}
    `;

    const avatar = document.createElement('div');
    avatar.style.cssText = `
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        ${isUser ? 'background: #3b82f6;' : 'background: #f3f4f6; border: 1px solid #e5e7eb;'}
    `;
    avatar.innerHTML = `<i class="fas ${isUser ? 'fa-user' : 'fa-robot'}" style="color: ${isUser ? 'white' : '#6b7280'}; font-size: 0.875rem;"></i>`;

    const messageContent = document.createElement('div');
    messageContent.style.cssText = `
        flex: 1;
        max-width: 80%;
    `;

    const messageBubble = document.createElement('div');
    messageBubble.style.cssText = `
        padding: 0.75rem 1rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        line-height: 1.5;
        ${isUser ?
            'background: #3b82f6; color: white; border-bottom-right-radius: 0.25rem;' :
            'background: white; color: #111827; border: 1px solid #e5e7eb; border-bottom-left-radius: 0.25rem;'
        }
        ${isLoading ? 'opacity: 0.7;' : ''}
    `;

    if (isLoading) {
        messageBubble.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Thinking...';
    } else {
        messageBubble.textContent = message;
    }

    messageContent.appendChild(messageBubble);

    if (!isUser && !isLoading) {
        const timestamp = document.createElement('div');
        timestamp.style.cssText = 'font-size: 0.75rem; color: #9ca3af; margin-top: 0.25rem; padding-left: 1rem;';
        timestamp.textContent = new Date().toLocaleTimeString();
        messageContent.appendChild(timestamp);
    }

    messageEl.appendChild(avatar);
    messageEl.appendChild(messageContent);

    chatHistoryEl.appendChild(messageEl);

    // Scroll to bottom
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.scrollTop = chatMessages.scrollHeight;

    return messageEl;
}



async function sendMessage() {
    const clientId = document.getElementById('client_id').value;
    const clientSecret = document.getElementById('client_secret').value;
    const model = document.getElementById('selected_model').value;
    const message = document.getElementById('chat_message').value.trim();

    if (!clientId || !clientSecret) {
        showToast('Please enter your API credentials first.', 'error');
        return;
    }

    if (!model) {
        showToast('Please select a model.', 'error');
        return;
    }

    if (!message) {
        showToast('Please enter a message.', 'error');
        return;
    }

    const sendButton = document.getElementById('sendButton');
    const messageInput = document.getElementById('chat_message');

    // Add user message to chat
    addChatMessage(message, true);

    // Add loading message for AI response
    const loadingMessage = addChatMessage('', false, true);

    // Clear input and disable send button
    messageInput.value = '';
    messageInput.style.height = 'auto';
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Sending...';

    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                client_id: clientId,
                client_secret: clientSecret,
                model: model,
                message: message
            })
        });

        const data = await response.json();

        // Remove loading message
        loadingMessage.remove();

        if (response.ok) {
            const content = data.message?.content || 'No response content';
            addChatMessage(content, false);
            chatHistory.push({ user: message, assistant: content, timestamp: new Date() });
        } else {
            addChatMessage(`Error: ${data.error || 'Unknown error occurred'}`, false);
            showToast('Failed to get response from AI', 'error');
        }
    } catch (error) {
        // Remove loading message
        loadingMessage.remove();
        addChatMessage(`Network error: ${error.message}`, false);
        showToast('Network error occurred', 'error');
    } finally {
        // Reset button state
        sendButton.innerHTML = '<i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>Send';
        updateSendButtonState();
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        z-index: 9999;
        transform: translateX(100%);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        max-width: 400px;
    `;

    const icon = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';
    toast.innerHTML = `<i class="fas ${icon}"></i>${message}`;

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 10);

    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Helper function to convert file to base64
function fileToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // Remove the data:image/jpeg;base64, prefix
            const base64 = reader.result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = error => reject(error);
    });
}

// Food image analysis function
async function analyzeFoodImage() {
    const clientId = document.getElementById('client_id').value;
    const clientSecret = document.getElementById('client_secret').value;
    const model = document.getElementById('selected_model').value;
    const imageFile = document.getElementById('food_image').files[0];

    if (!clientId || !clientSecret) {
        showToast('Please enter your API credentials first.', 'error');
        return;
    }

    if (!model) {
        showToast('Please select a model.', 'error');
        return;
    }

    if (!imageFile) {
        showToast('Please upload an image first.', 'error');
        return;
    }

    const analyzeButton = document.getElementById('analyzeImageButton');
    const responseDiv = document.getElementById('image_analysis_response');
    const resultsDiv = document.getElementById('imageAnalysisResults');

    // Show loading state
    analyzeButton.disabled = true;
    analyzeButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Analyzing...';

    resultsDiv.style.display = 'block';
    responseDiv.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem; color: #6b7280;"></i>Analyzing nutritional content...';

    try {
        const imageBase64 = await fileToBase64(imageFile);

        // Use the dedicated image analysis endpoint on ollama-proxy
        const response = await fetch('/ai/api/image/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Client-ID': clientId,
                'X-Client-Secret': clientSecret
            },
            body: JSON.stringify({
                model: model,
                image: imageBase64
            })
        });

        const data = await response.json();

        if (response.ok) {
            // The new endpoint returns structured nutrition data directly
            if (data.error) {
                responseDiv.innerHTML = `
                    <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 0.5rem; padding: 1rem; color: #dc2626;">
                        <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 500; margin-bottom: 0.5rem;">
                            <i class="fas fa-exclamation-triangle"></i>
                            Analysis Failed
                        </div>
                        <div style="font-size: 0.875rem;">${data.error}</div>
                        ${data.message ? `<div style="font-size: 0.75rem; margin-top: 0.5rem; opacity: 0.8;">${data.message}</div>` : ''}
                    </div>
                `;
            } else {
                displayModernNutritionResults(data, model, responseDiv);
            }
        } else {
            responseDiv.innerHTML = `
                <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 0.5rem; padding: 1rem; color: #dc2626;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                        <i class="fas fa-exclamation-triangle"></i>
                        Request Failed: ${data.error || 'Unknown error occurred'}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        responseDiv.innerHTML = `
            <div style="background: #fee2e2; border: 1px solid #fecaca; border-radius: 0.5rem; padding: 1rem; color: #dc2626;">
                <div style="display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                    <i class="fas fa-exclamation-triangle"></i>
                    Network Error: ${error.message}
                </div>
            </div>
        `;
    } finally {
        // Reset button state
        analyzeButton.disabled = false;
        analyzeButton.innerHTML = '<i class="fas fa-search" style="margin-right: 0.5rem;"></i>Analyze Nutrition';
    }
}

// Display modern nutrition results
function displayModernNutritionResults(nutrition, model, targetDiv) {
    const html = `
        <div style="background: white; border: 1px solid #e5e7eb; border-radius: 0.5rem; overflow: hidden;">
            <div style="background: #f9fafb; padding: 1rem; border-bottom: 1px solid #e5e7eb;">
                <div style="display: flex; justify-content: between; align-items: center;">
                    <h5 style="font-size: 1rem; font-weight: 600; color: #111827; margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-chart-bar" style="color: #8b5cf6;"></i>
                        ${nutrition.name || 'Food Item'}
                    </h5>
                    <span style="font-size: 0.75rem; color: #6b7280;">${new Date().toLocaleTimeString()}</span>
                </div>
                <p style="font-size: 0.875rem; color: #6b7280; margin: 0.25rem 0 0 0;">
                    Serving Size: ${nutrition.serving_size || 'Not specified'}
                </p>
            </div>

            <div style="padding: 1rem;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1rem;">
                    <div>
                        <h6 style="font-size: 0.875rem; font-weight: 600; color: #111827; margin: 0 0 0.75rem 0;">Macronutrients</h6>
                        <div style="space-y: 0.5rem;">
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Calories</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.calories || 0} kcal</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Protein</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.protein || 0} g</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Fat</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.fat || 0} g</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Carbohydrates</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.carbs || 0} g</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h6 style="font-size: 0.875rem; font-weight: 600; color: #111827; margin: 0 0 0.75rem 0;">Vitamins & Minerals</h6>
                        <div style="space-y: 0.5rem;">
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Sodium</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.sodium || 0} mg</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Fiber</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.fiber || 0} g</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Sugar</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.sugar || 0} g</span>
                            </div>
                            <div style="display: flex; justify-content: between; padding: 0.25rem 0;">
                                <span style="color: #6b7280; font-size: 0.875rem;">Calcium</span>
                                <span style="font-weight: 500; color: #111827; font-size: 0.875rem;">${nutrition.calcium || 0} mg</span>
                            </div>
                        </div>
                    </div>
                </div>

                <details style="margin-top: 1rem;">
                    <summary style="cursor: pointer; font-size: 0.875rem; color: #6b7280; padding: 0.5rem; background: #f9fafb; border-radius: 0.375rem;">
                        <i class="fas fa-code" style="margin-right: 0.5rem;"></i>View Raw Data
                    </summary>
                    <pre style="margin-top: 0.5rem; padding: 1rem; background: #f9fafb; border-radius: 0.375rem; font-size: 0.75rem; overflow-x: auto;"><code>${JSON.stringify(nutrition, null, 2)}</code></pre>
                </details>
            </div>
        </div>
    `;

    targetDiv.innerHTML = html;
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Update send button state when inputs change
    ['client_id', 'client_secret', 'selected_model'].forEach(id => {
        document.getElementById(id).addEventListener('input', updateSendButtonState);
        document.getElementById(id).addEventListener('change', updateSendButtonState);
    });

    // Auto-test connection when both credentials are provided
    let connectionTestTimeout;
    function scheduleConnectionTest() {
        clearTimeout(connectionTestTimeout);
        connectionTestTimeout = setTimeout(() => {
            const clientId = document.getElementById('client_id').value;
            const clientSecret = document.getElementById('client_secret').value;

            if (clientId && clientSecret && !isConnected) {
                testConnection();
            }
        }, 1000); // Wait 1 second after user stops typing
    }

    document.getElementById('client_id').addEventListener('input', scheduleConnectionTest);
    document.getElementById('client_secret').addEventListener('input', scheduleConnectionTest);
});
</script>
{% endblock %}
