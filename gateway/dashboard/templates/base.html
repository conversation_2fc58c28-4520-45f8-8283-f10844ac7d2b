<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Calcounta API Dashboard{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* OpenAI Platform color palette */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #f1f3f4;
            --bg-card: #ffffff;
            --bg-card-hover: #f8f9fa;
            --bg-sidebar: #f8f9fa;
            --bg-code: #f6f8fa;
            --border-primary: #e1e5e9;
            --border-secondary: #d0d7de;
            --border-accent: #10a37f;
            --text-primary: #2d333a;
            --text-secondary: #656d76;
            --text-muted: #8b949e;
            --text-disabled: #a8b3ba;
            --text-inverse: #ffffff;
            --accent-primary: #10a37f;
            --accent-primary-hover: #0d8f72;
            --accent-primary-light: #e6f7f4;
            --accent-secondary: #6366f1;
            --accent-danger: #dc2626;
            --accent-warning: #f59e0b;
            --accent-success: #10a37f;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.5;
            font-size: 14px;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* OpenAI Platform Navigation */
        .navbar {
            background-color: var(--bg-primary);
            border-bottom: 1px solid var(--border-primary);
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
            backdrop-filter: blur(8px);
        }

        .navbar-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
        }

        .navbar-brand {
            font-weight: 600;
            font-size: 1.125rem;
            color: var(--text-primary) !important;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .navbar-brand:hover {
            color: var(--accent-primary) !important;
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-link {
            color: var(--text-secondary) !important;
            text-decoration: none;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius-md);
            transition: all 0.15s ease;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .nav-link:hover {
            color: var(--text-primary) !important;
            background-color: var(--bg-secondary);
        }

        .nav-link.active {
            color: var(--accent-primary) !important;
            background-color: var(--accent-primary-light);
        }

        /* OpenAI Platform Layout */
        .main-layout {
            display: flex;
            min-height: calc(100vh - 64px);
        }

        .sidebar {
            width: 280px;
            background-color: var(--bg-sidebar);
            border-right: 1px solid var(--border-primary);
            padding: 1.5rem 0;
            overflow-y: auto;
            position: sticky;
            top: 64px;
            height: calc(100vh - 64px);
        }

        .sidebar-nav {
            padding: 0 1.5rem;
        }

        .sidebar-section {
            margin-bottom: 2rem;
        }

        .sidebar-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.75rem;
            padding: 0 0.75rem;
        }

        .sidebar-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .sidebar-link::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: var(--accent-primary);
            transform: scaleY(0);
            transition: transform 0.2s ease;
            border-radius: 0 2px 2px 0;
        }

        .sidebar-link:hover {
            color: var(--text-primary);
            background-color: var(--bg-card-hover);
            transform: translateX(4px);
        }

        .sidebar-link:hover .sidebar-icon {
            transform: scale(1.1);
        }

        .sidebar-link.active {
            color: var(--accent-primary);
            background-color: var(--accent-primary-light);
            font-weight: 600;
            transform: translateX(4px);
        }

        .sidebar-link.active::before {
            transform: scaleY(1);
        }

        .sidebar-link.active .sidebar-icon {
            transform: scale(1.1);
            color: var(--accent-primary);
        }

        .sidebar-icon {
            width: 1rem;
            height: 1rem;
            flex-shrink: 0;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .content-area {
            flex: 1;
            padding: 2rem;
            max-width: calc(100% - 280px);
        }

        .content-container {
            max-width: 1024px;
            margin: 0 auto;
        }

        /* Enhanced Cards */
        .card {
            background-color: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
            animation: fadeIn 0.5s ease-out;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(16, 163, 127, 0.05), transparent);
            transition: left 0.5s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
            border-color: var(--accent-primary);
        }

        .card:hover::before {
            left: 100%;
        }

        .card-header {
            background: none !important;
            border: none !important;
            padding: 0 0 1rem 0 !important;
            margin-bottom: 1.5rem;
            border-bottom: 1px solid var(--border-secondary) !important;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-subtitle {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin: 0.5rem 0 0 0;
        }

        .card-body {
            padding: 0 !important;
        }

        /* Enhanced Buttons with Animations */
        .btn {
            padding: 0.625rem 1rem;
            border-radius: var(--radius-md);
            border: 1px solid transparent;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: center;
            line-height: 1.25;
            position: relative;
            overflow: hidden;
            transform: translateZ(0);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .btn:active::before {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-primary-hover) 100%) !important;
            color: var(--text-inverse) !important;
            border-color: var(--accent-primary) !important;
            box-shadow: 0 2px 4px rgba(16, 163, 127, 0.2);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--accent-primary-hover) 0%, var(--accent-primary) 100%) !important;
            border-color: var(--accent-primary-hover) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 163, 127, 0.3);
        }

        .btn-primary:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(16, 163, 127, 0.2);
        }

        .btn-secondary {
            background-color: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
            border-color: var(--border-primary) !important;
        }

        .btn-secondary:hover {
            background-color: var(--bg-tertiary) !important;
            border-color: var(--border-secondary) !important;
        }

        .btn-outline {
            background-color: transparent !important;
            color: var(--text-primary) !important;
            border-color: var(--border-primary) !important;
        }

        .btn-outline:hover {
            background-color: var(--bg-secondary) !important;
            border-color: var(--border-secondary) !important;
        }

        .btn-danger {
            background-color: var(--accent-danger) !important;
            color: var(--text-inverse) !important;
            border-color: var(--accent-danger) !important;
        }

        .btn-danger:hover {
            background-color: #b91c1c !important;
            border-color: #b91c1c !important;
        }

        .btn-warning {
            background-color: var(--accent-warning) !important;
            color: var(--text-inverse) !important;
            border-color: var(--accent-warning) !important;
        }

        .btn-success {
            background-color: var(--accent-success) !important;
            color: var(--text-inverse) !important;
            border-color: var(--accent-success) !important;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }
        /* OpenAI Platform Forms */
        .form-control {
            background-color: var(--bg-primary) !important;
            border: 1px solid var(--border-primary) !important;
            border-radius: var(--radius-md);
            padding: 0.75rem 1rem;
            color: var(--text-primary) !important;
            font-size: 0.875rem;
            transition: all 0.15s ease;
            line-height: 1.25;
        }

        .form-control:focus {
            outline: none !important;
            border-color: var(--accent-primary) !important;
            box-shadow: 0 0 0 3px rgba(16, 163, 127, 0.1) !important;
        }

        .form-control::placeholder {
            color: var(--text-muted) !important;
        }

        .form-label {
            color: var(--text-primary) !important;
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
            font-size: 0.875rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-text {
            color: var(--text-muted);
            font-size: 0.8125rem;
            margin-top: 0.25rem;
        }

        /* Input groups */
        .input-group .form-control {
            border-right: none;
        }

        .input-group .btn {
            border-left: none;
            border-color: var(--border-primary);
        }

        /* Code blocks */
        .code-block {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-primary);
            border-radius: 0.5rem;
            padding: 1rem;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.8125rem;
            white-space: pre-wrap;
            overflow-x: auto;
            color: var(--text-secondary);
        }

        /* Alerts */
        .alert {
            border-radius: 0.75rem;
            border: 1px solid;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: rgba(16, 185, 129, 0.1);
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        .alert-warning {
            background-color: rgba(245, 158, 11, 0.1);
            border-color: var(--accent-warning);
            color: var(--accent-warning);
        }

        .alert-danger {
            background-color: rgba(239, 68, 68, 0.1);
            border-color: var(--accent-red);
            color: var(--accent-red);
        }

        .alert-info {
            background-color: rgba(59, 130, 246, 0.1);
            border-color: var(--accent-blue);
            color: var(--accent-blue);
        }

        /* Copy button animations and effects */
        @keyframes copySuccess {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Copy button specific styles */
        .copy-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .copy-btn:hover {
            transform: translateY(-1px);
        }

        .copy-btn.success {
            animation: copySuccess 0.3s ease-in-out;
        }

        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 24px;
            right: 24px;
            z-index: 10000;
            pointer-events: none;
        }

        .toast {
            pointer-events: auto;
            margin-bottom: 12px;
            animation: slideInRight 0.4s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .toast.removing {
            animation: slideInRight 0.4s cubic-bezier(0.16, 1, 0.3, 1) reverse;
        }

        /* Footer */
        .footer {
            background-color: var(--bg-secondary);
            border-top: 1px solid var(--border-primary);
            margin-top: auto;
            color: var(--text-muted);
        }

        /* Layout */
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
        }

        /* Utilities */
        .text-muted {
            color: var(--text-muted) !important;
        }

        .text-success {
            color: var(--accent-green) !important;
        }

        .text-danger {
            color: var(--accent-red) !important;
        }

        .text-warning {
            color: var(--accent-warning) !important;
        }

        .text-primary {
            color: var(--text-primary) !important;
        }

        .text-secondary {
            color: var(--text-secondary) !important;
        }

        /* Status badges */
        .badge {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .bg-success {
            background-color: var(--accent-green) !important;
        }

        .bg-secondary {
            background-color: var(--text-muted) !important;
        }

        .bg-primary {
            background-color: var(--accent-blue) !important;
        }

        .bg-info {
            background-color: var(--accent-blue) !important;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .content-area {
                max-width: calc(100% - 240px);
            }
        }

        @media (max-width: 768px) {
            .main-layout {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                position: static;
                border-right: none;
                border-bottom: 1px solid var(--border-primary);
            }

            .content-area {
                max-width: 100%;
                padding: 1rem;
            }

            .navbar-container {
                padding: 0 1rem;
            }

            .card {
                padding: 1rem;
            }

            .btn {
                padding: 0.625rem 1rem;
                font-size: 0.8125rem;
            }

            /* Hide sidebar sections on mobile for cleaner look */
            .sidebar-section:not(:first-child) {
                display: none;
            }
        }

        @media (max-width: 640px) {
            .content-container {
                padding: 0;
            }

            /* Stack table cells on mobile */
            table, thead, tbody, th, td, tr {
                display: block;
            }

            thead tr {
                position: absolute;
                top: -9999px;
                left: -9999px;
            }

            tr {
                border: 1px solid var(--border-primary);
                border-radius: var(--radius-md);
                margin-bottom: 1rem;
                padding: 1rem;
                background: var(--bg-card);
            }

            td {
                border: none;
                padding: 0.5rem 0;
                position: relative;
                padding-left: 30%;
            }

            td:before {
                content: attr(data-label);
                position: absolute;
                left: 0;
                width: 25%;
                padding-right: 10px;
                white-space: nowrap;
                font-weight: 500;
                color: var(--text-muted);
                font-size: 0.8125rem;
            }
        }

        /* API Key Cards */
        .api-key-card {
            transition: all 0.2s ease;
            border: 1px solid var(--border-primary);
        }

        .api-key-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent-green);
        }

        /* Copy button feedback */
        .copy-success {
            background-color: var(--accent-green) !important;
            border-color: var(--accent-green) !important;
        }

        /* Modal overrides */
        .modal-content {
            background-color: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: 0.75rem;
        }

        .modal-header {
            border-bottom: 1px solid var(--border-secondary);
        }

        .modal-title {
            color: var(--text-primary);
        }

        .btn-close {
            filter: invert(1);
        }

        /* Dropdown overrides */
        .dropdown-menu {
            background-color: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: 0.5rem;
        }

        .dropdown-item {
            color: var(--text-secondary);
        }

        .dropdown-item:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .dropdown-divider {
            border-color: var(--border-secondary);
        }

        /* Enhanced dropdown menu styling */
        [id^="keyMenu"] {
            background: rgba(255, 255, 255, 0.98) !important;
            backdrop-filter: blur(12px) !important;
            -webkit-backdrop-filter: blur(12px) !important;
            border: 1px solid #d1d5db !important;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1) !important;
            z-index: 9999 !important;
            position: fixed !important;
            border-radius: 0.5rem !important;
            min-width: 180px !important;
            max-width: 250px !important;
        }

        /* Ensure dropdown appears above everything */
        [id^="keyMenu"] * {
            z-index: inherit;
        }

        /* Dark mode support for dropdown */
        @media (prefers-color-scheme: dark) {
            [id^="keyMenu"] {
                background: rgba(30, 30, 30, 0.98) !important;
                border-color: rgba(255, 255, 255, 0.1) !important;
            }
        }

        /* Prevent dropdown from being clipped by table containers */
        .table-responsive {
            overflow: visible !important;
        }

        /* Ensure table cells don't clip dropdown */
        td {
            overflow: visible !important;
        }

        /* Add subtle animation for dropdown positioning */
        [id^="keyMenu"] {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        /* Modern animations */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Modern table styling */
        .modern-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .modern-table th {
            background: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
            padding: 1rem 1.5rem;
        }

        .modern-table td {
            border-bottom: 1px solid #f3f4f6;
            padding: 1rem 1.5rem;
        }

        .modern-table tr:hover {
            background-color: #f9fafb;
        }

        /* Modern button styles */
        .modern-btn {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .modern-btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .modern-btn-secondary:hover {
            background: #e5e7eb;
            color: #111827;
        }

        /* Modern Playground Styles */
        .playground-container {
            display: grid;
            grid-template-columns: 320px 1fr;
            gap: 2rem;
            height: calc(100vh - 200px);
            min-height: 600px;
        }

        .playground-sidebar {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            overflow-y: auto;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .playground-main {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        /* Responsive design for playground */
        @media (max-width: 1024px) {
            .playground-container {
                grid-template-columns: 280px 1fr;
                gap: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .playground-container {
                grid-template-columns: 1fr;
                gap: 1rem;
                height: auto;
            }

            .playground-sidebar {
                order: 2;
                max-height: 400px;
            }

            .playground-main {
                order: 1;
                min-height: 500px;
            }
        }

        /* Chat message animations */
        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-message {
            animation: messageSlideIn 0.3s ease-out;
        }

        /* Custom scrollbar for chat */
        #chatMessages::-webkit-scrollbar {
            width: 6px;
        }

        #chatMessages::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        #chatMessages::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        #chatMessages::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Enhanced Animations */
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -8px, 0);
            }
            70% {
                transform: translate3d(0, -4px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        @keyframes ripple {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Loading states */
        .loading {
            animation: pulse 2s infinite;
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* Skeleton loading */
        .skeleton {
            background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-card-hover) 50%, var(--bg-tertiary) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        /* Focus states for accessibility */
        *:focus {
            outline: 2px solid var(--accent-green);
            outline-offset: 2px;
        }

        .btn:focus,
        .form-control:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        /* Stats Cards */
        .stats-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--accent-primary);
        }

        .stats-card:hover .stats-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stats-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Responsive stats grid */
        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr !important;
                gap: 1rem !important;
            }
        }

        @media (max-width: 640px) {
            .stats-card {
                padding: 1rem !important;
            }

            .stats-card .stats-number {
                font-size: 1.5rem !important;
            }

            /* Hide Client ID column on mobile to save space */
            th:nth-child(2), td:nth-child(2) {
                display: none;
            }

            /* Adjust button spacing on mobile */
            .api-key-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }

            .api-key-buttons button {
                width: 100%;
                justify-content: center;
            }
        }

        /* Print styles */
        @media print {
            .navbar,
            .footer,
            .btn,
            button {
                display: none !important;
            }

            .card {
                border: 1px solid #000 !important;
                box-shadow: none !important;
            }
        }
    </style>
</head>
<body>
    <!-- OpenAI Platform Navigation -->
    <nav class="navbar">
        <div class="navbar-container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
                Calcounta API
            </a>

            <div class="navbar-nav">
                {% if session.get('access_token') %}
                    <a class="nav-link" href="{{ url_for('dashboard') }}">Dashboard</a>
                    <a class="nav-link" href="{{ url_for('chat') }}">Playground</a>
                    <div style="position: relative; display: inline-block;">
                        <button class="nav-link" style="background: none; border: none; cursor: pointer; display: flex; align-items: center; gap: 0.5rem;" onclick="toggleUserMenu()">
                            <div style="width: 1.5rem; height: 1.5rem; background: var(--accent-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                {{ session.get('user', {}).get('username', 'U')[0].upper() }}
                            </div>
                            {{ session.get('user', {}).get('username', 'User') }}
                            <i class="fas fa-chevron-down" style="font-size: 0.75rem;"></i>
                        </button>
                        <div id="userMenu" style="display: none; position: absolute; top: 100%; right: 0; background: var(--bg-card); border: 1px solid var(--border-primary); border-radius: var(--radius-lg); min-width: 200px; box-shadow: var(--shadow-xl); z-index: 1000; margin-top: 0.5rem;">
                            <div style="padding: 0.75rem 1rem; border-bottom: 1px solid var(--border-secondary);">
                                <div style="font-weight: 500; color: var(--text-primary);">{{ session.get('user', {}).get('username', 'User') }}</div>
                                <div style="font-size: 0.8125rem; color: var(--text-muted);">{{ session.get('user', {}).get('email', '') }}</div>
                            </div>
                            <a href="{{ url_for('dashboard') }}" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem 1rem; color: var(--text-secondary); text-decoration: none; transition: all 0.15s ease;">
                                <i class="fas fa-tachometer-alt"></i>Dashboard
                            </a>
                            <a href="{{ url_for('logout') }}" style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem 1rem; color: var(--accent-danger); text-decoration: none; transition: all 0.15s ease;">
                                <i class="fas fa-sign-out-alt"></i>Sign out
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a class="nav-link" href="{{ url_for('login') }}">Sign in</a>
                    <a class="btn btn-primary" href="{{ url_for('register') }}">Sign up</a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
        <div class="container mt-3">
            {% for category, message in messages %}
            <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'info' if category == 'info' else 'success' }} alert-dismissible fade show" role="alert">
                {% if category == 'error' %}
                    <i class="fas fa-exclamation-triangle me-2"></i>
                {% elif category == 'warning' %}
                    <i class="fas fa-exclamation-circle me-2"></i>
                {% elif category == 'info' %}
                    <i class="fas fa-info-circle me-2"></i>
                {% else %}
                    <i class="fas fa-check-circle me-2"></i>
                {% endif %}
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}
    {% endwith %}

    <!-- Main Layout -->
    <div class="main-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <nav class="sidebar-nav">
                <div class="sidebar-section">
                    <a href="{{ url_for('dashboard') }}" class="sidebar-link {{ 'active' if request.endpoint == 'dashboard' else '' }}">
                        <i class="fas fa-key sidebar-icon"></i>
                        API Keys
                    </a>
                    <a href="{{ url_for('chat') }}" class="sidebar-link {{ 'active' if request.endpoint == 'chat' else '' }}">
                        <i class="fas fa-robot sidebar-icon"></i>
                        Playground
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Content Area -->
        <main class="content-area">
            <div class="content-container">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- Footer -->
    <footer style="background: var(--bg-secondary); border-top: 1px solid var(--border-primary); margin-top: auto; padding: 1.5rem 0;">
        <div style="max-width: 1280px; margin: 0 auto; padding: 0 1.5rem;">
            <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                <div style="color: var(--text-muted); font-size: 0.875rem;">
                    © 2024 Calcounta API Gateway. All rights reserved.
                </div>
                <div style="display: flex; gap: 1.5rem; font-size: 0.875rem;">
                    <a href="#" style="color: var(--text-muted); text-decoration: none; transition: color 0.15s ease;">Documentation</a>
                    <a href="#" style="color: var(--text-muted); text-decoration: none; transition: color 0.15s ease;">Support</a>
                    <a href="#" style="color: var(--text-muted); text-decoration: none; transition: color 0.15s ease;">Privacy</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // User menu toggle
        function toggleUserMenu() {
            const menu = document.getElementById('userMenu');
            menu.style.display = menu.style.display === 'none' ? 'block' : 'none';
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('userMenu');
            const button = event.target.closest('button');
            if (menu && !button) {
                menu.style.display = 'none';
            }
        });

        // Copy to clipboard with enhanced feedback
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(element.value).then(function() {
                // Enhanced success feedback
                const button = element.nextElementSibling;
                const originalIcon = button.innerHTML;
                const originalClasses = button.className;

                button.innerHTML = '<i class="fas fa-check"></i>';
                button.className = button.className.replace('btn-outline-secondary', 'btn-success copy-success');

                setTimeout(function() {
                    button.innerHTML = originalIcon;
                    button.className = originalClasses;
                }, 2000);
            });
        }
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
