{% extends "base.html" %}

{% block title %}API Keys - Calcounta{% endblock %}

{% block content %}
<!-- Modern Page Header -->
<div class="page-header" style="margin-bottom: 2rem;">
    <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem;">
        <div>
            <h1 style="font-size: 2rem; font-weight: 600; color: #111827; margin: 0 0 0.5rem 0; letter-spacing: -0.025em;">
                API Management
            </h1>
            <p style="color: #6b7280; font-size: 1rem; margin: 0; font-weight: 400;">
                Secure access to the Calcounta AI platform
            </p>
        </div>
        <div style="display: flex; gap: 0.75rem;">
            <button onclick="showCreateKeyModal()" style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); display: flex; align-items: center; gap: 0.5rem;"
                    onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 2px 0 rgba(0, 0, 0, 0.05)'">
                <i class="fas fa-plus" style="font-size: 0.875rem;"></i>
                Create API Key
            </button>
        </div>
    </div>
</div>

<!-- Success Alert for New API Key -->
{% if session.get('new_api_key') %}
<div style="background: var(--accent-primary-light); border: 1px solid var(--accent-primary); border-radius: var(--radius-lg); padding: 1.5rem; margin-bottom: 2rem; position: relative;">
    <button type="button" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: var(--accent-primary); cursor: pointer; font-size: 1rem;">
        <i class="fas fa-times"></i>
    </button>

    <div style="display: flex; align-items: flex-start; gap: 1rem; margin-bottom: 1.5rem;">
        <div style="background: var(--accent-primary); border-radius: 50%; width: 2rem; height: 2rem; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
            <i class="fas fa-check" style="color: white; font-size: 0.875rem;"></i>
        </div>
        <div>
            <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.5rem 0;">
                API key created
            </h3>
            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                Please save this secret key somewhere safe and accessible. For security reasons, <strong>you won't be able to view it again</strong> through your Calcounta account. If you lose this secret key, you'll need to generate a new one.
            </p>
        </div>
    </div>

    <div style="background: var(--bg-code); border: 1px solid var(--border-secondary); border-radius: var(--radius-md); padding: 1rem; position: relative; margin-bottom: 1rem;">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 1rem; align-items: center;">
            <div style="font-size: 0.8125rem; color: var(--text-muted); font-weight: 500;">Client ID</div>
            <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.875rem; color: var(--text-primary); background: none; padding: 0;" id="newApiKeyClientId">{{ session.new_api_key.client_id|trim }}</code>
            <button onclick="copyToClipboard('newApiKeyClientId')" style="background: none; border: none; color: var(--text-muted); cursor: pointer; padding: 0.25rem; border-radius: var(--radius-sm); transition: all 0.15s ease;" title="Copy Client ID to clipboard">
                <i class="fas fa-copy"></i>
            </button>
        </div>
    </div>

    <div style="background: var(--bg-code); border: 1px solid var(--border-secondary); border-radius: var(--radius-md); padding: 1rem; position: relative;">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 1rem; align-items: center;">
            <div style="font-size: 0.8125rem; color: var(--text-muted); font-weight: 500;">Client Secret</div>
            <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.875rem; color: var(--text-primary); background: none; padding: 0;" id="newApiKeySecret">{{ session.new_api_key.client_secret|trim }}</code>
            <button onclick="copyToClipboard('newApiKeySecret')" style="background: none; border: none; color: var(--text-muted); cursor: pointer; padding: 0.25rem; border-radius: var(--radius-sm); transition: all 0.15s ease;" title="Copy Client Secret to clipboard">
                <i class="fas fa-copy"></i>
            </button>
        </div>
    </div>
</div>
{% set _ = session.pop('new_api_key', None) %}
{% endif %}

<!-- Warning Alert for Regenerated API Key -->
{% if session.get('regenerated_api_key') %}
<div style="background: #fef3cd; border: 1px solid var(--accent-warning); border-radius: var(--radius-lg); padding: 1.5rem; margin-bottom: 2rem; position: relative;">
    <button type="button" onclick="this.parentElement.style.display='none'" style="position: absolute; top: 1rem; right: 1rem; background: none; border: none; color: var(--accent-warning); cursor: pointer; font-size: 1rem;">
        <i class="fas fa-times"></i>
    </button>

    <div style="display: flex; align-items: flex-start; gap: 1rem; margin-bottom: 1.5rem;">
        <div style="background: var(--accent-warning); border-radius: 50%; width: 2rem; height: 2rem; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
            <i class="fas fa-sync-alt" style="color: white; font-size: 0.875rem;"></i>
        </div>
        <div>
            <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.5rem 0;">
                API key regenerated
            </h3>
            <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem; line-height: 1.5;">
                A new secret key has been generated for "<strong>{{ session.regenerated_api_key.name }}</strong>". The previous key is now invalid. Please save this new secret key somewhere safe.
            </p>
        </div>
    </div>

    <div style="background: var(--bg-code); border: 1px solid var(--border-secondary); border-radius: var(--radius-md); padding: 1rem; position: relative; margin-bottom: 1rem;">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 1rem; align-items: center;">
            <div style="font-size: 0.8125rem; color: var(--text-muted); font-weight: 500;">Client ID</div>
            <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.875rem; color: var(--text-primary); background: none; padding: 0;" id="regenApiKeyClientId">{{ session.regenerated_api_key.client_id|trim }}</code>
            <button onclick="copyToClipboard('regenApiKeyClientId')" style="background: none; border: none; color: var(--text-muted); cursor: pointer; padding: 0.25rem; border-radius: var(--radius-sm); transition: all 0.15s ease;" title="Copy Client ID to clipboard" id="copyBtnClientId">
                <i class="fas fa-copy"></i>
            </button>
        </div>
    </div>

    <div style="background: var(--bg-code); border: 1px solid var(--border-secondary); border-radius: var(--radius-md); padding: 1rem; position: relative;">
        <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 1rem; align-items: center;">
            <div style="font-size: 0.8125rem; color: var(--text-muted); font-weight: 500;">Client Secret</div>
            <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.875rem; color: var(--text-primary); background: none; padding: 0;" id="regenApiKeySecret">{{ session.regenerated_api_key.client_secret|trim }}</code>
            <button onclick="copyToClipboard('regenApiKeySecret')" style="background: none; border: none; color: var(--text-muted); cursor: pointer; padding: 0.25rem; border-radius: var(--radius-sm); transition: all 0.15s ease;" title="Copy Client Secret to clipboard" id="copyBtnSecret">
                <i class="fas fa-copy"></i>
            </button>
        </div>
    </div>
</div>
{% set _ = session.pop('regenerated_api_key', None) %}
{% endif %}

<!-- Modern Stats Grid -->
<div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
    <!-- API Keys Card -->
    <div class="modern-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1.5rem; transition: all 0.2s ease; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);"
         onmouseover="this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'"
         onmouseout="this.style.boxShadow='0 1px 3px 0 rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(0)'">
        <div style="display: flex; items-center: space-between;">
            <div>
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                    <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); border-radius: 0.5rem; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-key" style="color: white; font-size: 1rem;"></i>
                    </div>
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 500; color: #6b7280; margin: 0;">API Keys</h3>
                        <p style="font-size: 1.875rem; font-weight: 600; color: #111827; margin: 0; line-height: 1;">{{ api_keys|length }}</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span style="background: #dbeafe; color: #1d4ed8; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500;">
                        Active
                    </span>
                    <span style="color: #6b7280; font-size: 0.875rem;">Secure tokens</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Models Card -->
    <div class="modern-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1.5rem; transition: all 0.2s ease; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);"
         onmouseover="this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'"
         onmouseout="this.style.boxShadow='0 1px 3px 0 rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(0)'">
        <div style="display: flex; items-center: space-between;">
            <div>
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                    <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 0.5rem; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-robot" style="color: white; font-size: 1rem;"></i>
                    </div>
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 500; color: #6b7280; margin: 0;">AI Models</h3>
                        <p style="font-size: 1.875rem; font-weight: 600; color: #111827; margin: 0; line-height: 1;" id="modelCount">0</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span style="background: #ede9fe; color: #7c3aed; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500;" id="connectionStatus">
                        <i class="fas fa-wifi" style="margin-right: 0.25rem;"></i>
                        Connected
                    </span>
                    <span style="color: #6b7280; font-size: 0.875rem;">Ready to use</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Service Status Card -->
    <div class="modern-card" style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; padding: 1.5rem; transition: all 0.2s ease; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);"
         onmouseover="this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(-2px)'"
         onmouseout="this.style.boxShadow='0 1px 3px 0 rgba(0, 0, 0, 0.1)'; this.style.transform='translateY(0)'">
        <div style="display: flex; items-center: space-between;">
            <div>
                <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                    <div style="background: linear-gradient(135deg, #10b981, #059669); border-radius: 0.5rem; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-check-circle" style="color: white; font-size: 1rem;"></i>
                    </div>
                    <div>
                        <h3 style="font-size: 0.875rem; font-weight: 500; color: #6b7280; margin: 0;">Service Status</h3>
                        <p style="font-size: 1.875rem; font-weight: 600; color: #111827; margin: 0; line-height: 1;">99.9%</p>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span style="background: #d1fae5; color: #065f46; padding: 0.25rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500;">
                        <i class="fas fa-circle" style="margin-right: 0.25rem; font-size: 0.5rem;"></i>
                        Operational
                    </span>
                    <span style="color: #6b7280; font-size: 0.875rem;">All systems online</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Model Status Card (Hidden by default, shown when downloading) -->
<div id="modelStatusCard" style="display: none; background: var(--bg-card); border: 1px solid var(--border-primary); border-radius: var(--radius-lg); padding: 1.5rem; box-shadow: var(--shadow-sm); margin-bottom: 2rem; animation: slideUp 0.3s ease-out;">
    <div id="modelStatusContent">
        <!-- Dynamic content will be inserted here by JavaScript -->
    </div>
</div>

<!-- API Keys Section Header -->
<div style="margin-bottom: 1.5rem;">
    <h2 style="font-size: 1.25rem; font-weight: 600; color: #111827; margin: 0 0 0.5rem 0;">
        API Keys
    </h2>
    <p style="color: #6b7280; font-size: 0.875rem; margin: 0;">
        Manage your API access credentials
    </p>
</div>

<!-- Modern API Keys Table -->
{% if api_keys %}
<div style="background: white; border: 1px solid #e5e7eb; border-radius: 0.75rem; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);">
    <div style="overflow-x: auto; border-radius: 0.75rem;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="background: #f9fafb; border-bottom: 1px solid #e5e7eb;">
                    <th style="padding: 1rem 1.5rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Name
                    </th>
                    <th style="padding: 1rem 1.5rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Client ID
                    </th>
                    <th style="padding: 1rem 1.5rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Secret Key
                    </th>
                    <th style="padding: 1rem 1.5rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Created
                    </th>
                    <th style="padding: 1rem 1.5rem; text-align: left; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Last Used
                    </th>
                    <th style="padding: 1rem 1.5rem; text-align: right; font-weight: 500; color: #374151; font-size: 0.875rem;">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for key in api_keys %}
                <tr style="border-bottom: 1px solid #f3f4f6; transition: all 0.2s ease;"
                    onmouseover="this.style.backgroundColor='#f9fafb'"
                    onmouseout="this.style.backgroundColor='transparent'">
                    <td style="padding: 1rem 1.5rem;" data-label="Name">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); border-radius: 0.5rem; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; flex-shrink: 0;">
                                <i class="fas fa-key" style="color: white; font-size: 0.75rem;"></i>
                            </div>
                            <div>
                                <div style="font-weight: 500; color: #111827; margin-bottom: 0.25rem; font-size: 0.875rem;">{{ key.name }}</div>
                                {% if key.description %}
                                <div style="font-size: 0.75rem; color: #6b7280;">{{ key.description }}</div>
                                {% endif %}
                                <div style="display: flex; align-items: center; gap: 0.5rem; margin-top: 0.25rem;">
                                    <span style="background: {{ '#dbeafe' if key.is_active else '#fee2e2' }}; color: {{ '#1d4ed8' if key.is_active else '#dc2626' }}; padding: 0.125rem 0.5rem; border-radius: 0.375rem; font-size: 0.75rem; font-weight: 500;">
                                        <i class="fas {{ 'fa-circle' if key.is_active else 'fa-times-circle' }}" style="margin-right: 0.25rem; font-size: 0.5rem;"></i>
                                        {{ 'Active' if key.is_active else 'Disabled' }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td style="padding: 1rem 1.5rem;" data-label="Client ID">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 0.5rem 0.75rem; flex: 1;">
                                <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.75rem; color: #374151; background: none; padding: 0;" id="clientId{{ loop.index }}Display">
                                    {{ key.client_id[:8] }}...{{ key.client_id[-4:] }}
                                </code>
                            </div>
                            <button onclick="toggleClientId('{{ loop.index }}')"
                                    style="background: #f3f4f6; border: 1px solid #d1d5db; color: #6b7280; cursor: pointer; padding: 0.375rem; border-radius: 0.375rem; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; width: 32px; height: 32px;"
                                    title="Show/Hide full Client ID"
                                    onmouseover="this.style.backgroundColor='#e5e7eb'; this.style.color='#374151'"
                                    onmouseout="this.style.backgroundColor='#f3f4f6'; this.style.color='#6b7280'"
                                    id="toggleBtn{{ loop.index }}">
                                <i class="fas fa-eye" style="font-size: 0.75rem;"></i>
                            </button>
                            <button onclick="copyToClipboard('clientId{{ loop.index }}')"
                                    style="background: #f3f4f6; border: 1px solid #d1d5db; color: #6b7280; cursor: pointer; padding: 0.375rem; border-radius: 0.375rem; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; width: 32px; height: 32px;"
                                    title="Copy Client ID"
                                    onmouseover="this.style.backgroundColor='#3b82f6'; this.style.color='white'"
                                    onmouseout="this.style.backgroundColor='#f3f4f6'; this.style.color='#6b7280'">
                                <i class="fas fa-copy" style="font-size: 0.75rem;"></i>
                            </button>
                            <input type="hidden" id="clientId{{ loop.index }}" value="{{ key.client_id|trim }}">
                        </div>
                    </td>
                    <td style="padding: 1rem 1.5rem;" data-label="Secret Key">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 0.5rem; padding: 0.5rem 0.75rem; flex: 1;">
                                <code style="font-family: 'SF Mono', Monaco, monospace; font-size: 0.75rem; color: #374151; background: none; padding: 0;">
                                    sk-...{{ key.client_id[-4:] }}
                                </code>
                            </div>
                            <button onclick="copyToClipboard('key{{ loop.index }}')"
                                    style="background: #f3f4f6; border: 1px solid #d1d5db; color: #6b7280; cursor: pointer; padding: 0.375rem; border-radius: 0.375rem; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; width: 32px; height: 32px;"
                                    title="Copy secret key"
                                    onmouseover="this.style.backgroundColor='#10b981'; this.style.color='white'"
                                    onmouseout="this.style.backgroundColor='#f3f4f6'; this.style.color='#6b7280'">
                                <i class="fas fa-copy" style="font-size: 0.75rem;"></i>
                            </button>
                            <input type="hidden" id="key{{ loop.index }}" value="{{ (key.client_secret if session.get('new_api_key') and session.new_api_key.client_id == key.client_id else key.client_id)|trim }}">
                        </div>
                    </td>
                    <td style="padding: 1rem 1.5rem;" data-label="Created">
                        <div style="color: #6b7280; font-size: 0.875rem;">
                            <div style="font-weight: 500; color: #111827; margin-bottom: 0.125rem;">
                                {% if key.created_at %}
                                    {% if key.created_at is string %}
                                        {{ key.created_at[:10] if key.created_at|length > 10 else key.created_at }}
                                    {% else %}
                                        {{ key.created_at.strftime('%b %d, %Y') }}
                                    {% endif %}
                                {% else %}
                                    Unknown
                                {% endif %}
                            </div>
                        </div>
                    </td>
                    <td style="padding: 1rem 1.5rem;" data-label="Last Used">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="background: {{ '#10b981' if key.last_used else '#d1d5db' }}; border-radius: 50%; width: 6px; height: 6px;"></div>
                            <div style="color: #6b7280; font-size: 0.875rem;">
                                <div style="font-weight: 500; color: #111827; margin-bottom: 0.125rem;">
                                    {% if key.last_used %}
                                        {% if key.last_used is string %}
                                            {{ key.last_used[:10] if key.last_used|length > 10 else key.last_used }}
                                        {% else %}
                                            {{ key.last_used.strftime('%b %d, %Y') }}
                                        {% endif %}
                                    {% else %}
                                        Never
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td style="padding: 1rem 1.5rem; text-align: right;" data-label="Actions">
                        <div style="position: relative; display: inline-block;">
                            <button onclick="toggleKeyMenu('{{ loop.index }}')"
                                    style="background: #f3f4f6; border: 1px solid #d1d5db; color: #6b7280; cursor: pointer; padding: 0.5rem; border-radius: 0.375rem; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; width: 32px; height: 32px;"
                                    onmouseover="this.style.backgroundColor='#e5e7eb'; this.style.color='#374151'"
                                    onmouseout="this.style.backgroundColor='#f3f4f6'; this.style.color='#6b7280'">
                                <i class="fas fa-ellipsis-h" style="font-size: 0.75rem;"></i>
                            </button>
                            <div id="keyMenu{{ loop.index }}" style="display: none; position: fixed; background: #ffffff; border: 1px solid #d1d5db; border-radius: 0.5rem; min-width: 180px; box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1); z-index: 9999; opacity: 0; transform: translateY(-10px); transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); overflow: hidden;">
                                <div style="padding: 0.75rem;">
                                    <button onclick="showRegenerateModal('{{ key.id }}', '{{ key.name }}')"
                                            style="display: flex; align-items: center; gap: 0.75rem; width: 100%; padding: 0.75rem 1rem; background: none; border: none; color: #374151; text-align: left; cursor: pointer; transition: all 0.2s ease; font-size: 0.875rem; border-radius: var(--radius-md); font-weight: 500;"
                                            onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#111827'; this.style.transform='translateX(4px)'"
                                            onmouseout="this.style.backgroundColor='transparent'; this.style.color='#374151'; this.style.transform='translateX(0)'">
                                        <i class="fas fa-sync-alt" style="color: var(--accent-primary); width: 16px;"></i>
                                        Regenerate Secret
                                    </button>
                                    <form method="POST" action="{{ url_for('toggle_api_key', key_id=key.id) }}" style="margin: 0;">
                                        <button type="submit"
                                                style="display: flex; align-items: center; gap: 0.75rem; width: 100%; padding: 0.75rem 1rem; background: none; border: none; color: #374151; text-align: left; cursor: pointer; transition: all 0.2s ease; font-size: 0.875rem; border-radius: var(--radius-md); font-weight: 500;"
                                                onmouseover="this.style.backgroundColor='#f3f4f6'; this.style.color='#111827'; this.style.transform='translateX(4px)'"
                                                onmouseout="this.style.backgroundColor='transparent'; this.style.color='#374151'; this.style.transform='translateX(0)'">
                                            <i class="fas {{ 'fa-pause' if key.is_active else 'fa-play' }}" style="color: {{ 'var(--accent-warning)' if key.is_active else 'var(--accent-primary)' }}; width: 16px;"></i>
                                            {{ 'Disable Key' if key.is_active else 'Enable Key' }}
                                        </button>
                                    </form>
                                    <div style="border-top: 1px solid #e5e7eb; margin: 0.5rem 0;"></div>
                                    <form method="POST" action="{{ url_for('delete_api_key', key_id=key.id) }}" onsubmit="return confirm('Are you sure you want to delete this API key? This action cannot be undone.')" style="margin: 0;">
                                        <button type="submit"
                                                style="display: flex; align-items: center; gap: 0.75rem; width: 100%; padding: 0.75rem 1rem; background: none; border: none; color: #dc2626; text-align: left; cursor: pointer; transition: all 0.2s ease; font-size: 0.875rem; border-radius: var(--radius-md); font-weight: 500;"
                                                onmouseover="this.style.backgroundColor='rgba(239, 68, 68, 0.1)'; this.style.transform='translateX(4px)'"
                                                onmouseout="this.style.backgroundColor='transparent'; this.style.transform='translateX(0)'">
                                            <i class="fas fa-trash" style="color: #dc2626; width: 16px;"></i>
                                            Delete Key
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% else %}
<div style="text-align: center; padding: 4rem 2rem; border: 2px dashed #e5e7eb; border-radius: 0.75rem; background: #fafafa;">
    <div style="background: linear-gradient(135deg, #3b82f6, #2563eb); border-radius: 50%; width: 4rem; height: 4rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem auto;">
        <i class="fas fa-key" style="font-size: 1.5rem; color: white;"></i>
    </div>
    <h3 style="font-size: 1.25rem; font-weight: 600; color: #111827; margin: 0 0 0.75rem 0;">No API keys yet</h3>
    <p style="color: #6b7280; margin: 0 0 2rem 0; font-size: 0.875rem; line-height: 1.6; max-width: 400px; margin-left: auto; margin-right: auto;">
        Create your first API key to start accessing the Calcounta AI platform. Your keys will appear here once created.
    </p>
    <button onclick="showCreateKeyModal()" style="background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 0.5rem; font-weight: 500; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease; display: inline-flex; align-items: center; gap: 0.5rem;"
            onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 6px -1px rgba(0, 0, 0, 0.1)'"
            onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
        <i class="fas fa-plus" style="font-size: 0.875rem;"></i>
        Create your first API key
    </button>
</div>
{% endif %}

<!-- Enhanced Create API Key Modal -->
<div id="createKeyModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.6); z-index: 1000; align-items: center; justify-content: center; backdrop-filter: blur(4px);">
    <div style="background: var(--bg-card); border: 1px solid var(--border-primary); border-radius: var(--radius-xl); max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: var(--shadow-xl); transform: scale(0.95); opacity: 0; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 2rem 2rem 1rem 2rem; border-bottom: 1px solid var(--border-secondary);">
            <div style="display: flex; align-items: center; gap: 1rem;">
                <div style="background: linear-gradient(135deg, var(--accent-primary), var(--accent-primary-hover)); border-radius: 12px; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center;">
                    <i class="fas fa-plus" style="color: white; font-size: 1rem;"></i>
                </div>
                <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--text-primary); margin: 0;">
                    Create new API key
                </h3>
            </div>
            <button onclick="hideCreateKeyModal()" style="background: var(--bg-secondary); border: 1px solid var(--border-primary); color: var(--text-muted); cursor: pointer; padding: 0.5rem; border-radius: var(--radius-md); transition: all 0.2s ease; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center;"
                    onmouseover="this.style.backgroundColor='var(--accent-danger)'; this.style.color='white'"
                    onmouseout="this.style.backgroundColor='var(--bg-secondary)'; this.style.color='var(--text-muted)'">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form method="POST" action="{{ url_for('create_api_key') }}">
            <div style="padding: 2rem;">
                <div style="margin-bottom: 1.5rem;">
                    <label for="name" style="display: block; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem; font-size: 0.875rem;">
                        <i class="fas fa-tag" style="margin-right: 0.5rem; color: var(--accent-primary);"></i>
                        API Key Name
                    </label>
                    <input type="text" id="name" name="name" required placeholder="My Production Key"
                           style="width: 100%; padding: 0.875rem 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-md); background: var(--bg-card); color: var(--text-primary); font-size: 0.875rem; transition: all 0.2s ease; margin-bottom: 0.5rem;"
                           onfocus="this.style.borderColor='var(--accent-primary)'; this.style.boxShadow='0 0 0 3px rgba(16, 163, 127, 0.1)'"
                           onblur="this.style.borderColor='var(--border-primary)'; this.style.boxShadow='none'">
                    <div style="color: var(--text-muted); font-size: 0.8125rem; line-height: 1.4;">
                        Choose a descriptive name to help you identify this key later.
                    </div>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label for="description" style="display: block; font-weight: 600; color: var(--text-primary); margin-bottom: 0.5rem; font-size: 0.875rem;">
                        <i class="fas fa-align-left" style="margin-right: 0.5rem; color: var(--accent-primary);"></i>
                        Description (optional)
                    </label>
                    <input type="text" id="description" name="description" placeholder="For my mobile application API integration"
                           style="width: 100%; padding: 0.875rem 1rem; border: 1px solid var(--border-primary); border-radius: var(--radius-md); background: var(--bg-card); color: var(--text-primary); font-size: 0.875rem; transition: all 0.2s ease; margin-bottom: 0.5rem;"
                           onfocus="this.style.borderColor='var(--accent-primary)'; this.style.boxShadow='0 0 0 3px rgba(16, 163, 127, 0.1)'"
                           onblur="this.style.borderColor='var(--border-primary)'; this.style.boxShadow='none'">
                    <div style="color: var(--text-muted); font-size: 0.8125rem; line-height: 1.4;">
                        Add a brief description of how you plan to use this API key.
                    </div>
                </div>

                <div style="background: rgba(16, 163, 127, 0.1); border: 1px solid rgba(16, 163, 127, 0.2); border-radius: var(--radius-md); padding: 1rem; margin-bottom: 1.5rem;">
                    <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-shield-alt" style="color: var(--accent-primary); font-size: 1rem;"></i>
                        <span style="font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">Security Notice</span>
                    </div>
                    <p style="color: var(--text-secondary); margin: 0; font-size: 0.8125rem; line-height: 1.5;">
                        Your API key will be shown only once after creation. Make sure to copy and store it securely.
                    </p>
                </div>
            </div>
            <div style="display: flex; gap: 1rem; padding: 1.5rem 2rem 2rem 2rem; border-top: 1px solid var(--border-secondary);">
                <button type="button" onclick="hideCreateKeyModal()"
                        style="flex: 1; padding: 0.875rem 1.5rem; background: var(--bg-secondary); color: var(--text-primary); border: 1px solid var(--border-primary); border-radius: var(--radius-md); font-weight: 500; cursor: pointer; transition: all 0.2s ease; font-size: 0.875rem;"
                        onmouseover="this.style.backgroundColor='var(--bg-tertiary)'"
                        onmouseout="this.style.backgroundColor='var(--bg-secondary)'">
                    Cancel
                </button>
                <button type="submit" class="btn btn-primary" style="flex: 1; padding: 0.875rem 1.5rem; font-size: 0.875rem;">
                    <i class="fas fa-key" style="margin-right: 0.5rem;"></i>
                    Create API Key
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Secret Info Modal -->
<div id="secretInfoModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: var(--bg-card); border: 1px solid var(--border-primary); border-radius: 0.75rem; max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border-bottom: 1px solid var(--border-secondary);">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-shield-alt" style="color: var(--accent-secondary);"></i>
                Client Secret Security
            </h3>
            <button onclick="hideSecretInfoModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.25rem;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 1.5rem;">
            <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid var(--accent-blue); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <p style="color: var(--accent-blue); margin: 0; display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Security Notice:</strong> Client secrets are securely hashed and cannot be retrieved.
                </p>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <h4 style="font-size: 1rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.75rem 0;">Why can't I view my secret?</h4>
                <p style="color: var(--text-secondary); margin: 0 0 0.75rem 0;">For security reasons, client secrets are stored using one-way encryption (hashing). This means:</p>
                <ul style="color: var(--text-secondary); margin: 0; padding-left: 1.25rem;">
                    <li style="margin-bottom: 0.5rem;"><strong>Secrets cannot be decrypted</strong> - even we can't see your original secret</li>
                    <li style="margin-bottom: 0.5rem;"><strong>Your data is protected</strong> - if our database is compromised, secrets remain secure</li>
                    <li><strong>Industry standard practice</strong> - this is how secure systems handle sensitive data</li>
                </ul>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <h4 style="font-size: 1rem; font-weight: 600; color: var(--text-primary); margin: 0 0 0.75rem 0;">What are my options?</h4>
                <ul style="color: var(--text-secondary); margin: 0; padding-left: 1.25rem;">
                    <li style="margin-bottom: 0.5rem;"><strong>Use your saved secret</strong> - if you saved it when first created</li>
                    <li style="margin-bottom: 0.5rem;"><strong>Regenerate a new secret</strong> - creates a new secret and invalidates the old one</li>
                    <li><strong>Create a new API key</strong> - if you need additional access</li>
                </ul>
            </div>

            <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid var(--accent-warning); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <p style="color: var(--accent-warning); margin: 0; display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Tip:</strong> Always save your client secrets in a secure password manager when they're first created!
                </p>
            </div>

            <button onclick="hideSecretInfoModal()" style="width: 100%; padding: 0.75rem; background: var(--bg-secondary); color: var(--text-primary); border: 1px solid var(--border-primary); border-radius: 0.5rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Regenerate Secret Modal -->
<div id="regenerateSecretModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: var(--bg-card); border: 1px solid var(--border-primary); border-radius: 0.75rem; max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto;">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 1.5rem; border-bottom: 1px solid var(--border-secondary);">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--text-primary); margin: 0; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-sync-alt" style="color: var(--accent-warning);"></i>
                Regenerate Client Secret
            </h3>
            <button onclick="hideRegenerateModal()" style="background: none; border: none; color: var(--text-muted); cursor: pointer; font-size: 1.25rem;">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div style="padding: 1.5rem;">
            <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid var(--accent-warning); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <p style="color: var(--accent-warning); margin: 0; display: flex; align-items: center; gap: 0.5rem; font-weight: 500;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This will generate a completely new client secret and invalidate the current one.
                </p>
            </div>

            <p style="color: var(--text-secondary); margin: 0 0 1.5rem 0;">
                Are you sure you want to regenerate the client secret for "<strong style="color: var(--text-primary);" id="keyNameToRegenerate"></strong>"?
            </p>

            <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid var(--accent-blue); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <h4 style="font-size: 1rem; font-weight: 600; color: var(--accent-blue); margin: 0 0 0.75rem 0;">What happens next:</h4>
                <ul style="color: var(--text-secondary); margin: 0; padding-left: 1.25rem;">
                    <li style="margin-bottom: 0.5rem;">A new client secret will be generated</li>
                    <li style="margin-bottom: 0.5rem;">The old secret will immediately stop working</li>
                    <li style="margin-bottom: 0.5rem;">You'll see the new secret once (save it securely!)</li>
                    <li>Update any applications using this API key</li>
                </ul>
            </div>

            <div style="display: flex; gap: 0.75rem;">
                <button onclick="hideRegenerateModal()" style="flex: 1; padding: 0.75rem; background: var(--bg-tertiary); color: var(--text-primary); border: 1px solid var(--border-primary); border-radius: 0.5rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease;">
                    Cancel
                </button>
                <form method="POST" id="regenerateSecretForm" style="flex: 1;">
                    <button type="submit"
                            style="width: 100%; padding: 0.75rem; background: var(--accent-warning); color: white; border: none; border-radius: 0.5rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);"
                            onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(245, 158, 11, 0.3)'"
                            onmouseout="this.style.background='var(--accent-warning)'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.2)'">
                        <i class="fas fa-sync-alt" style="margin-right: 0.5rem;"></i>Yes, Regenerate Secret
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
// Enhanced copy to clipboard with better feedback and animations
function copyToClipboard(elementId) {
    console.log('🔄 copyToClipboard called with elementId:', elementId);

    // Show immediate feedback that the function was called
    showToast('Copying...', 'info');

    const element = document.getElementById(elementId);
    if (!element) {
        console.error('❌ Element not found:', elementId);
        showToast('Failed to copy - element not found', 'error');
        return;
    }

    const text = (element.value || element.textContent).trim();
    console.log('📝 Text to copy:', text);
    if (!text) {
        console.error('❌ No text to copy from element:', elementId);
        showToast('Failed to copy - no text found', 'error');
        return;
    }

    navigator.clipboard.writeText(text).then(function() {
        console.log('Text copied successfully, looking for button...');

        // Enhanced button detection with multiple fallback strategies
        let button = null;

        // Strategy 1: Look for button with exact onclick match first (most reliable)
        button = document.querySelector(`button[onclick="copyToClipboard('${elementId}')"]`);
        if (button) {
            console.log('Found button by exact onclick match:', button);
        }

        // Strategy 2: Check if the element is directly followed by a button
        if (!button && element.nextElementSibling && element.nextElementSibling.tagName === 'BUTTON') {
            button = element.nextElementSibling;
            console.log('Found button as nextElementSibling:', button);
        }

        // Strategy 3: Look in the same parent container (for grid layouts)
        if (!button) {
            const parent = element.parentElement;
            if (parent) {
                // Look for any button with copyToClipboard in the same parent
                const buttons = parent.querySelectorAll('button[onclick*="copyToClipboard"]');
                // Find the button that specifically calls this elementId
                for (let btn of buttons) {
                    if (btn.onclick && btn.onclick.toString().includes(elementId)) {
                        button = btn;
                        console.log('Found button in parent container by onclick content:', button);
                        break;
                    }
                }
            }
        }

        // Strategy 4: Look in grandparent container (for complex layouts)
        if (!button) {
            const grandparent = element.parentElement?.parentElement;
            if (grandparent) {
                const buttons = grandparent.querySelectorAll('button[onclick*="copyToClipboard"]');
                for (let btn of buttons) {
                    if (btn.onclick && btn.onclick.toString().includes(elementId)) {
                        button = btn;
                        console.log('Found button in grandparent container by onclick content:', button);
                        break;
                    }
                }
            }
        }

        // Strategy 5: Fallback - look for any button with partial onclick match
        if (!button) {
            button = document.querySelector(`button[onclick*="${elementId}"]`);
            console.log('Found button by partial onclick selector:', button);
        }

        console.log('🔍 Final button found:', button);

        // Show success toast with appropriate message
        let toastMessage;
        if (elementId.includes('ClientId') || elementId.includes('clientId')) {
            toastMessage = 'Client ID copied to clipboard!';
        } else if (elementId.includes('Secret') || elementId.includes('secret')) {
            toastMessage = 'Client Secret copied to clipboard!';
        } else {
            toastMessage = 'API key copied to clipboard!';
        }
        showToast(toastMessage, 'success');

        if (button) {
            // Store original styles
            const originalIcon = button.innerHTML;
            const originalBg = button.style.backgroundColor || getComputedStyle(button).backgroundColor;
            const originalColor = button.style.color || getComputedStyle(button).color;
            const originalTransform = button.style.transform;
            const originalBorder = button.style.border || getComputedStyle(button).border;

            // Apply success animation with subtle, professional styling
            button.innerHTML = '<i class="fas fa-check" style="font-size: inherit;"></i>';
            button.style.backgroundColor = '#10b981'; // Success green
            button.style.color = 'white';
            button.style.border = '1px solid #10b981';
            button.style.transform = 'scale(1.05)';
            button.style.transition = 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)';

            // Add subtle glow effect
            button.style.boxShadow = '0 0 0 3px rgba(16, 185, 129, 0.2)';

            // Reset button after 1.5 seconds with smooth transition
            setTimeout(function() {
                button.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                button.innerHTML = originalIcon;
                button.style.backgroundColor = originalBg;
                button.style.color = originalColor;
                button.style.transform = originalTransform;
                button.style.border = originalBorder;
                button.style.boxShadow = '';

                // Remove transition after animation completes
                setTimeout(() => {
                    button.style.transition = '';
                }, 300);
            }, 1500);
        } else {
            console.warn('⚠️ Button not found for visual feedback, but copy was successful');

            // Try to find button by ID as a last resort
            const fallbackButtonId = elementId.includes('ClientId') ? 'copyBtnClientId' :
                                   elementId.includes('Secret') ? 'copyBtnSecret' : null;

            if (fallbackButtonId) {
                const fallbackButton = document.getElementById(fallbackButtonId);
                if (fallbackButton) {
                    console.log('✅ Found fallback button:', fallbackButton);
                    // Apply simple success animation
                    fallbackButton.style.color = '#10b981';
                    fallbackButton.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        fallbackButton.style.color = '';
                        fallbackButton.innerHTML = '<i class="fas fa-copy"></i>';
                    }, 1500);
                }
            }
        }
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        let errorMessage;
        if (elementId.includes('ClientId') || elementId.includes('clientId')) {
            errorMessage = 'Failed to copy Client ID';
        } else if (elementId.includes('Secret') || elementId.includes('secret')) {
            errorMessage = 'Failed to copy Client Secret';
        } else {
            errorMessage = 'Failed to copy API key';
        }
        showToast(errorMessage, 'error');
    });
}

// Debug function to test clipboard functionality
function testClipboard() {
    console.log('🧪 Testing clipboard functionality...');

    // Test if clipboard API is available
    if (!navigator.clipboard) {
        console.error('❌ Clipboard API not available');
        showToast('Clipboard API not available', 'error');
        return;
    }

    // Test basic clipboard write
    navigator.clipboard.writeText('Test clipboard content').then(() => {
        console.log('✅ Clipboard test successful');
        showToast('Clipboard test successful!', 'success');
    }).catch(err => {
        console.error('❌ Clipboard test failed:', err);
        showToast('Clipboard test failed: ' + err.message, 'error');
    });
}

// Make test function available globally for debugging
window.testClipboard = testClipboard;

// Toggle Client ID visibility
function toggleClientId(index) {
    const displayElement = document.getElementById(`clientId${index}Display`);
    const hiddenElement = document.getElementById(`clientId${index}`);
    const toggleButton = document.getElementById(`toggleBtn${index}`);

    if (!displayElement || !hiddenElement || !toggleButton) return;

    const fullClientId = hiddenElement.value;
    const truncatedClientId = fullClientId.substring(0, 8) + '...' + fullClientId.substring(fullClientId.length - 4);

    if (displayElement.textContent.includes('...')) {
        // Show full Client ID
        displayElement.textContent = fullClientId;
        toggleButton.innerHTML = '<i class="fas fa-eye-slash" style="font-size: 0.875rem;"></i>';
        toggleButton.title = 'Hide full Client ID';

        // Add a subtle highlight effect
        displayElement.style.color = 'var(--accent-secondary)';
        displayElement.style.fontWeight = '600';

        // Auto-hide after 10 seconds for security
        setTimeout(() => {
            if (displayElement.textContent === fullClientId) {
                displayElement.textContent = truncatedClientId;
                toggleButton.innerHTML = '<i class="fas fa-eye" style="font-size: 0.875rem;"></i>';
                toggleButton.title = 'Show full Client ID';
                displayElement.style.color = 'var(--text-secondary)';
                displayElement.style.fontWeight = 'normal';
            }
        }, 10000);
    } else {
        // Hide Client ID
        displayElement.textContent = truncatedClientId;
        toggleButton.innerHTML = '<i class="fas fa-eye" style="font-size: 0.875rem;"></i>';
        toggleButton.title = 'Show full Client ID';
        displayElement.style.color = 'var(--text-secondary)';
        displayElement.style.fontWeight = 'normal';
    }
}

// Enhanced toast notification function with subtle, professional styling
function showToast(message, type = 'info') {
    const toast = document.createElement('div');

    // Professional, subtle styling
    toast.style.position = 'fixed';
    toast.style.top = '24px';
    toast.style.right = '24px';
    toast.style.minWidth = '320px';
    toast.style.maxWidth = '400px';
    toast.style.zIndex = '10000';
    toast.style.transform = 'translateX(calc(100% + 24px))';
    toast.style.transition = 'all 0.4s cubic-bezier(0.16, 1, 0.3, 1)';
    toast.style.opacity = '0';

    // Subtle background and border styling
    if (type === 'success') {
        toast.style.background = 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)';
        toast.style.border = '1px solid #bbf7d0';
        toast.style.color = '#166534';
    } else if (type === 'error') {
        toast.style.background = 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)';
        toast.style.border = '1px solid #fecaca';
        toast.style.color = '#dc2626';
    } else {
        toast.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
        toast.style.border = '1px solid #e2e8f0';
        toast.style.color = '#475569';
    }

    toast.style.borderRadius = '12px';
    toast.style.padding = '16px 20px';
    toast.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
    toast.style.backdropFilter = 'blur(8px)';
    toast.style.display = 'flex';
    toast.style.alignItems = 'center';
    toast.style.gap = '12px';
    toast.style.fontSize = '14px';
    toast.style.fontWeight = '500';
    toast.style.lineHeight = '1.4';

    // Icon styling
    const iconColor = type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#6366f1';
    const iconClass = type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle';

    toast.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; width: 20px; height: 20px; border-radius: 50%; background: ${iconColor}20; flex-shrink: 0;">
            <i class="fas ${iconClass}" style="color: ${iconColor}; font-size: 12px;"></i>
        </div>
        <span style="flex: 1;">${message}</span>
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
        toast.style.opacity = '1';
    }, 10);

    // Auto-dismiss after 3 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(calc(100% + 24px))';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    }, 3000);

    // Allow manual dismissal on click
    toast.addEventListener('click', () => {
        toast.style.transform = 'translateX(calc(100% + 24px))';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    });

    // Add hover effect for interactivity
    toast.addEventListener('mouseenter', () => {
        toast.style.transform = 'translateX(-4px) scale(1.02)';
    });

    toast.addEventListener('mouseleave', () => {
        toast.style.transform = 'translateX(0) scale(1)';
    });
}

// Enhanced Modal functions with animations
function showCreateKeyModal() {
    const modal = document.getElementById('createKeyModal');
    const modalContent = modal.querySelector('div');

    modal.style.display = 'flex';
    setTimeout(() => {
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'scale(1)';
    }, 10);
}

function hideCreateKeyModal() {
    const modal = document.getElementById('createKeyModal');
    const modalContent = modal.querySelector('div');

    modalContent.style.opacity = '0';
    modalContent.style.transform = 'scale(0.95)';
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function showSecretInfoModal() {
    const modal = document.getElementById('secretInfoModal');
    modal.style.display = 'flex';
}

function hideSecretInfoModal() {
    const modal = document.getElementById('secretInfoModal');
    modal.style.display = 'none';
}

function showRegenerateModal(keyId, keyName) {
    // Set the key name in the modal
    document.getElementById('keyNameToRegenerate').textContent = keyName;

    // Set the form action URL
    const form = document.getElementById('regenerateSecretForm');
    form.action = `/regenerate-api-key-secret/${keyId}`;

    // Show the modal
    const modal = document.getElementById('regenerateSecretModal');
    modal.style.display = 'flex';
}

function hideRegenerateModal() {
    const modal = document.getElementById('regenerateSecretModal');
    modal.style.display = 'none';
}

// Enhanced toggle key menu dropdown with animations and smart positioning
function toggleKeyMenu(keyIndex) {
    const menu = document.getElementById(`keyMenu${keyIndex}`);
    const button = document.querySelector(`[onclick="toggleKeyMenu('${keyIndex}')"]`);
    const isVisible = menu.style.display === 'block';

    // Hide all other menus with animation
    document.querySelectorAll('[id^="keyMenu"]').forEach(m => {
        if (m !== menu) {
            m.style.opacity = '0';
            m.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                m.style.display = 'none';
            }, 200);
        }
    });

    // Toggle current menu with animation
    if (isVisible) {
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            menu.style.display = 'none';
        }, 200);
    } else {
        // Calculate position relative to the button
        const buttonRect = button.getBoundingClientRect();
        const menuWidth = 180; // min-width from CSS
        const menuHeight = 120; // estimated height

        // Calculate optimal position
        let top = buttonRect.bottom + 8; // 8px gap below button
        let left = buttonRect.right - menuWidth; // align right edge with button

        // Check if menu would go below viewport
        if (top + menuHeight > window.innerHeight) {
            top = buttonRect.top - menuHeight - 8; // position above button
        }

        // Check if menu would go off left edge
        if (left < 8) {
            left = 8; // minimum 8px from left edge
        }

        // Check if menu would go off right edge
        if (left + menuWidth > window.innerWidth - 8) {
            left = window.innerWidth - menuWidth - 8; // 8px from right edge
        }

        // Apply calculated position
        menu.style.top = `${top}px`;
        menu.style.left = `${left}px`;
        menu.style.display = 'block';

        setTimeout(() => {
            menu.style.opacity = '1';
            menu.style.transform = 'translateY(0)';
        }, 10);
    }
}

// Enhanced close modals when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['createKeyModal', 'secretInfoModal', 'regenerateSecretModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal && modal.style.display === 'flex' && event.target === modal) {
            if (modalId === 'createKeyModal') {
                hideCreateKeyModal();
            } else {
                modal.style.display = 'none';
            }
        }
    });

    // Close dropdown menus when clicking outside with animation
    if (!event.target.closest('[id^="keyMenu"]') && !event.target.closest('[onclick*="toggleKeyMenu"]')) {
        document.querySelectorAll('[id^="keyMenu"]').forEach(menu => {
            if (menu.style.display === 'block') {
                menu.style.opacity = '0';
                menu.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    menu.style.display = 'none';
                }, 200);
            }
        });
    }
});

// Enhanced interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--bg-secondary)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'transparent';
        });
    });

    // Enhanced keyboard navigation for modals
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            // Close any open modals with animation
            const modals = ['createKeyModal', 'secretInfoModal', 'regenerateSecretModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal && modal.style.display === 'flex') {
                    if (modalId === 'createKeyModal') {
                        hideCreateKeyModal();
                    } else {
                        modal.style.display = 'none';
                    }
                }
            });

            // Close any open dropdown menus with animation
            document.querySelectorAll('[id^="keyMenu"]').forEach(menu => {
                if (menu.style.display === 'block') {
                    menu.style.opacity = '0';
                    menu.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        menu.style.display = 'none';
                    }, 200);
                }
            });
        }
    });
});

// Model status management with enhanced UI
let modelStatusEventSource;
let lastModelCheck = 0;

function startModelStatusStream() {
    // Close existing connection if any
    if (modelStatusEventSource) {
        modelStatusEventSource.close();
    }

    // Create new EventSource for real-time updates
    modelStatusEventSource = new EventSource('/api/model-status-stream');

    modelStatusEventSource.onmessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            if (data.error) {
                console.error('Model status error:', data.error);
                showModelError(data.error);
            } else {
                updateModelStatusUI(data);
            }
        } catch (error) {
            console.error('Error parsing model status data:', error);
        }
    };

    modelStatusEventSource.onerror = function(event) {
        console.error('EventSource error:', event);
        updateConnectionStatus('error');
        // Automatically reconnect after 5 seconds
        setTimeout(() => {
            if (modelStatusEventSource.readyState === EventSource.CLOSED) {
                console.log('Reconnecting to model status stream...');
                updateConnectionStatus('reconnecting');
                startModelStatusStream();
            }
        }, 5000);
    };

    modelStatusEventSource.onopen = function(event) {
        console.log('Model status stream connected');
        updateConnectionStatus('connected');
    };
}

function stopModelStatusStream() {
    if (modelStatusEventSource) {
        modelStatusEventSource.close();
        modelStatusEventSource = null;
    }
}

// Fallback function for compatibility
function checkModelStatus() {
    fetch('/api/model-status')
        .then(response => response.json())
        .then(data => {
            updateModelStatusUI(data);
        })
        .catch(error => {
            console.error('Error checking model status:', error);
            showModelError('Failed to check model status');
        });
}

function updateModelStatusUI(data) {
    const modelCard = document.getElementById('modelStatusCard');
    const modelContent = document.getElementById('modelStatusContent');
    const modelCount = document.getElementById('modelCount');

    // Update model count in stats card
    modelCount.textContent = data.total_models || 0;

    if (data.status === 'error') {
        showModelError(data.message);
        return;
    }

    // Only show the card if models are actively downloading
    if (data.is_downloading && data.downloading_models.length > 0) {
        modelCard.style.display = 'block';

        // Enhanced download progress with new design
        let progressHTML = `
            <div style="background: rgba(59, 130, 246, 0.1); border: 1px solid var(--accent-blue); border-radius: 0.5rem; padding: 1rem; margin-bottom: 1.5rem;">
                <h4 style="font-size: 1rem; font-weight: 600; color: var(--accent-blue); margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-download"></i>
                    Downloading Models
                </h4>
                <p style="color: var(--text-secondary); margin: 0; font-size: 0.875rem;">${data.downloading_models.length} model(s) are currently being downloaded.</p>
            </div>
        `;

        data.downloading_models.forEach(model => {
            const progress = model.progress || 0;
            const progressColor = progress === 100 ? 'var(--accent-green)' : 'var(--accent-blue)';

            progressHTML += `
                <div style="margin-bottom: 1.5rem; padding: 1rem; background: var(--bg-tertiary); border-radius: 0.5rem;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
                        <span style="font-weight: 600; color: var(--text-primary); display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-robot" style="color: var(--accent-blue);"></i>
                            ${model.name}
                        </span>
                        <span style="background: var(--bg-card); color: var(--text-secondary); padding: 0.25rem 0.75rem; border-radius: 0.375rem; font-size: 0.8125rem; font-weight: 500;">${progress}%</span>
                    </div>
                    <div style="background: var(--bg-card); border-radius: 0.375rem; height: 0.5rem; overflow: hidden; margin-bottom: 0.75rem;">
                        <div style="background: ${progressColor}; height: 100%; width: ${progress}%; transition: width 0.3s ease; ${progress > 0 ? 'animation: pulse 2s infinite;' : ''}"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.8125rem;">
                        <span style="color: var(--text-muted); display: flex; align-items: center; gap: 0.25rem;">
                            <i class="fas fa-info-circle"></i>
                            ${model.status || 'Downloading...'}
                        </span>
                        <span style="color: var(--text-muted); display: flex; align-items: center; gap: 0.25rem;">
                            <i class="fas fa-clock"></i>
                            ${new Date(model.timestamp).toLocaleTimeString()}
                            <span style="background: var(--accent-green); color: white; padding: 0.125rem 0.375rem; border-radius: 0.25rem; font-size: 0.75rem; margin-left: 0.25rem;">
                                <i class="fas fa-wifi" style="margin-right: 0.125rem;"></i>Live
                            </span>
                        </span>
                    </div>
                </div>
            `;
        });

        progressHTML += `
            <div style="background: rgba(245, 158, 11, 0.1); border: 1px solid var(--accent-warning); border-radius: 0.5rem; padding: 1rem;">
                <p style="color: var(--accent-warning); margin: 0; font-size: 0.875rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-clock"></i>
                    Model downloads can take several minutes depending on size and internet connection.
                </p>
                <p style="color: var(--accent-green); margin: 0.5rem 0 0 0; font-size: 0.875rem; display: flex; align-items: center; gap: 0.5rem;">
                    <i class="fas fa-broadcast-tower"></i>
                    <strong>Live streaming updates</strong> - progress updates in real-time.
                </p>
            </div>
        `;

        modelContent.innerHTML = progressHTML;
    } else {
        // Hide the card when no models are downloading
        modelCard.style.display = 'none';
    }
}

function showModelError(message) {
    const modelCard = document.getElementById('modelStatusCard');
    const modelContent = document.getElementById('modelStatusContent');

    modelCard.style.display = 'block';
    modelContent.innerHTML = `
        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid var(--accent-red); border-radius: 0.5rem; padding: 1rem;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--accent-red); margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
                <i class="fas fa-exclamation-triangle"></i>
                Connection Issue
            </h4>
            <p style="color: var(--text-secondary); margin: 0;">${message}</p>
        </div>
    `;
}

function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connectionStatus');
    if (!statusElement) return;

    switch(status) {
        case 'connected':
            statusElement.style.background = 'rgba(16, 185, 129, 0.2)';
            statusElement.style.color = 'var(--accent-green)';
            statusElement.innerHTML = '<i class="fas fa-wifi" style="margin-right: 0.25rem;"></i>Live';
            break;
        case 'error':
            statusElement.style.background = 'rgba(239, 68, 68, 0.2)';
            statusElement.style.color = 'var(--accent-red)';
            statusElement.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 0.25rem;"></i>Error';
            break;
        case 'reconnecting':
            statusElement.style.background = 'rgba(245, 158, 11, 0.2)';
            statusElement.style.color = 'var(--accent-warning)';
            statusElement.innerHTML = '<i class="fas fa-sync-alt fa-spin" style="margin-right: 0.25rem;"></i>Reconnecting';
            break;
        default:
            statusElement.style.background = 'rgba(255, 255, 255, 0.2)';
            statusElement.style.color = 'white';
            statusElement.innerHTML = '<i class="fas fa-question" style="margin-right: 0.25rem;"></i>Unknown';
    }
}

function startModelStatusPolling() {
    // Use real-time streaming instead of polling
    startModelStatusStream();
}

function stopModelStatusPolling() {
    // Stop the real-time stream
    stopModelStatusStream();
}

// Start polling when page loads
document.addEventListener('DOMContentLoaded', function() {
    startModelStatusPolling();
});

// Handle page visibility changes for better performance
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // Keep streaming but at reduced frequency when tab is hidden
        console.log('Tab hidden - model status stream continues');
    } else {
        // Ensure stream is active when tab becomes visible
        if (!modelStatusEventSource || modelStatusEventSource.readyState === EventSource.CLOSED) {
            console.log('Tab visible - restarting model status stream');
            startModelStatusPolling();
        }
    }
});

// Handle page unload to clean up connections
window.addEventListener('beforeunload', function() {
    stopModelStatusPolling();
});
</script>
{% endblock %}
