{% extends "base.html" %}

{% block title %}Welcome - Calcounta Ollama Gateway{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Hero Section -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-shield-alt text-primary" style="font-size: 4rem;"></i>
                </div>
                <h1 class="display-4 fw-bold text-primary mb-3">Calcounta Ollama Gateway</h1>
                <p class="lead text-muted mb-4">
                    Secure API access control for your Ollama instance. 
                    Generate API keys, manage access, and monitor usage with enterprise-grade security.
                </p>
                
                {% if not session.get('access_token') %}
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg me-md-2">
                        <i class="fas fa-user-plus me-2"></i>Get Started
                    </a>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                    </a>
                </div>
                {% else %}
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- Features Section -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="card h-100 text-center border-0">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-key text-primary" style="font-size: 2.5rem;"></i>
                            </div>
                            <h5 class="card-title">API Key Management</h5>
                            <p class="card-text text-muted">
                                Generate secure client_id and client_secret pairs for API access. 
                                Manage multiple keys with custom names and descriptions.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 text-center border-0">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-robot text-success" style="font-size: 2.5rem;"></i>
                            </div>
                            <h5 class="card-title">Ollama Integration</h5>
                            <p class="card-text text-muted">
                                Secure proxy to your local Ollama instance. 
                                Access all models and features with authenticated requests.
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card h-100 text-center border-0">
                        <div class="card-body">
                            <div class="mb-3">
                                <i class="fas fa-chart-line text-warning" style="font-size: 2.5rem;"></i>
                            </div>
                            <h5 class="card-title">Usage Analytics</h5>
                            <p class="card-text text-muted">
                                Monitor API usage, track requests, and manage rate limits. 
                                Built-in analytics for better resource management.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- How it Works -->
            <div class="card mb-5">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>How It Works
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">1. Register & Login</h6>
                            <p class="text-muted mb-3">Create your account and access the dashboard.</p>
                            
                            <h6 class="fw-bold">2. Generate API Keys</h6>
                            <p class="text-muted mb-3">Create client_id and client_secret pairs for your applications.</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">3. Access Ollama</h6>
                            <p class="text-muted mb-3">Use your credentials to make authenticated requests to Ollama.</p>
                            
                            <h6 class="fw-bold">4. Monitor Usage</h6>
                            <p class="text-muted mb-3">Track your API usage and manage rate limits.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Example -->
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-code me-2"></i>API Usage Example
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Once you have your API credentials, you can access Ollama like this:</p>
                    <div class="code-block">
<pre><code># List available models
curl -X GET http://localhost:85/ai/api/tags \
  -H "X-Client-ID: your_client_id" \
  -H "X-Client-Secret: your_client_secret"

# Chat with a model
curl -X POST http://localhost:85/ai/api/chat \
  -H "X-Client-ID: your_client_id" \
  -H "X-Client-Secret: your_client_secret" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "llama2",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
