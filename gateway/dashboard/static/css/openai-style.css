/* OpenAI-inspired Dashboard Styles */

/* Additional utility classes and enhancements */
.openai-gradient {
    background: linear-gradient(135deg, var(--accent-green) 0%, var(--accent-green-hover) 100%);
}

.openai-card-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.openai-card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.openai-button {
    position: relative;
    overflow: hidden;
}

.openai-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.openai-button:hover::before {
    left: 100%;
}

.openai-input {
    transition: all 0.2s ease;
}

.openai-input:focus {
    transform: scale(1.02);
}

.openai-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.openai-badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--accent-green);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.openai-badge-inactive {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    border: 1px solid var(--border-primary);
}

.openai-progress {
    background: var(--bg-tertiary);
    border-radius: 0.375rem;
    height: 0.5rem;
    overflow: hidden;
}

.openai-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
    transition: width 0.3s ease;
    position: relative;
}

.openai-progress-bar.animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.openai-tooltip {
    position: relative;
}

.openai-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-card);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.8125rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
}

.openai-tooltip:hover::after {
    opacity: 1;
}

.openai-grid {
    display: grid;
    gap: 1.5rem;
}

.openai-grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.openai-grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

.openai-grid-3 {
    grid-template-columns: repeat(3, 1fr);
}

@media (max-width: 768px) {
    .openai-grid-2,
    .openai-grid-3 {
        grid-template-columns: 1fr;
    }
}

.openai-status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.openai-status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.openai-status-dot.active {
    background: var(--accent-green);
}

.openai-status-dot.inactive {
    background: var(--text-muted);
    animation: none;
}

.openai-code-block {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 0.5rem;
    padding: 1rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.8125rem;
    line-height: 1.5;
    overflow-x: auto;
    position: relative;
}

.openai-code-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-green), var(--accent-blue));
    border-radius: 0.5rem 0.5rem 0 0;
}

.openai-copy-button {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 0.375rem;
    padding: 0.375rem;
    color: var(--text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
}

.openai-copy-button:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
}

.openai-section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-secondary);
}

.openai-section-icon {
    background: var(--accent-green);
    border-radius: 0.5rem;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.openai-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.openai-section-subtitle {
    color: var(--text-muted);
    font-size: 0.875rem;
    margin: 0;
}

/* Dark mode specific enhancements */
@media (prefers-color-scheme: dark) {
    .openai-card-hover:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-primary: #ffffff;
        --text-muted: #cccccc;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
