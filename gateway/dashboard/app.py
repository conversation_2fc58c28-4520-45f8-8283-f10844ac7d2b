import os
import requests
from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, Response
from functools import wraps
import time
import threading

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', 'dashboard-secret-key-change-me')

# Configuration
AUTH_SERVICE_URL = 'http://ollama-auth-service:8001'
OLLAMA_PROXY_URL = 'http://ollama-proxy:8002'
GATEWAY_URL = os.getenv('GATEWAY_URL', 'http://nginx:80')  # Internal gateway URL for container-to-container communication
EXTERNAL_GATEWAY_URL = 'http://localhost:85'  # External URL for user reference

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'access_token' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def make_auth_request(method, endpoint, data=None, headers=None):
    """Make request to auth service"""
    url = f"{AUTH_SERVICE_URL}{endpoint}"
    default_headers = {'Content-Type': 'application/json'}
    if headers:
        default_headers.update(headers)
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=default_headers, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=default_headers, timeout=10)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=default_headers, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=default_headers, timeout=10)
        else:
            return None, "Unsupported HTTP method"
        
        return response, None
    except requests.RequestException as e:
        return None, str(e)

@app.route('/')
def index():
    if 'access_token' in session:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        # Validation
        if not all([username, email, password, confirm_password]):
            flash('All fields are required.', 'error')
            return render_template('register.html')
        
        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('register.html')
        
        if len(password) < 8:
            flash('Password must be at least 8 characters long.', 'error')
            return render_template('register.html')
        
        # Register user
        response, error = make_auth_request('POST', '/api/auth/register', {
            'username': username,
            'email': email,
            'password': password
        })
        
        if error:
            flash(f'Registration failed: {error}', 'error')
            return render_template('register.html')
        
        if response.status_code == 201:
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('login'))
        else:
            error_msg = response.json().get('error', 'Registration failed')
            flash(f'Registration failed: {error_msg}', 'error')
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('Username and password are required.', 'error')
            return render_template('login.html')
        
        # Login user
        response, error = make_auth_request('POST', '/api/auth/login', {
            'username': username,
            'password': password
        })
        
        if error:
            flash(f'Login failed: {error}', 'error')
            return render_template('login.html')
        
        if response.status_code == 200:
            data = response.json()
            session['access_token'] = data['access_token']
            session['user'] = data['user']
            flash(f'Welcome back, {data["user"]["username"]}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            error_msg = response.json().get('error', 'Login failed')
            flash(f'Login failed: {error_msg}', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    if 'access_token' in session:
        # Logout from auth service
        headers = {'Authorization': f'Bearer {session["access_token"]}'}
        make_auth_request('POST', '/api/auth/logout', headers=headers)
    
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get user's API keys
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    response, error = make_auth_request('GET', '/api/keys', headers=headers)
    
    api_keys = []
    if response and response.status_code == 200:
        api_keys = response.json().get('api_keys', [])
    
    return render_template('dashboard.html',
                         user=session.get('user'),
                         api_keys=api_keys,
                         gateway_url=EXTERNAL_GATEWAY_URL)

@app.route('/create-api-key', methods=['POST'])
@login_required
def create_api_key():
    name = request.form.get('name')
    description = request.form.get('description', '')
    
    if not name:
        flash('API key name is required.', 'error')
        return redirect(url_for('dashboard'))
    
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    response, error = make_auth_request('POST', '/api/keys', {
        'name': name,
        'description': description
    }, headers=headers)
    
    if error:
        flash(f'Failed to create API key: {error}', 'error')
    elif response.status_code == 201:
        data = response.json()
        flash('API key created successfully!', 'success')
        # Store the new key temporarily for display
        session['new_api_key'] = data['api_key']
    else:
        error_msg = response.json().get('error', 'Failed to create API key')
        flash(f'Failed to create API key: {error_msg}', 'error')
    
    return redirect(url_for('dashboard'))

@app.route('/delete-api-key/<key_id>', methods=['POST'])
@login_required
def delete_api_key(key_id):
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    response, error = make_auth_request('DELETE', f'/api/keys/{key_id}', headers=headers)
    
    if error:
        flash(f'Failed to delete API key: {error}', 'error')
    elif response.status_code == 200:
        flash('API key deleted successfully.', 'success')
    else:
        error_msg = response.json().get('error', 'Failed to delete API key')
        flash(f'Failed to delete API key: {error_msg}', 'error')
    
    return redirect(url_for('dashboard'))

@app.route('/toggle-api-key/<key_id>', methods=['POST'])
@login_required
def toggle_api_key(key_id):
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    response, error = make_auth_request('PUT', f'/api/keys/{key_id}/toggle', headers=headers)

    if error:
        flash(f'Failed to toggle API key: {error}', 'error')
    elif response.status_code == 200:
        data = response.json()
        status = "enabled" if data.get('is_active') else "disabled"
        flash(f'API key {status} successfully.', 'success')
    else:
        error_msg = response.json().get('error', 'Failed to toggle API key')
        flash(f'Failed to toggle API key: {error_msg}', 'error')

    return redirect(url_for('dashboard'))

@app.route('/regenerate-api-key-secret/<key_id>', methods=['POST'])
@login_required
def regenerate_api_key_secret(key_id):
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    response, error = make_auth_request('PUT', f'/api/keys/{key_id}/regenerate-secret', headers=headers)

    if error:
        flash(f'Failed to regenerate client secret: {error}', 'error')
    elif response.status_code == 200:
        data = response.json()
        flash('Client secret regenerated successfully!', 'success')
        # Store the regenerated key temporarily for display
        session['regenerated_api_key'] = data['api_key']
    else:
        error_msg = response.json().get('error', 'Failed to regenerate client secret')
        flash(f'Failed to regenerate client secret: {error_msg}', 'error')

    return redirect(url_for('dashboard'))

@app.route('/chat', methods=['GET', 'POST'])
@login_required
def chat():
    # Get user's API keys for the dropdown
    headers = {'Authorization': f'Bearer {session["access_token"]}'}
    api_keys_response, error = make_auth_request('GET', '/api/keys', headers=headers)

    api_keys = []
    if api_keys_response and api_keys_response.status_code == 200:
        api_keys = api_keys_response.json().get('api_keys', [])
        # Only include active keys
        api_keys = [key for key in api_keys if key.get('is_active', False)]

    if request.method == 'POST':
        client_id = request.form.get('client_id')
        client_secret = request.form.get('client_secret')

        if not client_id or not client_secret:
            flash('Client ID and Client Secret are required.', 'error')
            return render_template('chat.html', api_keys=api_keys)

        # Get available models
        try:
            response = requests.get(
                f"{GATEWAY_URL}/ai/api/tags",
                headers={
                    'X-Client-ID': client_id,
                    'X-Client-Secret': client_secret
                },
                timeout=10
            )

            if response.status_code == 200:
                models = response.json().get('models', [])
                flash(f'Successfully connected! Found {len(models)} models.', 'success')
                return render_template('chat.html', models=models,
                                     client_id=client_id, client_secret=client_secret, api_keys=api_keys)
            else:
                error_msg = response.json().get('error', f'HTTP {response.status_code}')
                flash(f'Connection failed: {error_msg}', 'error')
        except requests.RequestException as e:
            flash(f'Connection failed: {str(e)}', 'error')

    return render_template('chat.html', api_keys=api_keys)

@app.route('/api/get-key-secret/<key_id>', methods=['POST'])
@login_required
def get_key_secret(key_id):
    """Get the client secret for a specific API key (for auto-fill)"""
    try:
        # For security, we'll validate the key belongs to the user
        # and return a temporary token instead of the actual secret
        headers = {'Authorization': f'Bearer {session["access_token"]}'}
        response, error = make_auth_request('GET', '/api/keys', headers=headers)

        if error or not response or response.status_code != 200:
            return jsonify({'error': 'Failed to validate API key'}), 400

        api_keys = response.json().get('api_keys', [])
        selected_key = None

        for key in api_keys:
            if key['id'] == key_id and key.get('is_active', False):
                selected_key = key
                break

        if not selected_key:
            return jsonify({'error': 'API key not found or inactive'}), 404

        # Store the key info temporarily in session for auto-fill
        session['temp_key_info'] = {
            'client_id': selected_key['client_id'],
            'key_id': key_id,
            'name': selected_key['name']
        }

        return jsonify({
            'client_id': selected_key['client_id'],
            'message': 'Key selected. You will need to enter the client secret manually for security.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
@login_required
def api_chat():
    """API endpoint for chat"""
    data = request.json
    client_id = data.get('client_id')
    client_secret = data.get('client_secret')
    model = data.get('model')

    # Handle both old format (message) and new format (messages)
    message = data.get('message')
    messages = data.get('messages')
    images = data.get('images')
    format_schema = data.get('format')

    if not all([client_id, client_secret, model]):
        return jsonify({'error': 'Missing required parameters'}), 400

    if not message and not messages:
        return jsonify({'error': 'Either message or messages is required'}), 400

    try:
        # Prepare the payload for Ollama
        payload = {
            'model': model,
            'stream': False
        }

        # Handle different message formats
        if messages:
            # New format with full message structure
            payload['messages'] = messages
        else:
            # Old format with simple message
            payload['messages'] = [{'role': 'user', 'content': message}]

        # Add images if provided
        if images:
            payload['images'] = images

        # Add format schema if provided (for structured responses)
        if format_schema:
            payload['format'] = format_schema

        response = requests.post(
            f"{GATEWAY_URL}/ai/api/chat",
            headers={
                'X-Client-ID': client_id,
                'X-Client-Secret': client_secret,
                'Content-Type': 'application/json'
            },
            json=payload,
            timeout=300  # Increased timeout for image processing
        )

        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'error': response.text}), response.status_code
    except requests.RequestException as e:
        return jsonify({'error': str(e)}), 500





@app.route('/api/model-status')
def model_status():
    """Get current model download status and available models"""
    import json
    import glob

    try:
        # Get available models from Ollama (try direct connection first)
        models = []
        try:
            # Try direct connection to Ollama container first
            response = requests.get("http://ollama:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                models = models_data.get('models', [])
                print(f"Successfully fetched {len(models)} models from Ollama direct")
            else:
                print(f"Direct Ollama API returned status {response.status_code}: {response.text}")
        except requests.RequestException as e:
            print(f"Failed to connect to Ollama directly: {e}")
            # Fallback to proxy if direct connection fails
            try:
                response = requests.get(f"{OLLAMA_PROXY_URL}/api/tags", timeout=5)
                if response.status_code == 200:
                    models_data = response.json()
                    models = models_data.get('models', [])
                    print(f"Successfully fetched {len(models)} models from Ollama proxy")
                else:
                    print(f"Ollama proxy returned status {response.status_code}: {response.text}")
            except requests.RequestException as e2:
                print(f"Ollama proxy connection also failed: {e2}")

        # Check for downloading models by reading status files
        downloading_models = []
        status_dir = "/tmp/ollama-status"

        try:
            import os
            # Check if directory exists and list files
            if os.path.exists(status_dir):
                status_files = [f for f in os.listdir(status_dir) if f.endswith('.json')]

                for filename in status_files:
                    status_file = os.path.join(status_dir, filename)
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            # Clean any remaining control characters
                            content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\r\t')
                            status_data = json.loads(content)
                            downloading_models.append(status_data)
                    except (json.JSONDecodeError, IOError, UnicodeDecodeError) as e:
                        # Skip invalid or unreadable files
                        print(f"Error reading status file {status_file}: {e}")
                        continue
        except OSError as e:
            # Status directory doesn't exist or can't be read
            print(f"Error accessing status directory: {e}")
            pass

        # If we have downloading models but Ollama responded, it means downloads are in progress
        if response.status_code == 200:
            return jsonify({
                'status': 'success',
                'available_models': models,
                'downloading_models': downloading_models,
                'total_models': len(models),
                'has_models': len(models) > 0,
                'is_downloading': len(downloading_models) > 0
            })
        else:
            # Ollama service not available, but we might have download status
            return jsonify({
                'status': 'partial',
                'message': 'Ollama service starting up or unavailable',
                'available_models': [],
                'downloading_models': downloading_models,
                'total_models': 0,
                'has_models': False,
                'is_downloading': len(downloading_models) > 0
            })

    except requests.RequestException as e:
        # Check for download status even if Ollama is not responding
        downloading_models = []
        status_dir = "/tmp/ollama-status"

        try:
            import os
            if os.path.exists(status_dir):
                status_files = [f for f in os.listdir(status_dir) if f.endswith('.json')]
                for filename in status_files:
                    status_file = os.path.join(status_dir, filename)
                    try:
                        with open(status_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                            # Clean any remaining control characters
                            content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\r\t')
                            status_data = json.loads(content)
                            downloading_models.append(status_data)
                    except (json.JSONDecodeError, IOError, UnicodeDecodeError):
                        continue
        except OSError:
            pass

        return jsonify({
            'status': 'error',
            'message': f'Ollama service unavailable: {str(e)}',
            'available_models': [],
            'downloading_models': downloading_models,
            'total_models': 0,
            'has_models': False,
            'is_downloading': len(downloading_models) > 0
        })

@app.route('/api/model-status-stream')
def model_status_stream():
    """Server-Sent Events endpoint for real-time model status updates"""
    def generate():
        import json
        import os

        while True:
            try:
                # Get current model status
                downloading_models = []
                status_dir = "/tmp/ollama-status"

                if os.path.exists(status_dir):
                    status_files = [f for f in os.listdir(status_dir) if f.endswith('.json')]
                    for filename in status_files:
                        status_file = os.path.join(status_dir, filename)
                        try:
                            with open(status_file, 'r', encoding='utf-8') as f:
                                content = f.read()
                                # Clean any remaining control characters
                                content = ''.join(char for char in content if ord(char) >= 32 or char in '\n\r\t')
                                status_data = json.loads(content)
                                downloading_models.append(status_data)
                        except (json.JSONDecodeError, IOError, UnicodeDecodeError):
                            continue

                # Get available models from Ollama (try direct connection first)
                available_models = []
                try:
                    # Try direct connection to Ollama container first
                    response = requests.get("http://ollama:11434/api/tags", timeout=3)
                    if response.status_code == 200:
                        models_data = response.json()
                        available_models = models_data.get('models', [])
                except requests.RequestException:
                    # Fallback to proxy if direct connection fails
                    try:
                        response = requests.get(f"{OLLAMA_PROXY_URL}/api/tags", timeout=3)
                        if response.status_code == 200:
                            models_data = response.json()
                            available_models = models_data.get('models', [])
                    except requests.RequestException:
                        pass

                # Prepare status data
                status_update = {
                    'available_models': available_models,
                    'downloading_models': downloading_models,
                    'total_models': len(available_models),
                    'has_models': len(available_models) > 0,
                    'is_downloading': len(downloading_models) > 0,
                    'timestamp': time.time()
                }

                # Send as Server-Sent Event
                yield f"data: {json.dumps(status_update)}\n\n"

                # Wait 2 seconds before next update (much faster than 10 seconds)
                time.sleep(2)

            except Exception as e:
                # Send error event
                error_data = {
                    'error': str(e),
                    'timestamp': time.time()
                }
                yield f"data: {json.dumps(error_data)}\n\n"
                time.sleep(5)  # Wait longer on error

    return Response(generate(), mimetype='text/event-stream', headers={
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
    })

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy',
        'service': 'dashboard',
        'timestamp': '2024-01-01T00:00:00Z'
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8003, debug=True)
