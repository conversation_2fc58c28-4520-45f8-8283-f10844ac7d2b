services:
  backend:
    container_name: calcounta-backend
    build:
      context: ./backend
      dockerfile: Dockerfile
    # No external ports exposed - only accessible within Docker network
    env_file:
      - .env
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - calcounta-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:86/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  frontend:
    container_name: calcounta-frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${PORT}:80"  # Expose frontend on the configured port externally
    env_file:
      - .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - calcounta-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  db:
    image: postgres:15
    container_name: calcounta-db
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - calcounta-network
    # No external ports exposed - only accessible within Docker network
    # ports:
    #   - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Separate database for Ollama API
  ollama-db:
    image: postgres:15
    container_name: calcounta-ollama-db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${OLLAMA_POSTGRES_USER}
      - POSTGRES_PASSWORD=${OLLAMA_POSTGRES_PASSWORD}
      - POSTGRES_DB=${OLLAMA_POSTGRES_DB}
    volumes:
      - ollama_postgres_data:/var/lib/postgresql/data
      - ./gateway/database/init-clean.sql:/docker-entrypoint-initdb.d/init-ollama.sql:ro
    networks:
      - calcounta-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${OLLAMA_POSTGRES_USER} -d ${OLLAMA_POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  ai-worker:
    container_name: calcounta-ai-worker
    build:
      context: ./worker
      dockerfile: Dockerfile
    env_file:
      - .env
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - calcounta-network

  # Ollama API Gateway (Nginx Reverse Proxy)
  ollama-gateway:
    image: nginx:alpine
    container_name: calcounta-ollama-gateway
    ports:
      - "${OLLAMA_GATEWAY_PORT}:80"
    volumes:
      - ./gateway/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./gateway/nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - ollama-auth-service
      - ollama-proxy
      - ollama-dashboard
    networks:
      - calcounta-network
    restart: unless-stopped

  # Ollama Authentication Service
  ollama-auth-service:
    build:
      context: ./gateway/auth-service
      dockerfile: Dockerfile
    container_name: calcounta-ollama-auth-service
    environment:
      - POSTGRES_DB=${OLLAMA_POSTGRES_DB}
      - POSTGRES_USER=${OLLAMA_POSTGRES_USER}
      - POSTGRES_PASSWORD=${OLLAMA_POSTGRES_PASSWORD}
      - POSTGRES_HOST=ollama-db
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET_KEY=${OLLAMA_JWT_SECRET_KEY}
      - JWT_ALGORITHM=${OLLAMA_JWT_ALGORITHM}
      - JWT_EXPIRATION_HOURS=${OLLAMA_JWT_EXPIRATION_HOURS}
      - API_KEY_LENGTH=${OLLAMA_API_KEY_LENGTH}
      - CLIENT_SECRET_LENGTH=${OLLAMA_CLIENT_SECRET_LENGTH}
      - BCRYPT_ROUNDS=${OLLAMA_BCRYPT_ROUNDS}
      - LOG_LEVEL=${OLLAMA_LOG_LEVEL}
      - ENVIRONMENT=${OLLAMA_ENVIRONMENT}
      - DEBUG=${OLLAMA_DEBUG}
    depends_on:
      - ollama-db
      - redis
    networks:
      - calcounta-network
    restart: unless-stopped
    volumes:
      - ./gateway/auth-service:/app

  # Ollama Proxy Service
  ollama-proxy:
    build:
      context: ./gateway/ollama-proxy
      dockerfile: Dockerfile
    container_name: calcounta-ollama-proxy
    environment:
      - POSTGRES_DB=${OLLAMA_POSTGRES_DB}
      - POSTGRES_USER=${OLLAMA_POSTGRES_USER}
      - POSTGRES_PASSWORD=${OLLAMA_POSTGRES_PASSWORD}
      - POSTGRES_HOST=ollama-db
      - POSTGRES_PORT=5432
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434
      - OLLAMA_URL=http://ollama:11434
      - RATE_LIMIT_PER_MINUTE=${OLLAMA_RATE_LIMIT_PER_MINUTE}
      - RATE_LIMIT_PER_HOUR=${OLLAMA_RATE_LIMIT_PER_HOUR}
      - RATE_LIMIT_PER_DAY=${OLLAMA_RATE_LIMIT_PER_DAY}
      - LOG_LEVEL=${OLLAMA_LOG_LEVEL}
      - ENVIRONMENT=${OLLAMA_ENVIRONMENT}
      - DEBUG=${OLLAMA_DEBUG}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:86,http://127.0.0.1:86}
    depends_on:
      - ollama-db
      - redis
      - ollama
    networks:
      - calcounta-network
    restart: unless-stopped
    volumes:
      - ./gateway/ollama-proxy:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Ollama Service (Internal Docker Container)
  ollama:
    image: ollama/ollama:latest
    container_name: calcounta-ollama
    volumes:
      - ./ollama-models:/root/.ollama
      - ./scripts/ollama-init.sh:/tmp/ollama-init.sh:ro
      - ollama_status:/tmp/ollama-status
    networks:
      - calcounta-network
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_MODEL=${OLLAMA_MODEL}
      - OLLAMA_KEEP_ALIVE=24h
      - OLLAMA_MAX_LOADED_MODELS=4
      - OLLAMA_FLASH_ATTENTION=1
      - OLLAMA_DATA_DIR=/root/.ollama
      - OLLAMA_MODELS_DIR=/root/.ollama/models
      - OLLAMA_DEBUG=1
    # Expose port only for localhost access (optional)
    ports:
      - "127.0.0.1:11434:11434"  # Only accessible from localhost
    entrypoint: ["/bin/bash"]
    command: >
      -c "
        echo '🚀 Starting Ollama service...' &&
        echo '📁 Ollama data directory: /root/.ollama (host bind mount)' &&

        # Ensure proper directory structure and permissions for host bind mount
        mkdir -p /root/.ollama/models &&
        mkdir -p /root/.ollama/download-status &&
        chmod -R 755 /root/.ollama &&

        # Set ownership to ensure compatibility with host filesystem
        chown -R root:root /root/.ollama &&

        # Show current state of models directory
        echo '📋 Current models directory contents:' &&
        ls -la /root/.ollama/ || echo 'Directory is empty (normal for first run)' &&
        if [ -d '/root/.ollama/models' ]; then
          echo '📦 Models subdirectory:' &&
          ls -la /root/.ollama/models/ || echo 'Models directory is empty'
        fi &&

        # Start Ollama service in background
        echo '🔄 Starting Ollama server...' &&
        ollama serve &
        OLLAMA_PID=\$! &&

        # Wait longer for Ollama to fully initialize
        echo '⏳ Waiting for Ollama to initialize (this may take up to 2 minutes)...' &&
        sleep 30 &&

        # Check if Ollama is responding
        echo '🔍 Testing Ollama service availability...' &&
        for i in {1..20}; do
          if ollama list >/dev/null 2>&1; then
            echo '✅ Ollama service is responding' &&
            break
          fi
          echo \"   Attempt \$i/20: Waiting for Ollama...\" &&
          sleep 3
        done &&

        # Show existing models before initialization
        echo '📋 Checking for existing models and partial downloads...' &&
        ollama list || echo 'No models found yet' &&

        # Run model initialization script
        if [ -f /tmp/ollama-init.sh ]; then
          echo '📦 Starting model initialization...' &&
          cp /tmp/ollama-init.sh /usr/local/bin/ollama-init.sh &&
          chmod +x /usr/local/bin/ollama-init.sh &&
          /usr/local/bin/ollama-init.sh
        else
          echo '⚠️  Model initialization script not found, skipping...'
        fi &&

        echo '✅ Ollama container fully initialized' &&
        echo '🔍 Final status check:' &&
        ollama list &&
        echo '🚀 Ollama is ready for use!' &&
        wait \$OLLAMA_PID
      "

  # Ollama Dashboard Web Interface
  ollama-dashboard:
    build:
      context: ./gateway/dashboard
      dockerfile: Dockerfile
    container_name: calcounta-ollama-dashboard
    environment:
      - FLASK_SECRET_KEY=${OLLAMA_JWT_SECRET_KEY}
      - GATEWAY_URL=http://ollama-gateway:80
    depends_on:
      - ollama-auth-service
    networks:
      - calcounta-network
    restart: unless-stopped
    volumes:
      - ./gateway/dashboard:/app
      - ollama_status:/tmp/ollama-status:ro

  # Redis for Session Storage and Rate Limiting
  redis:
    image: redis:7-alpine
    container_name: calcounta-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - calcounta-network
    restart: unless-stopped

networks:
  calcounta-network:
    driver: bridge

volumes:
  postgres_data:
  ollama_postgres_data:
  redis_data:
  ollama_status:

