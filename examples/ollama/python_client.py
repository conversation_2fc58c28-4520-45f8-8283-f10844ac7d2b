#!/usr/bin/env python3
"""
Calcounta Ollama Gateway - Python Client Example

This example demonstrates how to:
1. Register a user
2. <PERSON><PERSON> and get JWT token
3. Create API keys
4. Use API keys to access Ollama

Requirements:
    pip install requests
"""

import requests
import json
import sys
from typing import Optional, Dict, Any

class CalcountaClient:
    def __init__(self, base_url: str = "http://localhost:85"):
        self.base_url = base_url
        self.session = requests.Session()
        self.jwt_token: Optional[str] = None
        self.client_id: Optional[str] = None
        self.client_secret: Optional[str] = None
    
    def register(self, username: str, email: str, password: str) -> Dict[str, Any]:
        """Register a new user"""
        response = self.session.post(
            f"{self.base_url}/api/auth/register",
            json={
                "username": username,
                "email": email,
                "password": password
            }
        )
        
        if response.status_code == 201:
            print(f"✅ User '{username}' registered successfully")
            return response.json()
        elif response.status_code == 409:
            print(f"⚠️  User '{username}' already exists")
            return response.json()
        else:
            print(f"❌ Registration failed: {response.text}")
            response.raise_for_status()
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Login and get JWT token"""
        response = self.session.post(
            f"{self.base_url}/api/auth/login",
            json={
                "username": username,
                "password": password
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            self.jwt_token = data["access_token"]
            self.session.headers.update({
                "Authorization": f"Bearer {self.jwt_token}"
            })
            print(f"✅ Logged in as '{username}'")
            return data
        else:
            print(f"❌ Login failed: {response.text}")
            response.raise_for_status()
    
    def create_api_key(self, name: str, description: str = "") -> Dict[str, Any]:
        """Create a new API key"""
        if not self.jwt_token:
            raise ValueError("Must be logged in to create API keys")
        
        response = self.session.post(
            f"{self.base_url}/api/keys",
            json={
                "name": name,
                "description": description
            }
        )
        
        if response.status_code == 201:
            data = response.json()
            self.client_id = data["api_key"]["client_id"]
            self.client_secret = data["api_key"]["client_secret"]
            print(f"✅ API key '{name}' created successfully")
            print(f"🔑 Client ID: {self.client_id}")
            print(f"🔐 Client Secret: {self.client_secret[:20]}...")
            return data
        else:
            print(f"❌ API key creation failed: {response.text}")
            response.raise_for_status()
    
    def list_api_keys(self) -> Dict[str, Any]:
        """List user's API keys"""
        if not self.jwt_token:
            raise ValueError("Must be logged in to list API keys")
        
        response = self.session.get(f"{self.base_url}/api/keys")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📋 Found {len(data['api_keys'])} API key(s)")
            for key in data["api_keys"]:
                status = "🟢 Active" if key["is_active"] else "🔴 Inactive"
                print(f"   • {key['name']} ({key['client_id'][:16]}...) - {status}")
            return data
        else:
            print(f"❌ Failed to list API keys: {response.text}")
            response.raise_for_status()
    
    def list_models(self) -> Dict[str, Any]:
        """List available Ollama models"""
        if not self.client_id or not self.client_secret:
            raise ValueError("Must have API credentials to access Ollama")
        
        response = self.session.get(
            f"{self.base_url}/ai/api/tags",
            headers={
                "X-Client-ID": self.client_id,
                "X-Client-Secret": self.client_secret
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"🤖 Found {len(models)} model(s):")
            for model in models:
                print(f"   • {model['name']} ({model.get('size', 'unknown size')})")
            return data
        else:
            print(f"❌ Failed to list models: {response.text}")
            response.raise_for_status()
    
    def chat(self, model: str, message: str, stream: bool = False) -> Dict[str, Any]:
        """Chat with an Ollama model"""
        if not self.client_id or not self.client_secret:
            raise ValueError("Must have API credentials to access Ollama")
        
        payload = {
            "model": model,
            "messages": [
                {"role": "user", "content": message}
            ],
            "stream": stream
        }
        
        response = self.session.post(
            f"{self.base_url}/ai/api/chat",
            json=payload,
            headers={
                "X-Client-ID": self.client_id,
                "X-Client-Secret": self.client_secret
            },
            stream=stream
        )
        
        if response.status_code == 200:
            if stream:
                print(f"🤖 {model} (streaming):")
                for line in response.iter_lines():
                    if line:
                        try:
                            chunk = json.loads(line)
                            if chunk.get("message", {}).get("content"):
                                print(chunk["message"]["content"], end="", flush=True)
                        except json.JSONDecodeError:
                            continue
                print()  # New line after streaming
                return {"status": "streaming_complete"}
            else:
                data = response.json()
                content = data.get("message", {}).get("content", "No response")
                print(f"🤖 {model}: {content}")
                return data
        else:
            print(f"❌ Chat failed: {response.text}")
            response.raise_for_status()

def main():
    """Example usage of the Calcounta client"""
    print("🚀 Calcounta Ollama Gateway - Python Client Example")
    print("=" * 50)
    
    # Initialize client
    client = CalcountaClient()
    
    # User credentials
    username = "demo_user"
    email = "<EMAIL>"
    password = "secure_demo_password_123"
    
    try:
        # Step 1: Register user (or skip if already exists)
        print("\n1️⃣  Registering user...")
        client.register(username, email, password)
        
        # Step 2: Login
        print("\n2️⃣  Logging in...")
        client.login(username, password)
        
        # Step 3: Create API key
        print("\n3️⃣  Creating API key...")
        client.create_api_key("Demo API Key", "API key for demonstration")
        
        # Step 4: List API keys
        print("\n4️⃣  Listing API keys...")
        client.list_api_keys()
        
        # Step 5: List available models
        print("\n5️⃣  Listing Ollama models...")
        try:
            models_data = client.list_models()
            models = models_data.get("models", [])
            
            if not models:
                print("⚠️  No models available. Make sure Ollama is running and has models installed.")
                print("   Install a model: ollama pull llama2")
                return
            
            # Step 6: Chat with a model
            print("\n6️⃣  Testing chat...")
            model_name = models[0]["name"]
            client.chat(model_name, "Hello! Can you tell me a short joke?")
            
            print("\n🎉 Demo completed successfully!")
            print(f"\n💡 Your API credentials:")
            print(f"   Client ID: {client.client_id}")
            print(f"   Client Secret: {client.client_secret[:20]}...")
            print(f"\n🔧 You can now use these credentials in your applications!")
            
        except Exception as e:
            if "503" in str(e):
                print("⚠️  Ollama service is not available.")
                print("   Make sure Ollama is running: ollama serve")
            else:
                raise
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Calcounta Gateway.")
        print("   Make sure the gateway is running: docker-compose up -d")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
