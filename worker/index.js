require('dotenv').config();
const axios = require('axios');

console.log('AI Worker starting...');

// This is a placeholder for the AI worker
// In a real implementation, this would handle background AI processing tasks
// For now, it just logs a message and stays running

const checkHealth = async () => {
  try {
    const response = await axios.get('http://backend:86/health');
    console.log('Backend health check:', response.data);
  } catch (error) {
    console.error('Backend health check failed:', error.message);
  }
};

// Check backend health every 30 seconds
setInterval(checkHealth, 30000);

// Keep the process running
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
