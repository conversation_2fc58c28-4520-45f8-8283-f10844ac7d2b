import { createContext, useState, useContext, useCallback } from 'react';

export const LoadingContext = createContext();

export const LoadingProvider = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState({});
  const [globalMessage, setGlobalMessage] = useState('');

  // Check if any loading state is active
  const isLoading = Object.values(loadingStates).some(state => state);

  // Set loading state for a specific key
  const setLoading = useCallback((key, isLoading, message = '') => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: isLoading
    }));
    
    if (isLoading && message) {
      setGlobalMessage(message);
    } else if (!isLoading) {
      // Clear message when this loading state ends
      setGlobalMessage('');
    }
  }, []);

  // Clear all loading states
  const clearAllLoading = useCallback(() => {
    setLoadingStates({});
    setGlobalMessage('');
  }, []);

  // Get loading state for a specific key
  const getLoadingState = useCallback((key) => {
    return loadingStates[key] || false;
  }, [loadingStates]);

  const value = {
    isLoading,
    loadingStates,
    globalMessage,
    setLoading,
    clearAllLoading,
    getLoadingState
  };

  return (
    <LoadingContext.Provider value={value}>
      {children}
    </LoadingContext.Provider>
  );
};

// Custom hook to use the loading context
export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (context === undefined) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};
