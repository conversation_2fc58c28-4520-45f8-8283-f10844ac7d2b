import { createContext, useState, useEffect, useContext } from 'react';
import axios from 'axios';
import {
  loadApiConfig,
  saveApiConfig,
  clearApiConfig,
  isApiConfigured,
  validateApiCredentials,
  getAvailableProviders,
  getVisionModels,
  getAuthType,
  getDashboardUrl,
  fetchOllamaModels
} from '../services/apiConfigService';
import { useAuth } from '../hooks/useAuth';

const ApiConfigContext = createContext();

export const useApiConfig = () => {
  const context = useContext(ApiConfigContext);
  if (!context) {
    throw new Error('useApiConfig must be used within an ApiConfigProvider');
  }
  return context;
};

export const ApiConfigProvider = ({ children }) => {
  const { user } = useAuth();
  const [config, setConfig] = useState({
    provider: null,
    model: null,
    apiKey: null,
    clientId: null,
    clientSecret: null,
    isConfigured: false
  });
  const [ollamaModels, setOllamaModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [validating, setValidating] = useState(false);
  const [needsReauth, setNeedsReauth] = useState(false);
  const [availableProviders, setAvailableProviders] = useState({}); // New state for cross-device scenario

  // Load configuration on mount and when user changes
  useEffect(() => {
    const loadConfig = async () => {
      try {
        setLoading(true);

        // Load available providers first
        const providers = await getAvailableProviders();
        setAvailableProviders(providers);

        // First, try to load from backend
        const savedConfig = await loadApiConfig();

        if (savedConfig) {
          setConfig(savedConfig);
          setNeedsReauth(false);
        } else if (user) {
          // User is logged in but no API config found
          setConfig({
            provider: null,
            model: null,
            apiKey: null,
            clientId: null,
            clientSecret: null,
            isConfigured: false
          });
          setNeedsReauth(false);
        } else {
          // No user logged in
          setConfig({
            provider: null,
            model: null,
            apiKey: null,
            clientId: null,
            clientSecret: null,
            isConfigured: false
          });
          setNeedsReauth(false);
        }
      } catch (error) {
        console.error('Error loading API configuration:', error);
        setConfig({
          provider: null,
          model: null,
          apiKey: null,
          clientId: null,
          clientSecret: null,
          isConfigured: false
        });
        setNeedsReauth(false);
      } finally {
        setLoading(false);
      }
    };

    loadConfig();
  }, [user]); // Re-run when user changes (login/logout)

  // Save configuration
  const updateConfig = async (provider, model, credentials) => {
    try {
      setValidating(true);

      // Validate credentials based on provider type
      const validation = await validateApiCredentials(provider, credentials);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      const authType = getAuthType(provider);
      const newConfig = {
        provider,
        model,
        isConfigured: true
      };

      // Add appropriate credentials based on auth type
      if (authType === 'apiKey') {
        newConfig.apiKey = credentials.apiKey || credentials; // Support legacy single parameter
        newConfig.clientId = null;
        newConfig.clientSecret = null;
      } else if (authType === 'clientCredentials') {
        newConfig.clientId = credentials.clientId;
        newConfig.clientSecret = credentials.clientSecret;
        newConfig.apiKey = null;
      }

      // Save to backend
      const success = await saveApiConfig(provider, model, credentials);
      if (!success) {
        throw new Error('Failed to save API configuration to backend');
      }

      // Update state
      setConfig(newConfig);

      // Clear reauth flag since user has now configured
      setNeedsReauth(false);

      return { success: true, error: null };
    } catch (error) {
      console.error('Error updating API configuration:', error);
      return { success: false, error: 'Failed to update configuration' };
    } finally {
      setValidating(false);
    }
  };

  // Clear configuration
  const clearConfig = async () => {
    try {
      const success = await clearApiConfig();
      if (!success) {
        throw new Error('Failed to clear API configuration from backend');
      }

      setConfig({
        provider: null,
        model: null,
        apiKey: null,
        clientId: null,
        clientSecret: null,
        isConfigured: false
      });

      // Clear reauth flag
      setNeedsReauth(false);

      return true;
    } catch (error) {
      console.error('Error clearing API configuration:', error);
      return false;
    }
  };

  // Get available providers
  const getProviders = () => {
    return Object.keys(availableProviders).map(key => ({
      id: key,
      name: availableProviders[key].name,
      authType: availableProviders[key].authType
    }));
  };

  // Get available models for a provider (vision-capable only)
  const getModelsForProvider = (provider) => {
    if (provider === 'ollama' && ollamaModels.length > 0) {
      return ollamaModels;
    }
    return getVisionModels(provider);
  };

  // Fetch Ollama models dynamically
  const fetchOllamaModelsForProvider = async (clientId, clientSecret) => {
    try {
      const result = await fetchOllamaModels(clientId, clientSecret);
      if (result.success) {
        setOllamaModels(result.models);
        return { success: true, models: result.models };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error fetching Ollama models:', error);
      return { success: false, error: error.message };
    }
  };

  // Check if configuration is valid
  const isConfigValid = () => {
    if (!config.isConfigured || !config.provider || !config.model) {
      return false;
    }

    const authType = getAuthType(config.provider);
    if (authType === 'apiKey') {
      return !!config.apiKey;
    } else if (authType === 'clientCredentials') {
      return !!(config.clientId && config.clientSecret);
    }

    return false;
  };

  // Get current configuration for API calls
  const getApiConfig = () => {
    if (!isConfigValid()) {
      throw new Error('API not configured. Please configure your API settings.');
    }

    const authType = getAuthType(config.provider);
    const apiConfig = {
      provider: config.provider,
      model: config.model
    };

    if (authType === 'apiKey') {
      apiConfig.apiKey = config.apiKey;
    } else if (authType === 'clientCredentials') {
      apiConfig.clientId = config.clientId;
      apiConfig.clientSecret = config.clientSecret;
    }

    return apiConfig;
  };

  const value = {
    // State
    config,
    loading,
    validating,
    needsReauth, // New state for cross-device scenario
    availableProviders,
    ollamaModels,

    // Actions
    updateConfig,
    clearConfig,

    // Utilities
    getProviders,
    getModelsForProvider,
    fetchOllamaModelsForProvider,
    isConfigValid,
    getApiConfig,
    getAuthType,
    getDashboardUrl,

    // Direct access to service functions
    isApiConfigured
  };

  return (
    <ApiConfigContext.Provider value={value}>
      {children}
    </ApiConfigContext.Provider>
  );
};
