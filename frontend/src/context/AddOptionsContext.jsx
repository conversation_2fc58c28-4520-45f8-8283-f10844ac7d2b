import React, { createContext, useContext } from 'react';
import { useAddOptions } from '../hooks/useAddOptions';
import AddOptionsModal from '../components/modals/AddOptionsModal';
import FoodScanner from '../components/scanner/FoodScanner';

const AddOptionsContext = createContext();

export const useAddOptionsContext = () => {
  const context = useContext(AddOptionsContext);
  if (!context) {
    throw new Error('useAddOptionsContext must be used within an AddOptionsProvider');
  }
  return context;
};

export const AddOptionsProvider = ({ children }) => {
  const addOptions = useAddOptions();

  return (
    <AddOptionsContext.Provider value={addOptions}>
      {children}
      
      {/* Global Add Options Modal */}
      <AddOptionsModal
        isOpen={addOptions.showAddOptionsModal}
        onClose={addOptions.closeAddOptions}
        onAddMeal={addOptions.handleAddMeal}
      />

      {/* Global Food Scanner Modal */}
      <FoodScanner
        isOpen={addOptions.isScannerOpen}
        onClose={addOptions.closeFoodScanner}
      />
    </AddOptionsContext.Provider>
  );
};
