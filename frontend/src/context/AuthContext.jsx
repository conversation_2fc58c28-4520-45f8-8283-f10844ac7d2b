import { createContext, useState, useEffect } from 'react'
import axios from 'axios'

export const AuthContext = createContext()

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Set default axios timeout for all requests (except analysis which has its own timeout)
    axios.defaults.timeout = 60000; // 60 seconds default timeout

    // Check if user is logged in
    const token = localStorage.getItem('token')
    const userId = localStorage.getItem('userId')

    if (token && userId) {
      setUser({ id: userId, token })
      // Set default Authorization header for all requests
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }

    setLoading(false)
  }, [])

  const login = async (email, password) => {
    try {
      const response = await axios.post('/api/auth/login', { email, password })
      const { id, token } = response.data
      
      // Save to localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('userId', id)
      
      // Set default Authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      
      setUser({ id, token })
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Login failed' 
      }
    }
  }

  const register = async (userData) => {
    try {
      const response = await axios.post('/api/auth/register', userData)
      const { id, token } = response.data
      
      // Save to localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('userId', id)
      
      // Set default Authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      
      setUser({ id, token })
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.response?.data?.error || 'Registration failed' 
      }
    }
  }

  const logout = () => {
    // Remove from localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('userId')
    
    // Remove Authorization header
    delete axios.defaults.headers.common['Authorization']
    
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ user, loading, login, register, logout }}>
      {children}
    </AuthContext.Provider>
  )
}
