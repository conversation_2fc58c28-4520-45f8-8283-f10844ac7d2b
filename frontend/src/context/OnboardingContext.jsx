import { createContext, useContext, useState } from 'react';

/**
 * Types for onboarding data
 */
// Gender options
const GENDER_OPTIONS = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other'
};

// Workout frequency options
const WORKOUT_FREQUENCY = {
  NONE: 'none',
  LOW: '1-3',
  MEDIUM: '4-6',
  HIGH: '7+'
};

// Referral source options
const REFERRAL_SOURCE = {
  INSTAGRAM: 'instagram',
  FACEBOOK: 'facebook',
  TIKTOK: 'tiktok',
  YOUTUBE: 'youtube',
  GOOGLE: 'google',
  TV: 'tv',
  OTHER: 'other'
};

// Unit system options
const UNIT_SYSTEM = {
  METRIC: 'metric',
  IMPERIAL: 'imperial'
};

// Goal options
const GOAL = {
  LOSE: 'lose',
  MAINTAIN: 'maintain',
  GAIN: 'gain'
};

// Diet options
const DIET = {
  OMNIVORE: 'omnivore',
  PESCATARIAN: 'pescatarian',
  VEGETARIAN: 'vegetarian',
  VEGAN: 'vegan',
  KETO: 'keto'
};

// Obstacle options
const OBSTACLE = {
  CONSISTENCY: 'consistency',
  HABITS: 'habits',
  SUPPORT: 'support',
  MOTIVATION: 'motivation'
};

// Accomplishment options
const ACCOMPLISHMENT = {
  FIT: 'fit',
  HEALTHIER: 'healthier',
  ENERGY: 'energy',
  CONFIDENT: 'confident',
  BETTER: 'better'
};

const defaultOnboardingData = {
  gender: null,
  workoutFrequency: null,
  referralSource: null,
  unitSystem: UNIT_SYSTEM.METRIC,
  height: { cm: null, ft: null, in: null },
  weight: { kg: null, lbs: null },
  birthDate: { day: null, month: null, year: null },
  goal: null,
  targetWeight: { kg: null, lbs: null },
  goalRate: null,
  obstacle: null,
  accomplishments: [],
  diet: null,
};

const OnboardingContext = createContext();

export const OnboardingProvider = ({ children }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState(defaultOnboardingData);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [direction, setDirection] = useState('forward'); // 'forward' or 'backward'

  const totalSteps = 16;

  const updateOnboardingData = (key, value) => {
    setOnboardingData((prev) => ({ ...prev, [key]: value }));
  };

  const goToNextStep = () => {
    if (currentStep < totalSteps - 1) {
      setIsTransitioning(true);
      setDirection('forward');

      // Small delay to allow exit animation
      setTimeout(() => {
        setCurrentStep(currentStep + 1);
        window.scrollTo(0, 0);
        setIsTransitioning(false);
      }, 150);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setIsTransitioning(true);
      setDirection('backward');

      // Small delay to allow exit animation
      setTimeout(() => {
        setCurrentStep(currentStep - 1);
        window.scrollTo(0, 0);
        setIsTransitioning(false);
      }, 150);
    }
  };

  return (
    <OnboardingContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        onboardingData,
        updateOnboardingData,
        goToNextStep,
        goToPreviousStep,
        totalSteps,
        isTransitioning,
        direction,
        GENDER_OPTIONS,
        WORKOUT_FREQUENCY,
        REFERRAL_SOURCE,
        UNIT_SYSTEM,
        GOAL,
        DIET,
        OBSTACLE,
        ACCOMPLISHMENT
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
};

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
};
