@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import safe area utilities */
@import './styles/safe-area.css';

@layer base {
  html {
    @apply text-gray-900;
  }

  body {
    @apply bg-gray-100 min-h-screen;
  }

  /* Remove mobile focus highlights globally */
  * {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Allow text selection for input elements */
  input, textarea, select {
    -webkit-user-select: text;
    -khtml-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* Custom colors for exact specifications */
  .bg-custom-button {
    background-color: #242426;
  }

  .bg-custom-button-hover {
    background-color: #363638;
  }

  .text-custom-text {
    color: #FFFFFF;
  }

  .text-custom-placeholder {
    color: #8B8B8D;
  }

  .border-custom-placeholder {
    border-color: #8B8B8D;
  }

  /* Dark mode styles - exact color specifications */
  .dark body {
    background-color: #000000;
    color: #FFFFFF;
  }

  .dark .bg-white {
    background-color: #242426;
  }

  .dark .bg-gray-100 {
    background-color: #000000;
  }

  .dark .bg-gray-50 {
    background-color: #242426;
  }

  .dark .text-black {
    color: #FFFFFF;
  }

  .dark .text-gray-900 {
    color: #FFFFFF;
  }

  .dark .text-gray-700 {
    color: #FFFFFF;
  }

  .dark .text-gray-600 {
    color: #8B8B8D;
  }

  .dark .text-gray-500 {
    color: #8B8B8D;
  }

  .dark .border-gray-300 {
    border-color: #8B8B8D;
  }

  .dark .border-gray-200 {
    border-color: #8B8B8D;
  }

  .dark .border-gray-100 {
    border-color: #8B8B8D;
  }

  .dark .shadow-sm {
    @apply shadow-black/20;
  }

  .dark .card {
    background-color: #242426;
  }

  /* Dark mode form elements - exact color specifications */
  .dark input {
    background-color: #242426;
    color: #FFFFFF;
    border-color: #8B8B8D;
  }

  .dark input::placeholder {
    color: #8B8B8D;
  }

  .dark input:focus {
    background-color: #363638;
    border-color: #FFFFFF;
    ring-color: #FFFFFF;
  }

  .dark select {
    background-color: #242426;
    color: #FFFFFF;
    border-color: #8B8B8D;
  }

  .dark select option {
    background-color: #242426;
    color: #FFFFFF;
  }

  .dark textarea {
    background-color: #242426;
    color: #FFFFFF;
    border-color: #8B8B8D;
  }

  .dark textarea::placeholder {
    color: #8B8B8D;
  }

  .dark label {
    color: #FFFFFF;
  }

  /* Custom slider styling for dark mode */
  .dark .slider {
    background-color: #242426;
  }

  .dark .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #FFFFFF;
    cursor: pointer;
    border: 2px solid #242426;
  }

  .dark .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #FFFFFF;
    cursor: pointer;
    border: 2px solid #242426;
  }

  /* Dark mode background overrides */
  .dark .bg-gray-200 {
    @apply bg-gray-700;
  }

  .dark .bg-red-50 {
    @apply bg-red-900/20;
  }

  .dark .border-red-200 {
    @apply border-red-800;
  }

  .dark .text-red-700 {
    @apply text-red-300;
  }
}

/* OVERRIDE global safe area for scanner-page: force black notch for scanner */
.scanner-page body {
  background-color: black !important;
}

.scanner-page body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: env(safe-area-inset-top, 0);
  background-color: rgba(0, 0, 0, 0.9) !important;
  z-index: 99999 !important;
  pointer-events: none;
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700;
  }

  .btn-outline {
    @apply border border-gray-300 hover:bg-gray-100;
  }

  .input {
    @apply px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-4;
  }

  /* Hide scrollbars for marquee */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }

  /* macOS/iOS Design System Components */

  /* Responsive Macro Cards - Rectangular with 4:5 aspect ratio */
  .macro-card-rectangle {
    aspect-ratio: 4 / 5;
    width: 100%;
    /* Taller rectangle to accommodate icon/text (20%) and circle (80%) */
  }

  /* Ensure aspect-ratio support for older browsers */
  @supports not (aspect-ratio: 4 / 5) {
    .macro-card-rectangle::before {
      content: '';
      display: block;
      padding-top: 125%; /* 4:5 Aspect Ratio (5/4 = 1.25 = 125%) */
    }

    .macro-card-rectangle > * {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }

  /* iOS Card Component with Glassmorphism */
  .visionos-card {
    background: rgba(255, 255, 255, 0.08);
    @apply rounded-xl;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 1px 4px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
  }

  /* iOS Select Component with Glassmorphism */
  .visionos-select {
    @apply px-4 py-3 rounded-xl border-0 text-black;
    background: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    line-height: 1.5;
  }

  .visionos-select:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .visionos-select:focus {
    @apply ring-2 ring-black/50 ring-opacity-50;
    background: rgba(255, 255, 255, 0.18);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08);
  }

  /* iOS Button Component with Glassmorphism */
  .visionos-button {
    @apply rounded-xl border-0 transition-all duration-200 text-black;
    background: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    font-size: 16px;
    line-height: 1.5;
  }

  .visionos-button:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .visionos-button:active {
    transform: translateY(0) scale(0.98);
    background: rgba(255, 255, 255, 0.12);
    box-shadow:
      0 1px 4px rgba(0, 0, 0, 0.06),
      0 1px 2px rgba(0, 0, 0, 0.04);
  }

  /* iOS Date Input Component with Glassmorphism */
  .visionos-date-input {
    @apply px-4 py-3 rounded-xl border-0 text-black;
    background: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 16px;
    line-height: 1.5;
  }

  .visionos-date-input:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* iOS Number Input Component with Glassmorphism - Circular */
  .visionos-number-input {
    @apply px-3 py-2 rounded-full border-0 text-center text-black;
    background: rgba(255, 255, 255, 0.1);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 14px;
    font-weight: 600;
    line-height: 1.1;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .visionos-number-input:focus {
    @apply ring-2 ring-black/50 ring-opacity-50 outline-none;
    background: rgba(255, 255, 255, 0.18);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  /* Remove number input arrows */
  .visionos-number-input::-webkit-outer-spin-button,
  .visionos-number-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .visionos-number-input[type=number] {
    -moz-appearance: textfield;
  }

  /* iOS Settings List Styles */
  .ios-settings-section {
    @apply mb-8;
  }

  .ios-settings-header {
    @apply text-sm font-medium text-black/80 uppercase tracking-wide px-4 mb-3;
    font-size: 13px;
    letter-spacing: -0.08px;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
  }

  .ios-settings-group {
    background: rgba(255, 255, 255, 0.08);
    @apply rounded-xl overflow-hidden;
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
  }

  .ios-settings-item {
    @apply flex items-center justify-between px-4 py-3;
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.25);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 56px;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
  }

  .ios-settings-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 16px;
    right: 16px;
    height: 1px;
    background: linear-gradient(90deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0.1) 100%);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .ios-settings-item:last-child {
    border-bottom: none;
  }

  .ios-settings-item:last-child::after {
    display: none;
  }

  .ios-settings-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .ios-settings-item:active {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(0) scale(0.995);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  }

  .ios-settings-icon {
    @apply w-8 h-8 rounded-full flex items-center justify-center mr-3;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px) saturate(150%);
    -webkit-backdrop-filter: blur(10px) saturate(150%);
  }

  .ios-settings-label {
    @apply text-black font-medium;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.32px;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
  }

  .ios-settings-value {
    @apply text-black/80;
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.32px;
    text-shadow: 0 1px 3px rgba(255, 255, 255, 0.5);
  }

  /* Remove focus highlights on mobile devices */
  .ios-settings-value:focus {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    -webkit-appearance: none !important;
    box-shadow: none !important;
  }

  /* Specifically target select elements */
  select.ios-settings-value {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
  }

  select.ios-settings-value:focus {
    outline: none !important;
    -webkit-tap-highlight-color: transparent !important;
    -webkit-focus-ring-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
  }

  /* iOS Toggle Switch */
  .ios-toggle {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
  }

  .ios-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .ios-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(229, 229, 234, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 31px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.06),
      0 1px 4px rgba(0, 0, 0, 0.04);
  }

  .ios-toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.4);
    backdrop-filter: blur(10px) saturate(180%);
    -webkit-backdrop-filter: blur(10px) saturate(180%);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.15),
      0 3px 10px rgba(0, 0, 0, 0.1),
      0 1px 4px rgba(0, 0, 0, 0.06);
  }

  .ios-toggle input:checked + .ios-toggle-slider {
    background: rgba(52, 199, 89, 0.9);
    box-shadow:
      0 6px 20px rgba(52, 199, 89, 0.3),
      0 3px 10px rgba(52, 199, 89, 0.2),
      0 1px 4px rgba(52, 199, 89, 0.15);
  }

  .ios-toggle input:checked + .ios-toggle-slider:before {
    transform: translateX(20px);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.2),
      0 4px 12px rgba(0, 0, 0, 0.12),
      0 2px 6px rgba(0, 0, 0, 0.08);
  }

  .ios-toggle input:disabled + .ios-toggle-slider {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* iOS Button Styles with Glassmorphism */
  .ios-button-primary {
    @apply text-white font-medium rounded-xl px-6 py-3;
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.9) 0%, rgba(0, 122, 255, 0.8) 100%);
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.32px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 8px 32px rgba(0, 122, 255, 0.3),
      0 4px 16px rgba(0, 122, 255, 0.2),
      0 2px 8px rgba(0, 122, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .ios-button-primary:hover {
    background: linear-gradient(135deg, rgba(0, 81, 213, 0.95) 0%, rgba(0, 81, 213, 0.85) 100%);
    box-shadow:
      0 12px 40px rgba(0, 122, 255, 0.4),
      0 6px 20px rgba(0, 122, 255, 0.3),
      0 3px 12px rgba(0, 122, 255, 0.2);
    transform: translateY(-2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .ios-button-primary:active {
    transform: translateY(0) scale(0.98);
    background: linear-gradient(135deg, rgba(0, 122, 255, 0.85) 0%, rgba(0, 122, 255, 0.75) 100%);
    box-shadow:
      0 4px 16px rgba(0, 122, 255, 0.25),
      0 2px 8px rgba(0, 122, 255, 0.2);
  }

  .ios-button-secondary {
    @apply text-ios-label font-medium rounded-xl px-6 py-3;
    background: rgba(242, 242, 247, 0.8);
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.32px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4px 16px rgba(0, 0, 0, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.06),
      0 1px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
  }

  .ios-button-secondary:hover {
    background: rgba(229, 229, 234, 0.9);
    box-shadow:
      0 6px 20px rgba(0, 0, 0, 0.12),
      0 3px 10px rgba(0, 0, 0, 0.08),
      0 1px 5px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
    border: 1px solid rgba(255, 255, 255, 0.4);
  }

  .ios-button-secondary:active {
    transform: translateY(0) scale(0.98);
    background: rgba(209, 209, 214, 0.85);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 1px 4px rgba(0, 0, 0, 0.06);
  }

  .ios-button-destructive {
    @apply text-white font-medium rounded-xl px-6 py-3;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(239, 68, 68, 0.8) 100%);
    font-size: 16px;
    line-height: 1.5;
    letter-spacing: -0.32px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 8px 32px rgba(239, 68, 68, 0.3),
      0 4px 16px rgba(239, 68, 68, 0.2),
      0 2px 8px rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
  }

  .ios-button-destructive:hover {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.95) 0%, rgba(220, 38, 38, 0.85) 100%);
    box-shadow:
      0 12px 40px rgba(239, 68, 68, 0.4),
      0 6px 20px rgba(239, 68, 68, 0.3),
      0 3px 12px rgba(239, 68, 68, 0.2);
    transform: translateY(-2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
  }

  .ios-button-destructive:active {
    transform: translateY(0) scale(0.98);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.85) 0%, rgba(239, 68, 68, 0.75) 100%);
    box-shadow:
      0 4px 16px rgba(239, 68, 68, 0.25),
      0 2px 8px rgba(239, 68, 68, 0.2);
  }

  /* Glassmorphism Fallbacks for Unsupported Browsers */
  @supports not (backdrop-filter: blur(20px)) {
    .visionos-card,
    .ios-settings-group {
      background: rgba(255, 255, 255, 0.25);
    }

    .visionos-select,
    .visionos-button,
    .visionos-date-input,
    .visionos-number-input {
      background: rgba(255, 255, 255, 0.2);
    }

    .ios-settings-item {
      background: rgba(255, 255, 255, 0.1);
    }

    .ios-settings-icon {
      background: rgba(255, 255, 255, 0.15);
    }
  }

  /* Enhanced Glassmorphism for Modern Browsers */
  @supports (backdrop-filter: blur(20px)) {
    .ios-settings-group {
      background: rgba(255, 255, 255, 0.08);
    }

    .ios-settings-item:hover {
      backdrop-filter: blur(25px) saturate(200%);
      -webkit-backdrop-filter: blur(25px) saturate(200%);
    }

    .ios-settings-icon {
      position: relative;
      overflow: hidden;
    }

    .ios-settings-icon::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
      border-radius: inherit;
      pointer-events: none;
    }
  }
}

/* Scanner animations */
@keyframes scanLine {
  0% {
    transform: translateY(-40px);
  }
  50% {
    transform: translateY(40px);
  }
  100% {
    transform: translateY(-40px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* iOS-style spinner animation */
@keyframes iosSpinnerRotate {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.1;
  }
}





/* Scanner UI specific styles */

/* Scanner container - ensure it covers the entire screen including notch area */
.scanner-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  background-color: black !important;
}

.scanner-video, .scanner-image {
  position: absolute !important;
  top: calc(-1 * env(safe-area-inset-top, 0)) !important;
  left: 0 !important;
  width: 100% !important;
  height: calc(100% + env(safe-area-inset-top, 0)) !important;
  object-fit: cover !important;
}

/* Viewfinder overlay that extends to cover the notch area */
.scanner-viewfinder-overlay {
  position: absolute !important;
  top: calc(-1 * env(safe-area-inset-top, 0)) !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  height: calc(100% + env(safe-area-inset-top, 0)) !important;
}

.scanner-control-button {
  @apply w-12 h-12 rounded-full bg-black bg-opacity-40 flex items-center justify-center transition-all border border-white border-opacity-30;
}

.scanner-control-button:active {
  @apply bg-black bg-opacity-70 transform scale-95;
}

.scanner-capture-button {
  @apply w-16 h-16 rounded-full border-4 border-white flex items-center justify-center transition-all;
}

.scanner-capture-button:active {
  @apply transform scale-95;
}





.food-not-detected {
  @apply opacity-50;
}



/* Coming Soon Banner Styles - Apple Vision OS Inspired */
@keyframes comingSoonFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes comingSoonButtonHover {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.coming-soon-overlay 

.coming-soon-banner {
  width: 320px;
  max-width: 90vw;
 
}

.coming-soon-button {
  @apply w-full px-6 py-3 rounded-xl font-medium transition-all duration-200;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.coming-soon-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.coming-soon-button:hover::before {
  left: 100%;
}

.coming-soon-button:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.35) 0%, rgba(255, 255, 255, 0.15) 100%);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.coming-soon-button:active {
  transform: translateY(0);
  animation: comingSoonButtonHover 0.2s ease-out;
}

/* Analyzing food animations */
@keyframes analyzingPulse {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
}

@keyframes analyzingDots {
  0%, 20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.analyzing-dots > div:nth-child(1) {
  animation-delay: 0ms;
}

.analyzing-dots > div:nth-child(2) {
  animation-delay: 200ms;
}

.analyzing-dots > div:nth-child(3) {
  animation-delay: 400ms;
}

/* Analyzing food item shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.analyzing-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}
