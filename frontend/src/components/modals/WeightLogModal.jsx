import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Modal from '../ui/Modal';

/**
 * WeightLogModal - Modal for adding weight logs with date selection
 */
const WeightLogModal = ({ isOpen, onClose, onSuccess }) => {
  const [weight, setWeight] = useState('');
  const [date, setDate] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Set default date to today when modal opens
  useEffect(() => {
    if (isOpen) {
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      setDate(formattedDate);
      setWeight('');
      setError('');
    }
  }, [isOpen]);

  const handleSave = async () => {
    const weightValue = parseFloat(weight);
    
    // Basic validation
    if (!weight || isNaN(weightValue) || weightValue <= 0) {
      setError('Please enter a valid weight');
      return;
    }

    if (weightValue < 30 || weightValue > 300) {
      setError('Weight must be between 30 and 300 kg');
      return;
    }

    if (!date) {
      setError('Please select a date');
      return;
    }

    // Check if date is in the future
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // Set to end of today
    
    if (selectedDate > today) {
      setError('Cannot log weight for future dates');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      // Create timestamp for the selected date (noon of that day)
      const timestamp = new Date(date + 'T12:00:00.000Z').toISOString();
      
      await axios.post('/api/weight', {
        weightKg: weightValue,
        timestamp: timestamp
      });

      onSuccess();
    } catch (err) {
      console.error('Error saving weight log:', err);
      if (err.response?.status === 400) {
        setError('Invalid weight data. Please check your input.');
      } else {
        setError('Failed to save weight log. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setWeight('');
    setDate('');
    setError('');
    setLoading(false);
    onClose();
  };

  // Get max date (today)
  const maxDate = new Date().toISOString().split('T')[0];
  
  // Get min date (1 year ago)
  const minDate = new Date();
  minDate.setFullYear(minDate.getFullYear() - 1);
  const minDateString = minDate.toISOString().split('T')[0];

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-4">Add Weight Log</h2>
        
        <div className="space-y-4">
          {/* Weight Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Weight (kg)
            </label>
            <input
              type="number"
              value={weight}
              onChange={(e) => setWeight(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Enter your weight"
              step="0.1"
              min="30"
              max="300"
              autoFocus
            />
          </div>

          {/* Date Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              min={minDateString}
              max={maxDate}
            />
            <p className="text-xs text-gray-500 mt-1">
              You can log weight for any date up to 1 year ago
            </p>
          </div>

          {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>

        <div className="flex gap-3 mt-6">
          <button
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 transition-colors"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save Weight Log'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default WeightLogModal;
