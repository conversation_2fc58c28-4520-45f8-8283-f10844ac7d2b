import React, { useState, useEffect } from 'react';
import Modal from '../ui/Modal';

/**
 * TargetWeightInputModal - Simple modal with input field for target weight update
 */
const TargetWeightInputModal = ({ isOpen, onClose, onSave, currentTargetWeight, weightUnit = 'kg' }) => {
  const [targetWeight, setTargetWeight] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Set initial target weight when modal opens
  useEffect(() => {
    if (isOpen && currentTargetWeight) {
      setTargetWeight(currentTargetWeight.toString());
      setError('');
    }
  }, [isOpen, currentTargetWeight]);

  const handleSave = async () => {
    const targetWeightValue = parseFloat(targetWeight);
    
    // Basic validation
    if (!targetWeight || isNaN(targetWeightValue) || targetWeightValue <= 0) {
      setError('Please enter a valid target weight');
      return;
    }

    if (targetWeightValue < 30 || targetWeightValue > 300) {
      setError('Target weight must be between 30 and 300 kg');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await onSave(targetWeightValue);
      onClose();
    } catch (err) {
      setError('Failed to update target weight. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setTargetWeight('');
    setError('');
    setLoading(false);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-4">Update Target Weight</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Target Weight ({weightUnit})
          </label>
          <input
            type="number"
            value={targetWeight}
            onChange={(e) => setTargetWeight(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            placeholder="Enter your target weight"
            step="0.1"
            min="30"
            max="300"
            autoFocus
          />
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default TargetWeightInputModal;
