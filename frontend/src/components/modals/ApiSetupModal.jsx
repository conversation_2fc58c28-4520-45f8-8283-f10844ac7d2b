import { useState, useEffect } from 'react';
import { useApiConfig } from '../../context/ApiConfigContext';

const ApiSetupModal = ({ isOpen, onClose, isRequired = false }) => {
  const { updateConfig, getProviders, getModelsForProvider, fetchOllamaModelsForProvider, validating, needsReauth, config, getAuthType, getDashboardUrl } = useApiConfig();
  const [selectedProvider, setSelectedProvider] = useState('');
  const [selectedModel, setSelectedModel] = useState('');
  const [apiKey, setApiKey] = useState('');
  const [clientId, setClientId] = useState('');
  const [clientSecret, setClientSecret] = useState('');
  const [error, setError] = useState('');
  const [availableModels, setAvailableModels] = useState([]);
  const [dashboardUrl, setDashboardUrl] = useState('');
  const [fetchingModels, setFetchingModels] = useState(false);
  const [modelsFetched, setModelsFetched] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      if (config.isConfigured && config.provider && config.model) {
        // Pre-fill provider and model for existing config
        setSelectedProvider(config.provider);
        setSelectedModel(config.model);

        // Pre-fill credentials based on auth type
        const authType = getAuthType(config.provider);
        if (authType === 'apiKey' && config.apiKey) {
          setApiKey(config.apiKey);
          setClientId('');
          setClientSecret('');
        } else if (authType === 'clientCredentials' && config.clientId && config.clientSecret) {
          setClientId(config.clientId);
          setClientSecret(config.clientSecret);
          setApiKey('');
        }
      } else {
        setSelectedProvider('');
        setSelectedModel('');
        setApiKey('');
        setClientId('');
        setClientSecret('');
      }
      setError('');
    }
  }, [isOpen, config, getAuthType]);

  // Update available models when provider changes
  useEffect(() => {
    if (selectedProvider) {
      const models = getModelsForProvider(selectedProvider);
      setAvailableModels(models);

      // Reset Ollama-specific state when provider changes
      if (selectedProvider === 'ollama') {
        setModelsFetched(false);
      } else {
        setModelsFetched(true); // Other providers don't need fetching
      }

      // If we have a pre-filled model and it's valid for this provider, keep it
      // Otherwise, reset the model selection
      if (selectedModel && !models.find(m => m.id === selectedModel)) {
        setSelectedModel('');
      }
    } else {
      setAvailableModels([]);
      setSelectedModel('');
      setModelsFetched(false);
    }
  }, [selectedProvider, getModelsForProvider]);

  // Reset models fetched state when credentials change for Ollama
  useEffect(() => {
    if (selectedProvider === 'ollama') {
      setModelsFetched(false);
    }
  }, [clientId, clientSecret, selectedProvider]);

  // Load dashboard URL for Ollama
  useEffect(() => {
    if (selectedProvider === 'ollama') {
      getDashboardUrl('ollama').then(url => {
        setDashboardUrl(url || '');
      });
    } else {
      setDashboardUrl('');
    }
  }, [selectedProvider, getDashboardUrl]);

  // Function to fetch Ollama models
  const handleFetchOllamaModels = async () => {
    if (!clientId || !clientSecret) {
      setError('Please enter both Client ID and Client Secret to fetch models');
      return;
    }

    setFetchingModels(true);
    setError('');

    try {
      const result = await fetchOllamaModelsForProvider(clientId, clientSecret);
      if (result.success) {
        setModelsFetched(true);
        // Refresh available models
        const models = getModelsForProvider(selectedProvider);
        setAvailableModels(models);
        if (models.length > 0 && !selectedModel) {
          setSelectedModel(models[0].id);
        }
      } else {
        setError(result.error || 'Failed to fetch models');
        setModelsFetched(false);
      }
    } catch (error) {
      setError('Failed to fetch models: ' + error.message);
      setModelsFetched(false);
    } finally {
      setFetchingModels(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    if (!selectedProvider || !selectedModel) {
      setError('Please select a provider and model');
      return;
    }

    const authType = getAuthType(selectedProvider);
    let credentials;

    if (authType === 'apiKey') {
      if (!apiKey.trim()) {
        setError('Please enter your API key');
        return;
      }
      credentials = { apiKey: apiKey.trim() };
    } else if (authType === 'clientCredentials') {
      if (!clientId.trim() || !clientSecret.trim()) {
        setError('Please enter both Client ID and Client Secret');
        return;
      }
      credentials = {
        clientId: clientId.trim(),
        clientSecret: clientSecret.trim()
      };
    } else {
      setError('Invalid authentication type');
      return;
    }

    const result = await updateConfig(selectedProvider, selectedModel, credentials);

    if (result.success) {
      onClose();
    } else {
      setError(result.error || 'Failed to save configuration');
    }
  };

  const handleClose = () => {
    if (!isRequired) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const providers = getProviders();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {needsReauth ? 'Re-enter API Key' :
             isRequired ? 'Setup Required' :
             config.isConfigured ? 'Update AI Configuration' :
             'API Configuration'}
          </h2>
          {!isRequired && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {needsReauth && (
            <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-800 rounded-xl">
              <p className="text-amber-800 dark:text-amber-200 text-sm">
                <strong>Welcome back!</strong> We found your API configuration ({config.provider} - {config.model})
                but need you to re-enter your API key for this device. Your API keys are stored locally for security.
              </p>
            </div>
          )}
          {isRequired && !needsReauth && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-xl">
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                To use Calcounta's food analysis features, you need to configure your own AI provider API key.
                This ensures your data stays private and you have full control over your API usage.
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Provider Selection */}
            <div>
              <label htmlFor="provider" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                AI Provider
              </label>
              <select
                id="provider"
                value={selectedProvider}
                onChange={(e) => setSelectedProvider(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                disabled={false} // Allow provider selection
              >
                <option value="">Select a provider</option>
                {providers.map(provider => (
                  <option key={provider.id} value={provider.id}>
                    {provider.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Model Selection */}
            <div>
              <label htmlFor="model" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Model (Vision-capable only)
              </label>
              <select
                id="model"
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                disabled={!selectedProvider} // Enable model selection when provider is selected
              >
                <option value="">Select a model</option>
                {availableModels.map(model => (
                  <option key={model.id} value={model.id}>
                    {model.name}
                  </option>
                ))}
              </select>
              {selectedProvider === 'ollama' && availableModels.length === 0 && !modelsFetched && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Enter your credentials and click "Fetch Models" to load available models
                </p>
              )}
              {selectedProvider && selectedProvider !== 'ollama' && availableModels.length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  No vision-capable models available for this provider
                </p>
              )}
            </div>

            {/* Authentication Fields */}
            {selectedProvider && getAuthType(selectedProvider) === 'apiKey' && (
              <div>
                <label htmlFor="apiKey" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  API Key
                </label>
                <input
                  type="password"
                  id="apiKey"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={config.isConfigured && config.apiKey ? "API key configured (enter new key to update)" : "Enter your API key"}
                  required
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {config.isConfigured && config.apiKey
                    ? "Your existing API key is pre-filled. Enter a new key to update it."
                    : "Your API key is encrypted and stored securely in the database"
                  }
                </p>
              </div>
            )}

            {selectedProvider && getAuthType(selectedProvider) === 'clientCredentials' && (
              <>
                {/* Dashboard Link */}
                {dashboardUrl && (
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-xl">
                    <p className="text-blue-800 dark:text-blue-200 text-sm mb-2">
                      <strong>Need Client ID and Secret?</strong>
                    </p>
                    <a
                      href={dashboardUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                    >
                      Open Ollama API Dashboard
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </a>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      Create an account and generate your Client ID and Secret
                    </p>
                  </div>
                )}

                {/* Client ID Input */}
                <div>
                  <label htmlFor="clientId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Client ID
                  </label>
                  <input
                    type="text"
                    id="clientId"
                    value={clientId}
                    onChange={(e) => setClientId(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={config.isConfigured && config.clientId ? "Client ID configured (enter new ID to update)" : "Enter your Client ID"}
                    required
                  />
                </div>

                {/* Client Secret Input */}
                <div>
                  <label htmlFor="clientSecret" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Client Secret
                  </label>
                  <input
                    type="password"
                    id="clientSecret"
                    value={clientSecret}
                    onChange={(e) => setClientSecret(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={config.isConfigured && config.clientSecret ? "Client Secret configured (enter new secret to update)" : "Enter your Client Secret"}
                    required
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {config.isConfigured && config.clientId && config.clientSecret
                      ? "Your existing credentials are pre-filled. Enter new credentials to update them."
                      : "Your credentials are encrypted and stored securely in the database"
                    }
                  </p>
                </div>

                {/* Fetch Models Button for Ollama */}
                <div className="flex items-center space-x-3">
                  <button
                    type="button"
                    onClick={handleFetchOllamaModels}
                    disabled={fetchingModels || !clientId || !clientSecret}
                    className="flex-1 py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium rounded-xl transition-colors text-sm"
                  >
                    {fetchingModels ? 'Fetching Models...' : 'Fetch Available Models'}
                  </button>
                  {modelsFetched && (
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Click to load the latest models available in your Ollama instance
                </p>
              </>
            )}

            {/* Error Message */}
            {error && (
              <div className="p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl">
                <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={validating}
              className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold rounded-xl transition-colors"
            >
              {validating ? 'Validating...' : 'Save Configuration'}
            </button>
          </form>

          {/* Help Text */}
          <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              How to get your credentials:
            </h4>
            <ul className="text-xs text-gray-600 dark:text-gray-300 space-y-1">
              <li>• <strong>OpenAI:</strong> Visit platform.openai.com → API keys</li>
              <li>• <strong>Gemini:</strong> Visit aistudio.google.com → Get API key</li>
              <li>• <strong>Ollama:</strong> Use the dashboard link above to create Client ID & Secret</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiSetupModal;
