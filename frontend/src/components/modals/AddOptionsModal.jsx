import React, { useState } from 'react';
import Modal from '../ui/Modal';
import WeightLogModal from './WeightLogModal';

/**
 * AddOptionsModal - Modal that gives users options to add meal or weight log
 */
const AddOptionsModal = ({ isOpen, onClose, onAddMeal }) => {
  const [showWeightLogModal, setShowWeightLogModal] = useState(false);

  const handleAddMeal = () => {
    onClose();
    onAddMeal();
  };

  const handleAddWeightLog = () => {
    setShowWeightLogModal(true);
  };

  const handleWeightLogClose = () => {
    setShowWeightLogModal(false);
  };

  const handleWeightLogSuccess = () => {
    setShowWeightLogModal(false);
    onClose();
  };

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose}>
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-6 text-center">What would you like to add?</h2>
          
          <div className="space-y-4">
            {/* Add Meal Option */}
            <button
              onClick={handleAddMeal}
              className="w-full p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:bg-blue-50 transition-all duration-200 flex items-center"
            >
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                <span className="text-white text-lg">🍽️</span>
              </div>
              <div className="text-left">
                <h3 className="font-medium text-gray-900">Add Meal</h3>
                <p className="text-sm text-gray-500">Log your food intake with AI scanning</p>
              </div>
            </button>

            {/* Add Weight Log Option */}
            <button
              onClick={handleAddWeightLog}
              className="w-full p-4 bg-white border-2 border-gray-200 rounded-xl hover:border-purple-500 hover:bg-purple-50 transition-all duration-200 flex items-center"
            >
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                <span className="text-white text-lg">⚖️</span>
              </div>
              <div className="text-left">
                <h3 className="font-medium text-gray-900">Add Weight Log</h3>
                <p className="text-sm text-gray-500">Record your weight for any date</p>
              </div>
            </button>
          </div>

          <div className="mt-6">
            <button
              onClick={onClose}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </Modal>

      {/* Weight Log Modal */}
      <WeightLogModal
        isOpen={showWeightLogModal}
        onClose={handleWeightLogClose}
        onSuccess={handleWeightLogSuccess}
      />
    </>
  );
};

export default AddOptionsModal;
