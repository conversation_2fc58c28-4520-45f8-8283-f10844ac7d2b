import React, { useState, useEffect } from 'react';
import Modal from '../ui/Modal';

/**
 * WeightInputModal - Simple modal with input field for weight update
 */
const WeightInputModal = ({ isOpen, onClose, onSave, currentWeight, weightUnit = 'kg' }) => {
  const [weight, setWeight] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Set initial weight when modal opens
  useEffect(() => {
    if (isOpen && currentWeight) {
      setWeight(currentWeight.toString());
      setError('');
    }
  }, [isOpen, currentWeight]);

  const handleSave = async () => {
    const weightValue = parseFloat(weight);
    
    // Basic validation
    if (!weight || isNaN(weightValue) || weightValue <= 0) {
      setError('Please enter a valid weight');
      return;
    }

    if (weightValue < 30 || weightValue > 300) {
      setError('Weight must be between 30 and 300 kg');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await onSave(weightValue);
      onClose();
    } catch (err) {
      setError('Failed to update weight. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setWeight('');
    setError('');
    setLoading(false);
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-4">Update Weight</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Weight ({weightUnit})
          </label>
          <input
            type="number"
            value={weight}
            onChange={(e) => setWeight(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your weight"
            step="0.1"
            min="30"
            max="300"
            autoFocus
          />
          {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>

        <div className="flex gap-3">
          <button
            onClick={handleClose}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            disabled={loading}
          >
            {loading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default WeightInputModal;
