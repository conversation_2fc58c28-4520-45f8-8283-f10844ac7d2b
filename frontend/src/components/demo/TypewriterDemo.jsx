import React, { useState } from 'react';
import TypewriterText, { TypewriterParagraph, TypewriterList } from '../ui/TypewriterText';
import { useTypewriterSequence } from '../../hooks/useTypewriterSequence';

/**
 * Demo component to test typewriter animations
 * This can be used to verify the typewriter effects are working correctly
 */
const TypewriterDemo = () => {
  const [showDemo, setShowDemo] = useState(false);
  const { shouldStartStep, markStepComplete, startNextStep } = useTypewriterSequence(3);

  const handleStartDemo = () => {
    setShowDemo(true);
  };

  const handleReset = () => {
    setShowDemo(false);
    window.location.reload(); // Simple reset
  };

  const sampleTexts = [
    "Welcome to Calcounta",
    "Your AI-powered nutrition companion",
    "Let's get started on your health journey"
  ];

  const sampleParagraph = [
    "Track your calories with ease.",
    "Get personalized nutrition insights.",
    "Reach your health goals faster."
  ];

  const sampleList = [
    "✨ AI-powered meal tracking",
    "📊 Personalized nutrition insights", 
    "🎯 Goal-oriented progress tracking",
    "📱 Easy-to-use mobile interface"
  ];

  return (
    <div className="min-h-screen bg-white dark:bg-black p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center text-black dark:text-white">
          Typewriter Animation Demo
        </h1>

        {!showDemo ? (
          <div className="text-center">
            <button
              onClick={handleStartDemo}
              className="bg-black dark:bg-white text-white dark:text-black px-6 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity"
            >
              Start Demo
            </button>
          </div>
        ) : (
          <div className="space-y-12">
            {/* Basic Typewriter Text */}
            <section>
              <h2 className="text-xl font-semibold mb-4 text-black dark:text-white">
                Basic Typewriter Effect
              </h2>
              <div className="space-y-4">
                {sampleTexts.map((text, index) => (
                  <div key={index} className="text-lg text-gray-700 dark:text-gray-300">
                    <TypewriterText
                      text={text}
                      speed={50}
                      delay={index * 1000}
                      showCursor={index === sampleTexts.length - 1}
                    />
                  </div>
                ))}
              </div>
            </section>

            {/* Typewriter Paragraph */}
            <section>
              <h2 className="text-xl font-semibold mb-4 text-black dark:text-white">
                Multi-line Typewriter
              </h2>
              <TypewriterParagraph
                lines={sampleParagraph}
                speed={40}
                lineDelay={800}
                startDelay={3500}
                className="text-lg text-gray-700 dark:text-gray-300"
              />
            </section>

            {/* Typewriter List */}
            <section>
              <h2 className="text-xl font-semibold mb-4 text-black dark:text-white">
                List Typewriter Effect
              </h2>
              <TypewriterList
                items={sampleList}
                speed={35}
                itemDelay={600}
                startDelay={6000}
                className="space-y-2"
                itemClassName="text-lg text-gray-700 dark:text-gray-300"
              />
            </section>

            {/* Step Counter Demo */}
            <section>
              <h2 className="text-xl font-semibold mb-4 text-black dark:text-white">
                Step Counter Effect
              </h2>
              <div className="text-center">
                <TypewriterText
                  text="Step 5 of 16"
                  speed={80}
                  delay={9000}
                  showCursor={false}
                />
              </div>
            </section>

            {/* Card Content Demo */}
            <section>
              <h2 className="text-xl font-semibold mb-4 text-black dark:text-white">
                Card Content Effect
              </h2>
              <div className="grid gap-4">
                {['Get fit and feel healthier', 'Boost my energy and mood', 'Stay motivated and confident'].map((text, index) => (
                  <div
                    key={index}
                    className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800"
                  >
                    <TypewriterText
                      text={text}
                      speed={35}
                      delay={10000 + index * 400}
                      showCursor={false}
                    />
                  </div>
                ))}
              </div>
            </section>

            {/* Reset Button */}
            <div className="text-center pt-8">
              <button
                onClick={handleReset}
                className="bg-gray-500 text-white px-6 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity"
              >
                Reset Demo
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TypewriterDemo;
