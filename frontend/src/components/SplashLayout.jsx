import { classNames } from '../utils/classNames';
import Logo from './ui/Logo';

/**
 * SplashLayout component for login and register pages
 * Provides a split-screen layout with an image on one side and content on the other
 */
const SplashLayout = ({
  children,
  title,
  subtitle,
  imageSrc,
  imageAlt = 'Decorative image',
  imagePosition = 'right', // 'left' or 'right'
}) => {
  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-white dark:bg-black animate-fade-in">
      {/* Content side */}
      <div
        className={classNames(
          "flex-1 flex items-center justify-center p-8",
          imagePosition === 'right' ? 'md:order-1' : 'md:order-2'
        )}
      >
        <div className="w-full max-w-md space-y-8">
          {/* Header */}
          <div className="text-center">
            <h1 className="text-center text-3xl font-extrabold text-black dark:text-white mb-2">{title}</h1>
            {subtitle && <p className="text-center text-gray-600 dark:text-gray-400">{subtitle}</p>}
          </div>

          {/* Content */}
          <div className="mt-8">
            {children}
          </div>
        </div>
      </div>

      {/* Image side */}
      <div
        className={classNames(
          "hidden md:flex md:flex-1 relative overflow-hidden",
          imagePosition === 'right' ? 'md:order-2' : 'md:order-1'
        )}
      >
        {/* Overlay for better text visibility */}
        <div className="absolute inset-0 bg-black bg-opacity-30 z-10"></div>

        {/* Image */}
        <img
          src={imageSrc}
          alt={imageAlt}
          className="absolute inset-0 w-full h-full object-cover"
        />

        {/* Overlay text */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-white z-20 p-8">
          <div className="flex flex-col items-center mb-4">
            <Logo width={80} height={80} className="mb-4" />
            <h2 className="text-4xl font-bold text-center">Calcounta</h2>
          </div>
          <p className="text-xl text-center max-w-md">
            Your AI-Powered Nutrition Assistant
          </p>
        </div>
      </div>
    </div>
  );
};

export default SplashLayout;
