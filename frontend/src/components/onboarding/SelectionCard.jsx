import { useState } from 'react';
import { classNames } from '../../utils/classNames';
import { useTheme } from '../../context/ThemeContext';
import TypewriterText from '../ui/TypewriterText';

const SelectionCard = ({
  selected,
  onClick,
  title,
  subtitle,
  icon,
  className,
  enableTypewriter = false, // Disabled by default for minimalistic approach
  typewriterDelay = 0,
  typewriterSpeed = 30
}) => {
  const { isDarkMode } = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsPressed(false);
  };

  const handleMouseDown = () => {
    setIsPressed(true);
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  return (
    <div
      className={classNames(
        'w-full p-4 rounded-2xl border transition-all duration-200 ease-out flex items-center cursor-pointer',
        'transform-gpu', // Enable hardware acceleration
        'hover:shadow-md hover:-translate-y-0.5 active:translate-y-0 active:shadow-sm',
        selected && 'animate-bounceIn',
        isPressed && 'scale-98',
        className
      )}
      style={{
        backgroundColor: selected
          ? (isDarkMode ? '#FFFFFF' : '#000000')
          : isHovered
            ? (isDarkMode ? '#363638' : '#f9fafb')
            : (isDarkMode ? '#242426' : '#ffffff'),
        color: selected
          ? (isDarkMode ? '#000000' : '#ffffff')
          : (isDarkMode ? '#FFFFFF' : '#000000'),
        borderColor: selected
          ? (isDarkMode ? '#FFFFFF' : '#000000')
          : isHovered
            ? (isDarkMode ? '#FFFFFF' : '#d1d5db')
            : (isDarkMode ? '#8B8B8D' : '#e5e7eb')
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onClick={handleClick}
    >
      {/* Icon */}
      {icon && (
        <div className={classNames(
          "mr-3 transition-transform duration-200",
          isHovered && !selected ? "scale-110" : "scale-100"
        )}>
          {icon}
        </div>
      )}

      {/* Text content */}
      <div className="text-left">
        <h3 className={classNames(
          "font-semibold transition-all duration-200",
          isHovered && !selected ? "translate-x-1" : "translate-x-0"
        )}>
          {enableTypewriter ? (
            <TypewriterText
              text={title}
              speed={typewriterSpeed}
              delay={typewriterDelay}
              showCursor={false}
            />
          ) : (
            title
          )}
        </h3>
        {subtitle && (
          <p className={classNames(
            "text-sm transition-all duration-200",
            selected ? 'text-gray-200 dark:text-custom-placeholder' : 'text-gray-500 dark:text-custom-placeholder',
            isHovered && !selected ? "translate-x-1" : "translate-x-0"
          )}>
            {enableTypewriter ? (
              <TypewriterText
                text={subtitle}
                speed={typewriterSpeed + 10}
                delay={typewriterDelay + 200}
                showCursor={false}
              />
            ) : (
              subtitle
            )}
          </p>
        )}
      </div>

      {/* Check mark for selected item */}
      {selected && (
        <div className="ml-auto animate-bounceIn">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 transition-transform duration-200"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default SelectionCard;
