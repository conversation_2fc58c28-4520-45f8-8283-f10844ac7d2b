import { classNames } from '../../utils/classNames';
import { useTheme } from '../../context/ThemeContext';

const CircularProgress = ({
  progress,
  size = 60,
  strokeWidth = 4,
  color = '#000000',
  label,
  value,
  className
}) => {
  const { isDarkMode } = useTheme();
  // Constraints
  const actualProgress = Math.min(100, Math.max(0, progress));

  // SVG parameters
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const dash = (circumference * actualProgress) / 100;
  const center = size / 2;

  return (
    <div className={classNames("flex flex-col items-center", className)}>
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size}>
          {/* Background circle */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke={isDarkMode ? "#8B8B8D" : "#E5E7EB"}
            strokeWidth={strokeWidth}
          />

          {/* Progress circle */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeDasharray={`${dash} ${circumference - dash}`}
            strokeLinecap="round"
            transform={`rotate(-90 ${center} ${center})`}
          />
        </svg>
        {value ? (
          <div
            className="absolute inset-0 flex items-center justify-center text-xs font-bold text-black"
            style={{ color: isDarkMode ? '#FFFFFF' : undefined }}
          >
            {value}
          </div>
        ) : (
          <div
            className="absolute inset-0 flex items-center justify-center text-xs font-bold text-black"
            style={{ color: isDarkMode ? '#FFFFFF' : undefined }}
          >
            {progress}%
          </div>
        )}
      </div>
      {label && (
        <span
          className="mt-2 text-xs text-gray-500"
          style={{ color: isDarkMode ? '#8B8B8D' : undefined }}
        >{label}</span>
      )}
    </div>
  );
};

export default CircularProgress;
