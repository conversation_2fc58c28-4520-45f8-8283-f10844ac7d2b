import { useOnboarding } from '../../context/OnboardingContext';
import { useTheme } from '../../context/ThemeContext';
import { classNames } from '../../utils/classNames';
import { useOnboardingAnimations } from '../../hooks/useOnboardingAnimations';
import { useStepCounterTypewriter } from '../../hooks/useTypewriterSequence';
import TypewriterText from '../ui/TypewriterText';

const OnboardingLayout = ({
  children,
  title,
  subtitle,
  showBackButton = true,
  actionButton,
  enableTypewriter = false, // Disabled by default for minimalistic approach
}) => {
  const { currentStep, totalSteps, goToPreviousStep } = useOnboarding();
  const { isDarkMode } = useTheme();
  const {
    getPageTransitionClasses,
    getTextClasses,
    getProgressStyles,
    isVisible
  } = useOnboardingAnimations();
  const { showStepText, stepText } = useStepCounterTypewriter(currentStep, totalSteps, 800);

  const progress = ((currentStep) / (totalSteps - 1)) * 100;

  return (
    <div
      className={classNames(
        "min-h-screen bg-white",
        getPageTransitionClasses('forward')
      )}
      style={{ backgroundColor: isDarkMode ? '#000000' : undefined }}
    >
      <div className="container mx-auto px-4 py-8 max-w-md">
        {/* Progress indicator */}
        <div
          className={classNames(
            "w-full h-1 bg-gray-100 mb-6 overflow-hidden rounded-full",
            isVisible ? "animate-fadeInDown" : "opacity-0"
          )}
          style={{ backgroundColor: isDarkMode ? '#8B8B8D' : undefined }}
        >
          <div
            className={classNames(
              "h-full bg-black transition-all duration-500 ease-out",
              isVisible ? "animate-progressFill" : ""
            )}
            style={{
              ...getProgressStyles(progress),
              backgroundColor: isDarkMode ? '#FFFFFF' : undefined
            }}
          ></div>
        </div>

        {/* Back button */}
        {showBackButton && currentStep > 0 && (
          <button
            onClick={goToPreviousStep}
            className={classNames(
              "p-2 rounded-full text-black mb-4 transition-all duration-200 ease-out",
              "hover:bg-gray-100 hover:scale-110 active:scale-95",
              isVisible ? "animate-staggerFadeIn" : "opacity-0"
            )}
            style={{
              color: isDarkMode ? '#FFFFFF' : undefined,
            }}
            onMouseEnter={(e) => {
              if (isDarkMode) {
                e.target.style.backgroundColor = '#242426';
              }
            }}
            onMouseLeave={(e) => {
              if (isDarkMode) {
                e.target.style.backgroundColor = 'transparent';
              }
            }}
            aria-label="Go back"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6 transition-transform duration-200"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
        )}

        {/* Header */}
        <div className="text-center mb-8">
          <h1
            className={classNames(
              "text-2xl font-bold mb-2 text-black",
              getTextClasses('title')
            )}
            style={{ color: isDarkMode ? '#FFFFFF' : undefined }}
          >{title}</h1>

          {subtitle && (
            <p
              className={classNames(
                "text-gray-600",
                getTextClasses('subtitle')
              )}
              style={{ color: isDarkMode ? '#8B8B8D' : undefined }}
            >{subtitle}</p>
          )}
        </div>

        {/* Content */}
        <div className={classNames(
          "mb-8",
          isVisible ? "animate-slideUp" : "opacity-0"
        )}>
          {children}
        </div>

        {/* Footer with action button */}
        <div className={classNames(
          "mt-8",
          isVisible ? "animate-staggerFadeIn-delay-300" : "opacity-0"
        )}>
          {actionButton}
          <div
            className={classNames(
              "text-center text-gray-500 text-sm mt-4 transition-all duration-300",
              isVisible ? "animate-fadeInUp-delay-400" : "opacity-0"
            )}
            style={{ color: isDarkMode ? '#8B8B8D' : undefined }}
          >
            <p>Step {currentStep + 1} of {totalSteps}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingLayout;
