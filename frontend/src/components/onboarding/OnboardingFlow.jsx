import { useState, useEffect } from 'react';
import { useOnboarding } from '../../context/OnboardingContext';

// Import all steps
import WelcomeScreen from './steps/WelcomeScreen';
import GenderSelection from './steps/GenderSelection';
import WorkoutFrequency from './steps/WorkoutFrequency';
import ReferralSource from './steps/ReferralSource';
import ValueProposition from './steps/ValueProposition';
import HeightWeight from './steps/HeightWeight';
import DateOfBirth from './steps/DateOfBirth';
import GoalSelection from './steps/GoalSelection';
import TargetWeight from './steps/TargetWeight';
import GoalRate from './steps/GoalRate';
import Encouragement from './steps/Encouragement';
import ObstacleIdentification from './steps/ObstacleIdentification';
import UserIntent from './steps/UserIntent';
import DietaryPreference from './steps/DietaryPreference';
import ProgressProjection from './steps/ProgressProjection';
import FinalSummary from './steps/FinalSummary';

const OnboardingFlow = ({ onComplete }) => {
  const { currentStep, onboardingData } = useOnboarding();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [displayStep, setDisplayStep] = useState(currentStep);

  // Handle step transitions with animation
  useEffect(() => {
    if (currentStep !== displayStep) {
      setIsTransitioning(true);

      // Wait for exit animation to complete before changing step
      const timer = setTimeout(() => {
        setDisplayStep(currentStep);
        setIsTransitioning(false);
      }, 300); // Match exit animation duration

      return () => clearTimeout(timer);
    }
  }, [currentStep, displayStep]);

  // Define all steps in order with their components
  const renderStep = () => {
    switch (displayStep) {
      case 0:
        return <WelcomeScreen />;
      case 1:
        return <GenderSelection />;
      case 2:
        return <WorkoutFrequency />;
      case 3:
        return <ReferralSource />;
      case 4:
        return <ValueProposition />;
      case 5:
        return <HeightWeight />;
      case 6:
        return <DateOfBirth />;
      case 7:
        return <GoalSelection />;
      case 8:
        return <TargetWeight />;
      case 9:
        return <GoalRate />;
      case 10:
        return <Encouragement />;
      case 11:
        return <ObstacleIdentification />;
      case 12:
        return <UserIntent />;
      case 13:
        return <DietaryPreference />;
      case 14:
        return <ProgressProjection />;
      case 15:
        return <FinalSummary onComplete={handleComplete} />;
      default:
        return <WelcomeScreen />;
    }
  };

  const handleComplete = () => {
    if (onComplete && typeof onComplete === 'function') {
      onComplete(onboardingData);
    }
  };

  return (
    <div className="onboarding-flow relative overflow-hidden">
      <div
        className={`transition-all duration-300 ease-in-out ${
          isTransitioning ? 'opacity-0 transform translate-x-4' : 'opacity-100 transform translate-x-0'
        }`}
        key={displayStep} // Force re-render for each step
      >
        {renderStep()}
      </div>
    </div>
  );
};

export default OnboardingFlow;
