import { useState } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { classNames } from '../../../utils/classNames';
import { useFormAnimations, useValidationAnimations } from '../../../hooks/useOnboardingAnimations';
import TypewriterText from '../../ui/TypewriterText';

const HeightWeight = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, UNIT_SYSTEM } = useOnboarding();
  const [unitSystem, setUnitSystem] = useState(onboardingData.unitSystem || UNIT_SYSTEM.METRIC);
  const { getFieldClasses } = useFormAnimations(3); // 3 form sections
  const { triggerValidation, getValidationClasses } = useValidationAnimations();

  // Height state
  const [heightCm, setHeightCm] = useState(onboardingData.height.cm || '');
  const [heightFt, setHeightFt] = useState(onboardingData.height.ft || '');
  const [heightIn, setHeightIn] = useState(onboardingData.height.in || '');

  // Weight state
  const [weightKg, setWeightKg] = useState(onboardingData.weight.kg || '');
  const [weightLbs, setWeightLbs] = useState(onboardingData.weight.lbs || '');

  const toggleUnitSystem = () => {
    const newSystem = unitSystem === UNIT_SYSTEM.METRIC ? UNIT_SYSTEM.IMPERIAL : UNIT_SYSTEM.METRIC;
    setUnitSystem(newSystem);
    updateOnboardingData('unitSystem', newSystem);
  };

  const handleHeightChange = (value, unit) => {
    if (unit === 'cm') {
      setHeightCm(value);
      updateOnboardingData('height', { ...onboardingData.height, cm: value ? parseFloat(value) : null });
    } else if (unit === 'ft') {
      setHeightFt(value);
      updateOnboardingData('height', { ...onboardingData.height, ft: value ? parseFloat(value) : null });
    } else if (unit === 'in') {
      setHeightIn(value);
      updateOnboardingData('height', { ...onboardingData.height, in: value ? parseFloat(value) : null });
    }
  };

  const handleWeightChange = (value, unit) => {
    if (unit === 'kg') {
      setWeightKg(value);
      updateOnboardingData('weight', { ...onboardingData.weight, kg: value ? parseFloat(value) : null });
    } else if (unit === 'lbs') {
      setWeightLbs(value);
      updateOnboardingData('weight', { ...onboardingData.weight, lbs: value ? parseFloat(value) : null });
    }
  };

  const isFormValid = () => {
    if (unitSystem === UNIT_SYSTEM.METRIC) {
      return heightCm && weightKg;
    } else {
      return heightFt && heightIn && weightLbs;
    }
  };

  return (
    <OnboardingLayout
      title="Height & weight"
      subtitle="This helps us calculate your daily needs"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!isFormValid()}>
          Continue
        </ActionButton>
      }
    >
      <div className="mb-8">
        {/* Unit system toggle */}
        <div className={classNames(
          "flex justify-center items-center space-x-8 mb-8",
          getFieldClasses(0)
        )}>
          <div className="flex items-center space-x-4">
            <span className={classNames(
              "font-medium text-black dark:text-white transition-all duration-200",
              unitSystem === UNIT_SYSTEM.METRIC ? "font-bold opacity-100" : "opacity-60"
            )}>
              Metric
            </span>

            {/* iOS-style toggle switch */}
            <button
              onClick={toggleUnitSystem}
              className={classNames(
                "relative inline-flex h-8 w-14 items-center rounded-full transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2",
                unitSystem === UNIT_SYSTEM.IMPERIAL
                  ? "bg-green-500 dark:bg-green-600 focus:ring-green-500"
                  : "bg-gray-300 dark:bg-gray-600 focus:ring-gray-400"
              )}
            >
              <span
                className={classNames(
                  "inline-block h-6 w-6 transform rounded-full bg-white shadow-lg transition-all duration-300 ease-in-out",
                  unitSystem === UNIT_SYSTEM.IMPERIAL ? "translate-x-7" : "translate-x-1"
                )}
              />
            </button>

            <span className={classNames(
              "font-medium text-black dark:text-white transition-all duration-200",
              unitSystem === UNIT_SYSTEM.IMPERIAL ? "font-bold opacity-100" : "opacity-60"
            )}>
              Imperial
            </span>
          </div>
        </div>

        {/* Height Input */}
        <div className={classNames(
          "mb-6",
          getFieldClasses(1)
        )}>
          <label className="block mb-2 font-medium text-black dark:text-white">Height</label>
          {unitSystem === UNIT_SYSTEM.METRIC ? (
            <div className="flex items-center">
              <input
                type="number"
                placeholder="175"
                value={heightCm}
                onChange={(e) => handleHeightChange(e.target.value, 'cm')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
                min="50"
                max="300"
                step="0.1"
              />
              <span className="ml-2 text-black dark:text-white">cm</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <div className="flex items-center flex-1">
                <input
                  type="number"
                  placeholder="5"
                  value={heightFt}
                  onChange={(e) => handleHeightChange(e.target.value, 'ft')}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
                  min="1"
                  max="9"
                />
                <span className="ml-2 text-black dark:text-white">ft</span>
              </div>
              <div className="flex items-center flex-1">
                <input
                  type="number"
                  placeholder="10"
                  value={heightIn}
                  onChange={(e) => handleHeightChange(e.target.value, 'in')}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
                  min="0"
                  max="11"
                />
                <span className="ml-2 text-black dark:text-white">in</span>
              </div>
            </div>
          )}
        </div>

        {/* Weight Input */}
        <div className={classNames(
          "mb-6",
          getFieldClasses(2)
        )}>
          <label className="block mb-2 font-medium text-black dark:text-white">Current weight</label>
          <div className="flex items-center">
            <input
              type="number"
              placeholder={unitSystem === UNIT_SYSTEM.METRIC ? "70" : "154"}
              value={unitSystem === UNIT_SYSTEM.METRIC ? weightKg : weightLbs}
              onChange={(e) => handleWeightChange(e.target.value, unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs')}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
              min={unitSystem === UNIT_SYSTEM.METRIC ? "20" : "50"}
              max={unitSystem === UNIT_SYSTEM.METRIC ? "500" : "1000"}
              step="0.1"
            />
            <span className="ml-2 text-black dark:text-white">{unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}</span>
          </div>
        </div>

        {/* Helpful tip with typewriter effect */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            <TypewriterText
              text="💡 Don't worry, you can always update this later in your profile"
              speed={25}
              delay={2000}
              showCursor={false}
            />
          </p>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default HeightWeight;
