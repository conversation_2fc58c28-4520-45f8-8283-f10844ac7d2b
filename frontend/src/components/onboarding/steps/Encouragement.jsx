import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';

const Encouragement = () => {
  const { onboardingData, goToNextStep, UNIT_SYSTEM } = useOnboarding();

  // Calculate weight difference
  const getCurrentWeight = () => onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? onboardingData.weight.kg : onboardingData.weight.lbs;
  const getTargetWeight = () => onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? onboardingData.targetWeight.kg : onboardingData.targetWeight.lbs;
  const getWeightDiff = () => {
    if (!getCurrentWeight() || !getTargetWeight()) return 0;
    return Math.abs(getTargetWeight() - getCurrentWeight());
  };

  const getGoalVerb = () => {
    switch (onboardingData.goal) {
      case 'lose':
        return 'Losing';
      case 'gain':
        return 'Gaining';
      default:
        return 'Maintaining';
    }
  };

  const weightUnit = onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs';

  return (
    <OnboardingLayout
      title="You've Got This!"
      subtitle="We're here to support you every step of the way"
      actionButton={
        <ActionButton onClick={goToNextStep}>
          Continue
        </ActionButton>
      }
    >
      <div className="py-8 text-center">
        <h1 className="text-2xl font-bold mb-6">
          {getGoalVerb()}{' '}
          <span className="text-orange-500 font-bold">
            {getWeightDiff().toFixed(1)} {weightUnit}
          </span>{' '}
          is a realistic target. It's not hard at all!
        </h1>

        <div className="text-lg mb-8">
          80% of users say that their change in weight is easier than expected when they follow their personalized plan.
        </div>

        <div className="bg-orange-50 p-6 rounded-xl">
          <p className="text-gray-800">
            With a goal of {onboardingData.goalRate} {weightUnit}/week, you're setting yourself up for
            sustainable success rather than a quick fix that won't last.
          </p>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default Encouragement;
