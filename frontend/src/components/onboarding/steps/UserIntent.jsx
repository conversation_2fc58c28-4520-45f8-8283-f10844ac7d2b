import React from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { classNames } from '../../../utils/classNames';
import TypewriterText from '../../ui/TypewriterText';
import { useCardTypewriter } from '../../../hooks/useTypewriterSequence';

const UserIntent = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, ACCOMPLISHMENT } = useOnboarding();

  const toggleAccomplishment = (accomplishment) => {
    const currentAccomplishments = [...onboardingData.accomplishments];
    const index = currentAccomplishments.indexOf(accomplishment);

    if (index === -1) {
      // Add accomplishment if not already selected
      currentAccomplishments.push(accomplishment);
    } else {
      // Remove accomplishment if already selected
      currentAccomplishments.splice(index, 1);
    }

    updateOnboardingData('accomplishments', currentAccomplishments);
  };

  const isSelected = (accomplishment) => {
    return onboardingData.accomplishments.includes(accomplishment);
  };

  const isFormValid = () => {
    return onboardingData.accomplishments.length > 0;
  };

  const goals = [
    { id: ACCOMPLISHMENT.FIT, label: 'Get fit and feel healthier' },
    { id: ACCOMPLISHMENT.ENERGY, label: 'Boost my energy and mood' },
    { id: ACCOMPLISHMENT.CONFIDENT, label: 'Stay motivated and confident' },
    { id: ACCOMPLISHMENT.HEALTHIER, label: 'Feel better about my body' },
  ];

  const { shouldShowCard, shouldAnimateContent, startCardContentAnimation } = useCardTypewriter(goals, 1200);

  // Start card content animations sequentially
  React.useEffect(() => {
    goals.forEach((_, index) => {
      startCardContentAnimation(index, 'title', index * 250);
    });
  }, [startCardContentAnimation]);

  return (
    <OnboardingLayout
      title="What would you like to accomplish?"
      subtitle="Choose all that apply to you"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!isFormValid()}>
          Continue
        </ActionButton>
      }
    >
      <div className="space-y-5 py-4">
        {goals.map((goal, index) => (
          shouldShowCard(index) && (
            <div
              key={goal.id}
              className={classNames(
                "flex items-start space-x-3 p-4 border rounded-xl cursor-pointer transition-all duration-200",
                "hover:bg-gray-50 hover:shadow-md hover:-translate-y-0.5",
                isSelected(goal.id) ? "bg-gray-50 border-black shadow-md" : "border-gray-300"
              )}
              onClick={() => toggleAccomplishment(goal.id)}
            >
              <div className={classNames(
                "flex-shrink-0 w-5 h-5 rounded border flex items-center justify-center transition-all duration-200",
                isSelected(goal.id) ? "bg-black border-black animate-bounceIn" : "border-gray-300"
              )}>
                {isSelected(goal.id) && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="cursor-pointer flex-1">
                {goal.label}
              </div>
            </div>
          )
        ))}
      </div>
    </OnboardingLayout>
  );
};

export default UserIntent;
