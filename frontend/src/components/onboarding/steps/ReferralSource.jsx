import { useOnboarding } from '../../../context/OnboardingContext';
import { useTheme } from '../../../context/ThemeContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { classNames } from '../../../utils/classNames';

const ReferralSource = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, REFERRAL_SOURCE } = useOnboarding();
  const { isDarkMode } = useTheme();

  const handleSourceSelect = (source) => {
    updateOnboardingData('referralSource', source);
  };

  const sources = [
    { id: REFERRAL_SOURCE.INSTAGRAM, label: 'Instagram' },
    { id: REFERRAL_SOURCE.FACEBOOK, label: 'Facebook' },
    { id: REFERRAL_SOURCE.GOOGLE, label: 'Google' },
    { id: REFERRAL_SOURCE.OTHER, label: 'Other' },
    { id: REFERRAL_SOURCE.TIKTOK || 'tiktok', label: 'TikTok' },
    { id: REFERRAL_SOURCE.YOUTUBE || 'youtube', label: 'YouTube' },
  ];

  return (
    <OnboardingLayout
      title="Where did you hear about us?"
      subtitle="Help us understand how you found us"
      actionButton={
        <ActionButton
          onClick={goToNextStep}
          disabled={!onboardingData.referralSource}
        >
          Continue
        </ActionButton>
      }
    >
      <div className="grid grid-cols-2 gap-3 mb-6">
        {sources.map((source) => (
          <button
            key={source.id}
            className={classNames(
              'py-3 px-4 rounded-full border font-medium transition-all duration-300',
              onboardingData.referralSource === source.id
                ? 'border-black text-white'
                : 'border-gray-200 hover:border-gray-300'
            )}
            style={{
              backgroundColor: onboardingData.referralSource === source.id
                ? (isDarkMode ? '#FFFFFF' : '#000000')
                : (isDarkMode ? '#242426' : '#ffffff'),
              color: onboardingData.referralSource === source.id
                ? (isDarkMode ? '#000000' : '#ffffff')
                : (isDarkMode ? '#FFFFFF' : '#000000'),
              borderColor: onboardingData.referralSource === source.id
                ? (isDarkMode ? '#FFFFFF' : '#000000')
                : (isDarkMode ? '#8B8B8D' : '#e5e7eb')
            }}
            onMouseEnter={(e) => {
              if (onboardingData.referralSource !== source.id) {
                e.target.style.backgroundColor = isDarkMode ? '#363638' : '#f9fafb';
                e.target.style.borderColor = isDarkMode ? '#FFFFFF' : '#d1d5db';
              }
            }}
            onMouseLeave={(e) => {
              if (onboardingData.referralSource !== source.id) {
                e.target.style.backgroundColor = isDarkMode ? '#242426' : '#ffffff';
                e.target.style.borderColor = isDarkMode ? '#8B8B8D' : '#e5e7eb';
              }
            }}
            onClick={() => handleSourceSelect(source.id)}
          >
            {source.label}
          </button>
        ))}
      </div>
    </OnboardingLayout>
  );
};

export default ReferralSource;
