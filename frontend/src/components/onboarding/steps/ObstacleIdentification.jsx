import React from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import SelectionCard from '../SelectionCard';
import { useCardTypewriter } from '../../../hooks/useTypewriterSequence';
import {
  ConsistencyIcon,
  HabitsIcon,
  SupportIcon,
  MotivationIcon
} from '../../../assets/icons/OnboardingIcons';

const ObstacleIdentification = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, OBSTACLE } = useOnboarding();

  const handleObstacleSelect = (obstacle) => {
    updateOnboardingData('obstacle', obstacle);
  };

  const obstacles = [
    {
      id: OBSTACLE.CONSISTENCY,
      label: "Lack of consistency",
      icon: <ConsistencyIcon className="w-6 h-6" />
    },
    {
      id: OBSTACLE.HABITS,
      label: "Unhealthy eating habits",
      icon: <HabitsIcon className="w-6 h-6" />
    },
    {
      id: OBSTACLE.SUPPORT,
      label: "Lack of support",
      icon: <SupportIcon className="w-6 h-6" />
    },
    {
      id: OBSTACLE.MOTIVATION,
      label: "Lack of meal inspiration",
      icon: <MotivationIcon className="w-6 h-6" />
    }
  ];

  const { shouldShowCard, shouldAnimateContent, startCardContentAnimation } = useCardTypewriter(obstacles, 1200);

  // Start card content animations sequentially
  React.useEffect(() => {
    obstacles.forEach((_, index) => {
      startCardContentAnimation(index, 'title', index * 300);
    });
  }, [startCardContentAnimation]);

  return (
    <OnboardingLayout
      title="What's stopping you from reaching your goals?"
      subtitle="Identifying obstacles helps us customize your approach"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!onboardingData.obstacle}>
          Continue
        </ActionButton>
      }
    >
      <div className="grid gap-4">
        {obstacles.map((item, index) => (
          shouldShowCard(index) && (
            <SelectionCard
              key={item.id}
              title={item.label}
              icon={item.icon}
              selected={onboardingData.obstacle === item.id}
              onClick={() => handleObstacleSelect(item.id)}
              enableTypewriter={false}
            />
          )
        ))}
      </div>
    </OnboardingLayout>
  );
};

export default ObstacleIdentification;
