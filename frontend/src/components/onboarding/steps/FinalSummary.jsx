import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import { useTheme } from '../../../context/ThemeContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { CalorieIcon, ProteinIcon, CarbsIcon, FatsIcon } from '../../../assets/icons/DashboardIcons';
import TypewriterText from '../../ui/TypewriterText';
import { useTypewriterSequence } from '../../../hooks/useTypewriterSequence';

const FinalSummary = ({ onComplete }) => {
  const { onboardingData, UNIT_SYSTEM } = useOnboarding();
  const { isDarkMode } = useTheme();

  // Loading states
  const [currentPhase, setCurrentPhase] = useState(1); // 1: Loading, 2: Results Animation, 3: Final Results
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(0);
  const [visibleResults, setVisibleResults] = useState([]);

  const loadingMessages = [
    "Customizing your meal plan...",
    "Applying BMR formula...",
    "Calculating your BMI...",
    "Analyzing your goals...",
    "Finalizing your results..."
  ];

  // Typewriter sequence for final results
  const { shouldStartStep, markStepComplete, isSequenceComplete } = useTypewriterSequence(4);

  const resultItems = [
    "Calorie recommendation",
    "Carbohydrate targets",
    "Protein requirements",
    "Fat allocation",
    "Health score calculation"
  ];

  // Phase 1: Loading Animation with progress-based card data
  useEffect(() => {
    if (currentPhase !== 1) return;

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 1; // Slower progress (1% per 150ms = 15 seconds total)

        // Show results at specific progress milestones
        if (newProgress >= 20 && !visibleResults.includes(0)) {
          setVisibleResults(prev => [...prev, 0]); // Calorie at 20%
        }
        if (newProgress >= 40 && !visibleResults.includes(1)) {
          setVisibleResults(prev => [...prev, 1]); // Carbs at 40%
        }
        if (newProgress >= 60 && !visibleResults.includes(2)) {
          setVisibleResults(prev => [...prev, 2]); // Protein at 60%
        }
        if (newProgress >= 80 && !visibleResults.includes(3)) {
          setVisibleResults(prev => [...prev, 3]); // Fat at 80%
        }
        if (newProgress >= 100 && !visibleResults.includes(4)) {
          setVisibleResults(prev => [...prev, 4]); // Health score at 100%
          setTimeout(() => setCurrentPhase(2), 1500); // Delay before phase 2
        }

        return Math.min(newProgress, 100);
      });
    }, 150);

    const messageInterval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % loadingMessages.length);
    }, 1200); // Slower message rotation

    return () => {
      clearInterval(progressInterval);
      clearInterval(messageInterval);
    };
  }, [currentPhase, loadingMessages.length, visibleResults]);

  // Phase 2: Results Animation (now just a transition phase)
  useEffect(() => {
    if (currentPhase !== 2) return;

    const timer = setTimeout(() => {
      setCurrentPhase(3);
    }, 2000); // Longer pause to appreciate the completed results

    return () => clearTimeout(timer);
  }, [currentPhase]);

  // Calculate timeframe (from ProgressProjection logic)
  const calculateTimeframe = () => {
    const getCurrentWeight = () => {
      return onboardingData.unitSystem === UNIT_SYSTEM.METRIC
        ? onboardingData.weight.kg
        : onboardingData.weight.lbs;
    };

    const getTargetWeight = () => {
      return onboardingData.unitSystem === UNIT_SYSTEM.METRIC
        ? onboardingData.targetWeight.kg
        : onboardingData.targetWeight.lbs;
    };

    const currentWeight = getCurrentWeight();
    const target = getTargetWeight();

    if (!currentWeight || !target || !onboardingData.goalRate) return null;

    const diff = Math.abs(target - currentWeight);
    const weeks = Math.ceil(diff / onboardingData.goalRate);

    const today = new Date();
    const futureDate = new Date(today);
    futureDate.setDate(today.getDate() + (weeks * 7));

    return {
      weeks,
      weightDiff: diff,
      date: futureDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })
    };
  };

  // Helper function to get weight in the right unit
  const getWeight = () => {
    if (onboardingData.unitSystem === UNIT_SYSTEM.METRIC) {
      return `${onboardingData.weight.kg} kg`;
    } else {
      return `${onboardingData.weight.lbs} lbs`;
    }
  };

  // Helper function to get height in the right unit
  const getHeight = () => {
    if (onboardingData.unitSystem === UNIT_SYSTEM.METRIC) {
      return `${onboardingData.height.cm} cm`;
    } else {
      return `${onboardingData.height.ft}'${onboardingData.height.in}"`;
    }
  };

  // Helper function to get target weight in the right unit
  const getTargetWeight = () => {
    if (onboardingData.unitSystem === UNIT_SYSTEM.METRIC) {
      return `${onboardingData.targetWeight.kg} kg`;
    } else {
      return `${onboardingData.targetWeight.lbs} lbs`;
    }
  };

  // Helper function to get goal rate in the right unit
  const getGoalRate = () => {
    if (onboardingData.unitSystem === UNIT_SYSTEM.METRIC) {
      return `${onboardingData.goalRate} kg per week`;
    } else {
      return `${(onboardingData.goalRate * 2.20462).toFixed(1)} lbs per week`;
    }
  };

  // Calculate estimated daily calorie goal
  const calculateDailyCalories = () => {
    // This is a simplified calculation
    let bmr = 0;

    if (onboardingData.gender === 'male') {
      // Harris-Benedict equation for men
      bmr = 88.362 + (13.397 * onboardingData.weight.kg) + (4.799 * onboardingData.height.cm) - (5.677 * getAge());
    } else {
      // Harris-Benedict equation for women
      bmr = 447.593 + (9.247 * onboardingData.weight.kg) + (3.098 * onboardingData.height.cm) - (4.330 * getAge());
    }

    // Activity multiplier
    let activityMultiplier = 1.2; // Sedentary
    if (onboardingData.workoutFrequency === '1-3') {
      activityMultiplier = 1.375; // Light exercise
    } else if (onboardingData.workoutFrequency === '4-6') {
      activityMultiplier = 1.55; // Moderate exercise
    } else if (onboardingData.workoutFrequency === '7+') {
      activityMultiplier = 1.725; // Heavy exercise
    }

    let tdee = bmr * activityMultiplier;

    // Adjust for goal
    if (onboardingData.goal === 'lose') {
      tdee -= 500; // Calorie deficit
    } else if (onboardingData.goal === 'gain') {
      tdee += 500; // Calorie surplus
    }

    return Math.round(tdee);
  };

  // Calculate macronutrients
  const calculateMacros = () => {
    const calories = calculateDailyCalories();

    // Standard macro distribution
    const carbsPercentage = 0.45; // 45% carbs
    const proteinPercentage = 0.25; // 25% protein
    const fatPercentage = 0.30; // 30% fat

    const carbsCalories = calories * carbsPercentage;
    const proteinCalories = calories * proteinPercentage;
    const fatCalories = calories * fatPercentage;

    return {
      carbs: Math.round(carbsCalories / 4), // 4 calories per gram
      protein: Math.round(proteinCalories / 4), // 4 calories per gram
      fat: Math.round(fatCalories / 9) // 9 calories per gram
    };
  };

  // Helper function to calculate age from birthdate
  const getAge = () => {
    const { year, month, day } = onboardingData.birthDate;
    const birthDate = new Date(year, month - 1, day);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const timeframe = calculateTimeframe();
  const macros = calculateMacros();

  return (
    <OnboardingLayout
      title={currentPhase === 3 ? "Congratulations!" : "Preparing Your Plan"}
      subtitle={currentPhase === 3 ? "Your custom plan is ready" : "Please wait while we customize your experience"}
      actionButton={
        currentPhase === 3 ? (
          <ActionButton onClick={onComplete}>
            Start Using Calcounta
          </ActionButton>
        ) : null
      }
    >
      <div className="space-y-6">
        {/* Phase 1: Loading Animation with Results */}
        {(currentPhase === 1 || currentPhase === 2) && (
          <div className="space-y-6">
            <div className="text-center space-y-4">
              <div
                className="text-lg font-medium min-h-[28px]"
                style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
              >
                {currentPhase === 1 ? loadingMessages[currentMessage] : "Finalizing your personalized plan..."}
              </div>

              <div
                className="w-full rounded-full h-3"
                style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#e5e7eb' }}
              >
                <div
                  className="bg-orange-500 h-3 rounded-full transition-all duration-150 ease-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>

              <div
                className="text-sm"
                style={{ color: isDarkMode ? '#8B8B8D' : '#6b7280' }}
              >
                {progress}% Complete
              </div>
            </div>

            {/* Results card that appears during loading */}
            <div
              className="rounded-2xl p-6 space-y-4"
              style={{ backgroundColor: isDarkMode ? '#242426' : '#000000' }}
            >
              {resultItems.map((item, index) => (
                <div
                  key={index}
                  className={`flex items-start transition-all duration-700 ${
                    visibleResults.includes(index)
                      ? 'opacity-100 transform translate-y-0'
                      : 'opacity-0 transform translate-y-4'
                  }`}
                >
                  <div className="flex items-center mr-3 mt-0.5">
                    {visibleResults.includes(index) ? (
                      <div className="flex items-center justify-center w-6 h-6 bg-white rounded-full">
                        <svg className="w-3 h-3 text-black" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    ) : (
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    )}
                  </div>
                  <span className="text-white font-medium">{item}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Phase 3: Final Results Display */}
        {currentPhase === 3 && (
          <div className="space-y-6">
            {/* Header */}
            <div className="text-center">
              <h1
                className="text-2xl font-bold mb-2"
                style={{ color: isDarkMode ? '#FFFFFF' : '#111827' }}
              >Daily recommendation</h1>
              <p
                style={{ color: isDarkMode ? '#8B8B8D' : '#6b7280' }}
              >You can edit this anytime</p>
            </div>

            {/* Macro Cards Grid */}
            <div className="grid grid-cols-2 gap-4">
              {/* Calories */}
              <div
                className="rounded-2xl border p-4 shadow-sm"
                style={{
                  backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                  borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
                }}
              >
                <div className="flex items-center mb-3">
                  <div
                    className="p-2 rounded-full mr-3"
                    style={{
                      backgroundColor: isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)'
                    }}
                  >
                    <CalorieIcon
                      className="w-4 h-4"
                      style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
                    />
                  </div>
                  <span
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
                  >Calories</span>
                </div>
                <div className="relative w-20 h-20 mx-auto mb-2">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={isDarkMode ? "#8B8B8D" : "#f3f4f6"}
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={isDarkMode ? "#FFFFFF" : "#000000"}
                      strokeWidth="2"
                      strokeDasharray="75, 100"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div
                      className="text-lg font-bold"
                      style={{ color: isDarkMode ? '#FFFFFF' : '#000000' }}
                    >{calculateDailyCalories()}</div>
                  </div>
                </div>
                <div className="text-center">
                  <svg
                    className="w-4 h-4 mx-auto"
                    fill="none"
                    stroke={isDarkMode ? "#8B8B8D" : "#9ca3af"}
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
              </div>

              {/* Carbs */}
              <div
                className="rounded-2xl border p-4 shadow-sm"
                style={{
                  backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                  borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
                }}
              >
                <div className="flex items-center mb-3">
                  <div
                    className="p-2 rounded-full mr-3"
                    style={{
                      backgroundColor: isDarkMode ? 'rgba(249,115,22,0.2)' : '#fed7aa'
                    }}
                  >
                    <CarbsIcon className="w-4 h-4" />
                  </div>
                  <span
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
                  >Carbs</span>
                </div>
                <div className="relative w-20 h-20 mx-auto mb-2">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={isDarkMode ? "#8B8B8D" : "#f3f4f6"}
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#f97316"
                      strokeWidth="2"
                      strokeDasharray="60, 100"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-lg font-bold text-orange-500">{macros.carbs}<span className="text-xs">g</span></div>
                  </div>
                </div>
                <div className="text-center">
                  <svg
                    className="w-4 h-4 mx-auto"
                    fill="none"
                    stroke={isDarkMode ? "#8B8B8D" : "#9ca3af"}
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
              </div>

              {/* Protein */}
              <div
                className="rounded-2xl border p-4 shadow-sm"
                style={{
                  backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                  borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
                }}
              >
                <div className="flex items-center mb-3">
                  <div
                    className="p-2 rounded-full mr-3"
                    style={{
                      backgroundColor: isDarkMode ? 'rgba(239,68,68,0.2)' : '#fecaca'
                    }}
                  >
                    <ProteinIcon className="w-4 h-4" />
                  </div>
                  <span
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
                  >Protein</span>
                </div>
                <div className="relative w-20 h-20 mx-auto mb-2">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={isDarkMode ? "#8B8B8D" : "#f3f4f6"}
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#ef4444"
                      strokeWidth="2"
                      strokeDasharray="45, 100"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-lg font-bold text-red-500">{macros.protein}<span className="text-xs">g</span></div>
                  </div>
                </div>
                <div className="text-center">
                  <svg
                    className="w-4 h-4 mx-auto"
                    fill="none"
                    stroke={isDarkMode ? "#8B8B8D" : "#9ca3af"}
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
              </div>

              {/* Fats */}
              <div
                className="rounded-2xl border p-4 shadow-sm"
                style={{
                  backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                  borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
                }}
              >
                <div className="flex items-center mb-3">
                  <div
                    className="p-2 rounded-full mr-3"
                    style={{
                      backgroundColor: isDarkMode ? 'rgba(59,130,246,0.2)' : '#dbeafe'
                    }}
                  >
                    <FatsIcon className="w-4 h-4" />
                  </div>
                  <span
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
                  >Fats</span>
                </div>
                <div className="relative w-20 h-20 mx-auto mb-2">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={isDarkMode ? "#8B8B8D" : "#f3f4f6"}
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#3b82f6"
                      strokeWidth="2"
                      strokeDasharray="30, 100"
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-lg font-bold text-blue-500">{macros.fat}<span className="text-xs">g</span></div>
                  </div>
                </div>
                <div className="text-center">
                  <svg
                    className="w-4 h-4 mx-auto"
                    fill="none"
                    stroke={isDarkMode ? "#8B8B8D" : "#9ca3af"}
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Health Score */}
            <div
              className="rounded-2xl border p-6 shadow-sm"
              style={{
                backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
              }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="p-2 rounded-full mr-3"
                    style={{
                      backgroundColor: isDarkMode ? 'rgba(236,72,153,0.2)' : '#fce7f3'
                    }}
                  >
                    <svg className="w-5 h-5 text-pink-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span
                    className="text-lg font-semibold"
                    style={{ color: isDarkMode ? '#FFFFFF' : '#1f2937' }}
                  >Health Score</span>
                </div>
                <div
                  className="text-2xl font-bold"
                  style={{ color: isDarkMode ? '#FFFFFF' : '#111827' }}
                >7/10</div>
              </div>
              <div className="mt-4">
                <div
                  className="w-full rounded-full h-2"
                  style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#e5e7eb' }}
                >
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>
            </div>

            {/* How to reach your goals */}
            <div
              className="rounded-2xl border p-6 shadow-sm"
              style={{
                backgroundColor: isDarkMode ? '#242426' : '#ffffff',
                borderColor: isDarkMode ? '#8B8B8D' : '#f3f4f6'
              }}
            >
              <h3
                className="text-lg font-semibold mb-4"
                style={{ color: isDarkMode ? '#FFFFFF' : '#1f2937' }}
              >How to reach your goals:</h3>
              <ul
                className="space-y-3 text-sm"
                style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
              >
                <li className="flex items-start">
                  <div
                    className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                    style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#9ca3af' }}
                  ></div>
                  Track your food intake daily
                </li>
                <li className="flex items-start">
                  <div
                    className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                    style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#9ca3af' }}
                  ></div>
                  Follow your daily calorie recommendation
                </li>
                <li className="flex items-start">
                  <div
                    className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                    style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#9ca3af' }}
                  ></div>
                  Balance your carbs, proteins, and fats
                </li>
                <li className="flex items-start">
                  <div
                    className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                    style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#9ca3af' }}
                  ></div>
                  Stay consistent with your plan
                </li>
                <li className="flex items-start">
                  <div
                    className="w-2 h-2 rounded-full mt-2 mr-3 flex-shrink-0"
                    style={{ backgroundColor: isDarkMode ? '#8B8B8D' : '#9ca3af' }}
                  ></div>
                  Monitor your progress weekly
                </li>
              </ul>
            </div>

            {/* Let's get started button */}
            <div className="mt-8">
              <ActionButton
                onClick={onComplete}
                className="w-full bg-gray-900 text-white py-4 rounded-2xl text-lg font-semibold"
              >
                Let's get started!
              </ActionButton>
            </div>
          </div>
        )}
      </div>
    </OnboardingLayout>
  );
};

export default FinalSummary;
