import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';

const GoalRate = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, UNIT_SYSTEM } = useOnboarding();

  // Skip this step if goal is "maintain"
  if (onboardingData.goal === 'maintain') {
    setTimeout(goToNextStep, 0);
    return null;
  }

  const [sliderValue, setSliderValue] = useState(onboardingData.goalRate || 0.5);

  const minRate = 0.2;
  const maxRate = 1.0;

  useEffect(() => {
    updateOnboardingData('goalRate', sliderValue);
  }, [sliderValue]);

  const handleSliderChange = (e) => {
    setSliderValue(parseFloat(e.target.value));
  };

  // Calculate estimated weeks to goal
  const calculateWeeksToGoal = () => {
    const current = onboardingData.weight[onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'];
    const target = onboardingData.targetWeight[onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'];
    const diff = Math.abs(target - current);

    // If no difference or no rate, return 0
    if (diff === 0 || sliderValue === 0) return 0;

    const weeks = Math.ceil(diff / sliderValue);
    return weeks;
  };

  return (
    <OnboardingLayout
      title="How fast do you want to reach your goal?"
      subtitle="Choose a rate that's sustainable for you"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={sliderValue === 0}>
          Continue
        </ActionButton>
      }
    >
      <div className="py-8">
        <div className="mb-8">
          <div className="text-center mb-4">
            <span className="text-4xl font-bold">{sliderValue.toFixed(1)}</span>
            <span className="text-xl ml-1">
              {onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}/week
            </span>
          </div>

          <div className="flex justify-between items-center mb-6">
            <div className="text-xl">🐢</div>
            <div className="flex-1 mx-4">
              <input
                type="range"
                min={minRate}
                max={maxRate}
                step={0.1}
                value={sliderValue}
                onChange={handleSliderChange}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
            </div>
            <div className="text-xl">🐇</div>
          </div>

          <div className="text-center mt-8">
            <p className="text-gray-600">
              With this pace, you'll reach your goal in approximately:
            </p>
            <p className="font-bold text-lg mt-2">
              {calculateWeeksToGoal()} weeks
            </p>
          </div>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default GoalRate;
