import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { classNames } from '../../../utils/classNames';
import TypewriterText from '../../ui/TypewriterText';

const DateOfBirth = () => {
  const { onboardingData, updateOnboardingData, goToNextStep } = useOnboarding();

  const [day, setDay] = useState(onboardingData.birthDate.day || '');
  const [month, setMonth] = useState(onboardingData.birthDate.month || '');
  const [year, setYear] = useState(onboardingData.birthDate.year || '');
  const [age, setAge] = useState(null);

  // Generate options for days, months, and years
  const days = Array.from({ length: 31 }, (_, i) => i + 1);
  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 85 }, (_, i) => currentYear - 16 - i);

  // Calculate age when date changes
  useEffect(() => {
    if (day && month && year) {
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let calculatedAge = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        calculatedAge--;
      }
      setAge(calculatedAge);
    } else {
      setAge(null);
    }
  }, [day, month, year]);

  const handleDayChange = (e) => {
    const value = e.target.value;
    setDay(value);
    updateOnboardingData('birthDate', { ...onboardingData.birthDate, day: value ? parseInt(value) : null });
  };

  const handleMonthChange = (e) => {
    const value = e.target.value;
    setMonth(value);
    updateOnboardingData('birthDate', { ...onboardingData.birthDate, month: value ? parseInt(value) : null });
  };

  const handleYearChange = (e) => {
    const value = e.target.value;
    setYear(value);
    updateOnboardingData('birthDate', { ...onboardingData.birthDate, year: value ? parseInt(value) : null });
  };

  const isFormValid = () => {
    return day && month && year && age >= 13;
  };

  return (
    <OnboardingLayout
      title="When were you born?"
      subtitle="This helps us give you age-appropriate recommendations"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!isFormValid()}>
          Continue
        </ActionButton>
      }
    >
      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium mb-1 text-black dark:text-white">Day</label>
          <select
            value={day}
            onChange={handleDayChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
          >
            <option value="">Day</option>
            {days.map((d) => (
              <option key={d} value={d}>
                {d}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-black dark:text-white">Month</label>
          <select
            value={month}
            onChange={handleMonthChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
          >
            <option value="">Month</option>
            {months.map((m) => (
              <option key={m.value} value={m.value}>
                {m.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1 text-black dark:text-white">Year</label>
          <select
            value={year}
            onChange={handleYearChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-2xl bg-white text-black focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
          >
            <option value="">Year</option>
            {years.map((y) => (
              <option key={y} value={y}>
                {y}
              </option>
            ))}
          </select>
        </div>
      </div>

      {age !== null && (
        <div className="text-center mt-4">
          <span className="text-lg font-medium text-black dark:text-white">
            {age < 13 ? (
              <span className="text-red-500 dark:text-red-400">You must be at least 13 years old to use this app</span>
            ) : (
              <span>You are {age} years old</span>
            )}
          </span>
        </div>
      )}

      {/* Privacy tip with typewriter effect */}
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          <TypewriterText
            text="🔒 Your personal information is kept private and secure"
            speed={25}
            delay={2500}
            showCursor={false}
          />
        </p>
      </div>
    </OnboardingLayout>
  );
};

export default DateOfBirth;
