import { useState, useEffect, useRef } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import { useIsMobile } from '../../../hooks/useIsMobile';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';

const TargetWeight = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, UNIT_SYSTEM } = useOnboarding();
  const isMobile = useIsMobile();
  const scaleRef = useRef(null);
  const isScrollingRef = useRef(false);

  const [weightValue, setWeightValue] = useState(
    onboardingData.unitSystem === UNIT_SYSTEM.METRIC
      ? onboardingData.targetWeight.kg || onboardingData.weight.kg || 70
      : onboardingData.targetWeight.lbs || onboardingData.weight.lbs || 150
  );

  // Set min and max values for the slider
  const minWeight = onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 40 : 88;
  const maxWeight = onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 150 : 330;

  // Update target weight when weight value changes
  useEffect(() => {
    // Only update if weightValue is a valid number
    if (weightValue !== '' && !isNaN(weightValue)) {
      if (onboardingData.unitSystem === UNIT_SYSTEM.METRIC) {
        updateOnboardingData('targetWeight', { ...onboardingData.targetWeight, kg: weightValue });
      } else {
        updateOnboardingData('targetWeight', { ...onboardingData.targetWeight, lbs: weightValue });
      }
    }
  }, [weightValue]);

  const handleWeightChange = (e) => {
    const inputValue = e.target.value;

    // Allow empty string (for clearing the field)
    if (inputValue === '') {
      setWeightValue('');
      return;
    }

    const value = parseFloat(inputValue);
    if (!isNaN(value)) {
      setWeightValue(value);
    }
  };

  const getCurrentWeight = () => {
    return onboardingData.unitSystem === UNIT_SYSTEM.METRIC
      ? onboardingData.weight.kg
      : onboardingData.weight.lbs;
  };

  const getDifference = () => {
    const current = getCurrentWeight();
    const target = weightValue;
    if (!current || !target || target === '' || isNaN(target)) return '';

    const diff = target - current;
    return diff > 0 ? `+${diff.toFixed(1)}` : diff.toFixed(1);
  };

  // Infinite scroll scale picker state and functions
  const rulerRef = useRef(null);
  const scrollTimeoutRef = useRef(null);
  const tickSpacing = 12; // Tighter spacing for more accurate scale appearance
  const ticksPerUnit = 10; // 10 ticks per unit (0.1 increments)

  // Calculate the range of values we need to display
  const totalRange = maxWeight - minWeight;
  const totalTicks = Math.ceil(totalRange * ticksPerUnit);
  const centerTickIndex = Math.round((weightValue - minWeight) * ticksPerUnit);

  // Generate all possible ticks for the entire range with proper scale hierarchy
  const generateAllTicks = () => {
    const ticks = [];
    for (let i = 0; i <= totalTicks; i++) {
      const value = parseFloat((minWeight + i * 0.1).toFixed(1));

      // Check if this is a whole number (no decimal part)
      const isWholeNumber = Math.abs(value % 1) < 0.01;

      // Check for different tick types like a real physical scale
      const isMultipleOfTen = isWholeNumber && Math.abs(value % 10) < 0.01;
      const isMultipleOfFive = isWholeNumber && Math.abs(value % 5) < 0.01;
      const isWholeUnit = isWholeNumber;

      // Tick hierarchy like a real ruler:
      // - Long ticks: multiples of 10 (40, 50, 60, 70, 80, etc.) - tallest with numbers
      // - Medium ticks: multiples of 5 but not 10 (45, 55, 65, 75, etc.) - medium height with numbers
      // - Short ticks: whole numbers (41, 42, 43, 44, 46, 47, 48, 49, etc.) - shorter, no numbers
      // - Tiny ticks: 0.1 increments (40.1, 40.2, 40.3, etc.) - shortest, no numbers

      const isLong = isMultipleOfTen;
      const isMedium = isMultipleOfFive && !isMultipleOfTen;
      const isShort = isWholeUnit && !isMultipleOfFive;
      const isTiny = !isWholeUnit;

      // Show numbers only on long ticks (every 10) and medium ticks (every 5)
      const showLabel = isLong || isMedium;

      ticks.push({
        value,
        isLong,
        isMedium,
        isShort,
        isTiny,
        showLabel,
        index: i,
        key: `tick-${i}`
      });
    }
    return ticks;
  };

  const allTicks = generateAllTicks();

  const updateValue = () => {
    if (!scaleRef.current || isScrollingRef.current) return;

    const container = scaleRef.current;
    const centerOffset = container.scrollLeft + container.clientWidth / 2;

    // Account for the left padding when calculating tick position
    const leftPadding = container.clientWidth / 2 - 6; // 50vw - 6px (adjusted for new spacing)
    const adjustedOffset = centerOffset - leftPadding;

    // Calculate which tick is at the center
    const tickIndex = Math.round(adjustedOffset / tickSpacing);

    // Ensure the tick index is within bounds and get the actual tick
    const clampedTickIndex = Math.max(0, Math.min(tickIndex, allTicks.length - 1));
    const currentTick = allTicks[clampedTickIndex];

    if (currentTick && Math.abs(currentTick.value - weightValue) > 0.05) {
      setWeightValue(currentTick.value);
    }
  };

  const snapToNearestValue = () => {
    if (!scaleRef.current || isScrollingRef.current) return;

    const container = scaleRef.current;
    const centerOffset = container.scrollLeft + container.clientWidth / 2;

    // Account for the left padding when calculating snap position
    const leftPadding = container.clientWidth / 2 - 6; // 50vw - 6px (adjusted for new spacing)
    const adjustedOffset = centerOffset - leftPadding;
    const exactTickIndex = adjustedOffset / tickSpacing;
    const nearestTickIndex = Math.round(exactTickIndex);

    // Ensure the tick index is within bounds
    const clampedTickIndex = Math.max(0, Math.min(nearestTickIndex, allTicks.length - 1));

    // Calculate the target scroll position
    const targetScrollLeft = leftPadding + (clampedTickIndex * tickSpacing) - container.clientWidth / 2;

    // Only snap if we're not already perfectly aligned
    if (Math.abs(exactTickIndex - clampedTickIndex) > 0.01) {
      isScrollingRef.current = true;
      container.scrollTo({
        left: Math.max(0, Math.min(targetScrollLeft, container.scrollWidth - container.clientWidth)),
        behavior: 'smooth'
      });

      // Update the value after snapping
      setTimeout(() => {
        updateValue();
        isScrollingRef.current = false;
      }, 150);
    }
  };

  const handleScaleScroll = () => {
    updateValue();

    // Clear existing timeout and set new one for snap behavior
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    scrollTimeoutRef.current = setTimeout(() => {
      snapToNearestValue();
    }, 150); // Snap after 150ms of no scrolling
  };

  const scrollToValue = (value) => {
    if (!scaleRef.current) return;

    isScrollingRef.current = true;
    const container = scaleRef.current;

    // Calculate the tick index for the target value
    const tickIndex = Math.round((value - minWeight) * ticksPerUnit);

    // Account for the left padding and center the tick perfectly
    const leftPadding = container.clientWidth / 2 - 6; // 50vw - 6px (adjusted for new spacing)
    const targetScrollLeft = leftPadding + (tickIndex * tickSpacing) - container.clientWidth / 2;

    container.scrollTo({
      left: Math.max(0, Math.min(targetScrollLeft, container.scrollWidth - container.clientWidth)),
      behavior: 'smooth'
    });

    setTimeout(() => {
      isScrollingRef.current = false;
    }, 300);
  };

  // Initialize scale position when component mounts
  useEffect(() => {
    if (scaleRef.current && !isScrollingRef.current) {
      // Center the scale on the current weight value
      scrollToValue(weightValue);
    }
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Handle click to scroll
  const handleScaleClick = (e) => {
    if (!scaleRef.current) return;

    const containerRect = scaleRef.current.getBoundingClientRect();
    const clickX = e.clientX;
    const centerX = containerRect.left + containerRect.width / 2;
    const direction = clickX > centerX ? 1 : -1;

    // Clear any existing scroll timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Calculate current tick and move to next/previous
    const container = scaleRef.current;
    const centerOffset = container.scrollLeft + container.clientWidth / 2;
    const leftPadding = container.clientWidth / 2 - 6; // 50vw - 6px (adjusted for new spacing)
    const adjustedOffset = centerOffset - leftPadding;
    const currentTickIndex = Math.round(adjustedOffset / tickSpacing);
    const newTickIndex = Math.max(0, Math.min(currentTickIndex + direction, allTicks.length - 1));
    const targetScrollLeft = leftPadding + (newTickIndex * tickSpacing) - container.clientWidth / 2;

    container.scrollTo({
      left: Math.max(0, Math.min(targetScrollLeft, container.scrollWidth - container.clientWidth)),
      behavior: 'smooth'
    });

    // Update value after scroll
    setTimeout(() => {
      updateValue();
    }, 200);
  };

  return (
    <OnboardingLayout
      title="What is your desired weight?"
      subtitle={isMobile ? "Drag the slider to set your target" : "Enter your target weight"}
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!weightValue || weightValue === '' || isNaN(weightValue)}>
          Continue
        </ActionButton>
      }
    >
      <div className="py-8">
        <div className="mb-8">
          <div className="text-center mb-4">
            <span className="text-4xl font-bold text-black dark:text-white">{weightValue}</span>
            <span className="text-xl ml-1 text-black dark:text-white">{onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}</span>
          </div>

          <div className="text-center text-orange-500 dark:text-orange-400 font-semibold mb-6">
            {getDifference()} {onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'} from current weight
          </div>

          {isMobile ? (
            // Mobile: Custom Infinite Scroll Scale Picker
            <>
              <div className="relative mb-6">
                {/* Scroll container */}
                <div className="relative w-full max-w-full">
                  {/* Red triangle indicator - positioned absolutely to align with center */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-6 z-10 pointer-events-none">
                    <div
                      className="w-0 h-0 border-l-[10px] border-r-[10px] border-t-[20px] border-l-transparent border-r-transparent border-t-red-500 dark:border-t-red-400"
                    ></div>
                  </div>

                  {/* Center alignment line - for perfect tick alignment */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-0.5 h-full bg-red-500 dark:bg-red-400 opacity-30 z-5 pointer-events-none"></div>

                  <div
                    ref={scaleRef}
                    className="overflow-x-scroll overflow-y-hidden whitespace-nowrap relative border-t border-b border-gray-300 dark:border-gray-600 scrollbar-hide"
                    style={{
                      height: '120px',
                      WebkitOverflowScrolling: 'touch',
                      scrollBehavior: 'smooth'
                    }}
                    onScroll={handleScaleScroll}
                    onClick={handleScaleClick}
                  >
                    {/* Left padding to center the first tick */}
                    <div
                      className="inline-block flex-shrink-0"
                      style={{ width: 'calc(50vw - 6px)' }}
                    ></div>

                    {/* Ruler with ticks */}
                    <div
                      ref={rulerRef}
                      className="inline-flex relative"
                    >
                      {allTicks.map((tick, index) => (
                        <div
                          key={tick.key}
                          className={`w-0.5 bg-black dark:bg-white transition-all duration-75 ${
                            tick.isLong
                              ? 'h-[120px]'        // Tallest for multiples of 10
                              : tick.isMedium
                                ? 'h-[90px]'       // Medium for multiples of 5
                                : tick.isShort
                                  ? 'h-[60px]'     // Short for whole numbers
                                  : 'h-[30px]'     // Tiny for 0.1 increments
                          }`}
                          style={{
                            position: 'relative',
                            marginLeft: index === 0 ? '0' : '5px',
                            marginRight: '5px'
                          }}
                        >
                          {/* Labels for ticks that should show numbers */}
                          {tick.showLabel && (
                            <div
                              className={`absolute text-center w-full left-1/2 transform -translate-x-1/2 font-normal ${
                                tick.isLong
                                  ? 'text-xs text-gray-700 dark:text-gray-200 opacity-90 font-medium' // More prominent for multiples of 10
                                  : 'text-xs text-gray-500 dark:text-gray-400 opacity-70' // More subtle for multiples of 5
                              }`}
                              style={{ top: '-22px' }}
                            >
                              {tick.value.toFixed(0)}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Right padding to center the last tick */}
                    <div
                      className="inline-block flex-shrink-0"
                      style={{ width: 'calc(50vw - 6px)' }}
                    ></div>
                  </div>
                </div>

                {/* Scale instructions */}
                <div className="text-center mt-4">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Scroll or tap to select your target weight
                  </p>
                </div>
              </div>

              <div
                className="flex justify-between px-4 text-sm text-gray-500"
                style={{ color: document.documentElement.classList.contains('dark') ? '#8B8B8D' : undefined }}
              >
                <span>{minWeight} {onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}</span>
                <span>{maxWeight} {onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}</span>
              </div>
            </>
          ) : (
            // Desktop: Number Input
            <div className="flex justify-center mb-4">
              <div className="flex items-center">
                <input
                  type="number"
                  min={minWeight}
                  max={maxWeight}
                  step={onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 0.1 : 0.5}
                  value={weightValue}
                  onChange={handleWeightChange}
                  className="w-32 px-4 py-3 text-center text-lg font-semibold border border-gray-300 rounded-2xl bg-white text-black placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:focus:ring-white dark:focus:border-white"
                  placeholder={onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? "70" : "154"}
                />
                <span className="ml-3 text-lg font-medium text-black dark:text-white">
                  {onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs'}
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default TargetWeight;
