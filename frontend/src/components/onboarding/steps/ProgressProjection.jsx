import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import { useTheme } from '../../../context/ThemeContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';

const ProgressProjection = () => {
  const { onboardingData, goToNextStep, UNIT_SYSTEM } = useOnboarding();
  const { isDarkMode } = useTheme();
  const [buttonEnabled, setButtonEnabled] = useState(false);
  const [showFirstText, setShowFirstText] = useState(false);
  const [showSecondText, setShowSecondText] = useState(false);

  // Skip this step if goal is "maintain"
  if (onboardingData.goal === 'maintain') {
    setTimeout(goToNextStep, 0);
    return null;
  }

  // Show first text simultaneously with line animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowFirstText(true);
    }, 500); // Start with line animation
    return () => clearTimeout(timer);
  }, []);

  // Show second text shortly after first text
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSecondText(true);
    }, 2000); // 1.5 seconds after first text
    return () => clearTimeout(timer);
  }, []);

  // Enable button before animations complete
  useEffect(() => {
    const timer = setTimeout(() => {
      setButtonEnabled(true);
    }, 5000); // 5 seconds - 1 second before line animation completes
    return () => clearTimeout(timer);
  }, []);

  const weightUnit = onboardingData.unitSystem === UNIT_SYSTEM.METRIC ? 'kg' : 'lbs';

  const getCurrentWeight = () => {
    return onboardingData.unitSystem === UNIT_SYSTEM.METRIC
      ? onboardingData.weight.kg
      : onboardingData.weight.lbs;
  };

  const getTargetWeight = () => {
    return onboardingData.unitSystem === UNIT_SYSTEM.METRIC
      ? onboardingData.targetWeight.kg
      : onboardingData.targetWeight.lbs;
  };

  // Calculate the projected timeframe
  const calculateTimeframe = () => {
    const currentWeight = getCurrentWeight();
    const target = getTargetWeight();

    if (!currentWeight || !target || !onboardingData.goalRate) return null;

    const diff = Math.abs(target - currentWeight);
    const weeks = Math.ceil(diff / onboardingData.goalRate);

    // Calculate future date
    const today = new Date();
    const futureDate = new Date(today);
    futureDate.setDate(today.getDate() + (weeks * 7));

    return {
      weeks,
      date: futureDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })
    };
  };

  const timeframe = calculateTimeframe();

  return (
    <OnboardingLayout
      title="You have great potential to crush your goal"
      subtitle="Based on your plan, here's what to expect"
      actionButton={
        <div className={`transition-all duration-500 ease-out ${
          buttonEnabled ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <ActionButton onClick={goToNextStep} disabled={!buttonEnabled}>
            Continue
          </ActionButton>
        </div>
      }
    >
      <div className="py-6">
        <div className="text-center mb-8">
          <h2 className="text-xl font-semibold mb-2 text-black dark:text-white">Your weight transition</h2>
        </div>

        {/* Weight progress visualization */}
        <div
          className="bg-white rounded-xl border border-gray-200 p-6 mb-8"
          style={{
            backgroundColor: isDarkMode ? '#242426' : undefined,
            borderColor: isDarkMode ? '#8B8B8D' : undefined
          }}
        >

          {/* Animated Chart Container */}
          <div
            className="relative w-full h-40 mb-5 bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 rounded-xl overflow-hidden"
            style={{
              background: isDarkMode
                ? 'linear-gradient(to right, #1a1a1a, #2a2a2a, #1a1a1a)'
                : undefined
            }}
          >
            <svg
              viewBox="-10 0 320 150"
              preserveAspectRatio="none"
              className="w-full h-full block"
            >
              <defs>
                <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#f4e3d2" stopOpacity="0.7" />
                  <stop offset="100%" stopColor="#ffffff" stopOpacity="0" />
                </linearGradient>
                <linearGradient id="areaGradientDark" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="0%" stopColor="#d97706" stopOpacity="0.4" />
                  <stop offset="100%" stopColor="#1f2937" stopOpacity="0" />
                </linearGradient>
                <style>
                  {`
                    .animated-line {
                      stroke-dasharray: 600;
                      stroke-dashoffset: 600;
                      animation: drawLine 6s ease forwards;
                    }
                    @keyframes drawLine {
                      0%   { stroke-dashoffset: 600; }
                      25%  { stroke-dashoffset: 450; }
                      30%  { stroke-dashoffset: 450; }
                      60%  { stroke-dashoffset: 130; }
                      65%  { stroke-dashoffset: 130; }
                      100% { stroke-dashoffset: 0; }
                    }
                  `}
                </style>
              </defs>

              {/* Area fill */}
              <path
                d="M10,125 L90,127 C130,127 170,100 210,80 C240,70 270,60 290,60 L290,150 L10,150 Z"
                fill={isDarkMode ? "url(#areaGradientDark)" : "url(#areaGradient)"}
              />

              {/* Grid Lines */}
              <line x1="90" y1="0" x2="90" y2="150" stroke={isDarkMode ? "#4b5563" : "#ddd"} strokeDasharray="2" />
              <line x1="210" y1="0" x2="210" y2="150" stroke={isDarkMode ? "#4b5563" : "#ddd"} strokeDasharray="2" />

              {/* Curve line with animation */}
              <path
                className="animated-line"
                d="M10,125 L90,127 C130,127 170,100 210,80 C240,70 270,60 290,60"
                stroke={isDarkMode ? "#d97706" : "#885e3b"}
                fill="none"
                strokeWidth="2"
              />

              {/* Data points */}
              <circle cx="10" cy="125" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#FFFFFF" : "black"} strokeWidth="2" />
              <circle cx="90" cy="127" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#FFFFFF" : "black"} strokeWidth="2" />
              <circle cx="210" cy="80" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#FFFFFF" : "black"} strokeWidth="2" />
              <circle cx="290" cy="60" r="10" fill={isDarkMode ? "#f59e0b" : "orange"} />

              {/* Trophy icon */}
              <g transform="translate(285,55) scale(0.5)">
                <path
                  fill={isDarkMode ? "#FFFFFF" : "white"}
                  d="M10 1C8 1 8 3 8 3V5H4V7H6C6 9 7 11 9 12C8 13 7 14 6 15V16H14V15C13 14 12 13 11 12C13 11 14 9 14 7H16V5H12V3C12 3 12 1 10 1Z"
                />
              </g>
            </svg>
          </div>

          {/* Timeline Labels */}
          <div
            className="flex justify-between mx-2 mb-5 text-xs"
            style={{ color: isDarkMode ? '#8B8B8D' : '#6b7280' }}
          >
            <span>3 Days</span>
            <span>7 Days</span>
            <span>30 Days</span>
          </div>

          <div
            className={`text-center text-sm transition-all duration-700 ease-out ${
              showFirstText ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
            }`}
            style={{ color: isDarkMode ? '#FFFFFF' : '#374151' }}
          >
            {onboardingData.goal === 'lose' &&
              "Based on Calcounta's historical data, weight loss is usually delayed at first, but after 7 days, you can burn fat like crazy!"
            }
            {onboardingData.goal === 'gain' &&
              "Based on Calcounta's historical data, weight gain progress is gradual at first, but after 7 days, you'll see consistent muscle and healthy weight increases!"
            }
            {onboardingData.goal === 'maintain' &&
              "Based on Calcounta's historical data, maintaining your weight becomes easier after establishing consistent habits in the first 7 days!"
            }
          </div>
        </div>

        {/* Motivational text */}
        <div
          className={`bg-orange-50 p-5 rounded-xl transition-all duration-700 ease-out ${
            showSecondText ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
          }`}
          style={{ backgroundColor: isDarkMode ? '#242426' : undefined }}
        >
          <p
            className="text-center"
            style={{ color: isDarkMode ? '#FFFFFF' : '#1f2937' }}
          >
            With just {timeframe?.weeks} weeks of consistent effort, you'll reach your goal of {getTargetWeight()} {weightUnit}.
          </p>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default ProgressProjection;
