import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import SelectionCard from '../SelectionCard';
import ActionButton from '../ActionButton';

const WorkoutFrequency = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, WORKOUT_FREQUENCY } = useOnboarding();

  const handleSelect = (frequency) => {
    updateOnboardingData('workoutFrequency', frequency);
  };

  return (
    <OnboardingLayout
      title="Your Activity Level"
      subtitle="How often do you exercise per week?"
      actionButton={
        <ActionButton
          onClick={goToNextStep}
          disabled={!onboardingData.workoutFrequency}
        >
          Continue
        </ActionButton>
      }
    >
      <div className="flex flex-col space-y-3">
        <SelectionCard
          selected={onboardingData.workoutFrequency === WORKOUT_FREQUENCY.NONE}
          onClick={() => handleSelect(WORKOUT_FREQUENCY.NONE)}
          title="Never"
          subtitle="Little to no physical activity"
        />

        <SelectionCard
          selected={onboardingData.workoutFrequency === WORKOUT_FREQUENCY.LOW}
          onClick={() => handleSelect(WORKOUT_FREQUENCY.LOW)}
          title="1-3 times/week"
          subtitle="Light exercise"
        />

        <SelectionCard
          selected={onboardingData.workoutFrequency === WORKOUT_FREQUENCY.MEDIUM}
          onClick={() => handleSelect(WORKOUT_FREQUENCY.MEDIUM)}
          title="4-6 times/week"
          subtitle="Moderate exercise"
        />

        <SelectionCard
          selected={onboardingData.workoutFrequency === WORKOUT_FREQUENCY.HIGH}
          onClick={() => handleSelect(WORKOUT_FREQUENCY.HIGH)}
          title="7+ times/week"
          subtitle="Intense exercise"
        />
      </div>
    </OnboardingLayout>
  );
};

export default WorkoutFrequency;
