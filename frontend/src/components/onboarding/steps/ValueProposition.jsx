import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import { useTheme } from '../../../context/ThemeContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';

const ValueProposition = () => {
  const { goToNextStep } = useOnboarding();
  const { isDarkMode } = useTheme();
  const [animationStarted, setAnimationStarted] = useState(false);
  const [buttonEnabled, setButtonEnabled] = useState(false);
  const [showStatText, setShowStatText] = useState(false);

  // Start animation when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationStarted(true);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  // Show statistics text simultaneously with line animations
  useEffect(() => {
    if (animationStarted) {
      // Show text immediately when line animations start
      setShowStatText(true);
    }
  }, [animationStarted]);

  // Enable button 1 second before animations complete (at 6 seconds instead of 7)
  useEffect(() => {
    if (animationStarted) {
      const timer = setTimeout(() => {
        setButtonEnabled(true);
      }, 6000); // 6 seconds - 1 second before animations complete
      return () => clearTimeout(timer);
    }
  }, [animationStarted]);

  // SVG paths based on your HTML design
  const calcountaPath = "M40,40 C100,20 200,80 270,130 C320,160 360,180 390,180";
  const traditionalPath = "M40,40 C100,60 140,140 200,150 C260,160 300,80 390,30";

  return (
    <OnboardingLayout
      title="Calcounta creates long-term results"
      subtitle="Our smart algorithm adapts to your progress"
      actionButton={
        <div className={`transition-all duration-500 ease-out ${
          buttonEnabled ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
        }`}>
          <ActionButton onClick={goToNextStep} disabled={!buttonEnabled}>
            Continue
          </ActionButton>
        </div>
      }
    >
      <div className="flex-1 flex flex-col items-center justify-center py-8">
        {/* Weight projection graph card */}
        <div
          className="w-full bg-white rounded-[20px] shadow-sm border border-gray-200 p-5 mb-6 text-center overflow-hidden"
          style={{
            backgroundColor: isDarkMode ? '#242426' : undefined,
            borderColor: isDarkMode ? '#8B8B8D' : undefined
          }}
        >
          <h2
            className="text-xl font-medium mb-2 text-black"
            style={{ color: isDarkMode ? '#FFFFFF' : undefined }}
          >Your weight</h2>

          {/* SVG Graph */}
          <svg
            viewBox="0 0 400 200"
            preserveAspectRatio="none"
            className="w-full h-[200px] overflow-visible"
          >
            {/* Axes */}
            <line x1="40" y1="180" x2="390" y2="180" stroke={isDarkMode ? "#6b7280" : "#888"} strokeWidth="1" />
            <line x1="40" y1="20" x2="40" y2="180" stroke={isDarkMode ? "#6b7280" : "#888"} strokeWidth="1" />

            {/* Grid lines */}
            <line x1="40" y1="40" x2="390" y2="40" stroke={isDarkMode ? "#4b5563" : "#ccc"} strokeDasharray="4" />
            <line x1="40" y1="100" x2="390" y2="100" stroke={isDarkMode ? "#4b5563" : "#ccc"} strokeDasharray="4" />
            <line x1="40" y1="180" x2="390" y2="180" stroke={isDarkMode ? "#4b5563" : "#ccc"} strokeDasharray="4" />

            {/* Calcounta Line (smooth S-like curve) */}
            <path
              d={calcountaPath}
              fill="none"
              stroke={isDarkMode ? "#ffffff" : "black"}
              strokeWidth="2"
              className={`${animationStarted ? 'animate-drawPath' : ''}`}
              style={{
                strokeDasharray: '1000',
                strokeDashoffset: animationStarted ? '0' : '1000',
                transition: 'stroke-dashoffset 3s ease-out'
              }}
            />

            {/* Traditional Line (smooth dip below Calcounta mid-way with very soft curve) */}
            <path
              d={traditionalPath}
              fill="none"
              stroke={isDarkMode ? "#ef4444" : "#d33"}
              strokeWidth="2"
              className={`${animationStarted ? 'animate-drawPath-delayed' : ''}`}
              style={{
                strokeDasharray: '1000',
                strokeDashoffset: animationStarted ? '0' : '1000',
                transition: 'stroke-dashoffset 3s ease-out 1s'
              }}
            />

            {/* Dots */}
            <circle cx="40" cy="40" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#FFFFFF" : "black"} strokeWidth="2" />
            <circle cx="40" cy="40" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#ef4444" : "#d33"} strokeWidth="2" />
            <circle cx="390" cy="180" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#FFFFFF" : "black"} strokeWidth="2" />
            <circle cx="390" cy="30" r="5" fill={isDarkMode ? "#242426" : "white"} stroke={isDarkMode ? "#ef4444" : "#d33"} strokeWidth="2" />

            {/* Labels */}
            <text x="40" y="195" fontSize="14" fill={isDarkMode ? "#8B8B8D" : "#555"} textAnchor="middle">Month 1</text>
            <text x="310" y="195" fontSize="14" fill={isDarkMode ? "#8B8B8D" : "#555"} textAnchor="middle">Month 6</text>
            <text x="240" y="50" fontSize="14" fill={isDarkMode ? "#8B8B8D" : "#555"}>Traditional diet</text>
            <text x="50" y="30" fontSize="14" fill={isDarkMode ? "#8B8B8D" : "#555"}>calcounta</text>
          </svg>

          {/* Caption with fade-up effect */}
          <div
            className={`text-sm mt-2 transition-all duration-700 ease-out ${
              showStatText ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-6'
            }`}
            style={{ color: isDarkMode ? '#8B8B8D' : '#6b7280' }}
          >
            80% of Calcounta users maintain their weight loss even 6 months later
          </div>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default ValueProposition;
