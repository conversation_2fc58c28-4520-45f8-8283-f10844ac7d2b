import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import SelectionButton from '../SelectionButton';
import ActionButton from '../ActionButton';
import { useOnboardingAnimations } from '../../../hooks/useOnboardingAnimations';
import TypewriterText from '../../ui/TypewriterText';

const GenderSelection = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, GENDER_OPTIONS } = useOnboarding();
  const { getStaggeredClasses } = useOnboardingAnimations();

  const handleGenderSelect = (gender) => {
    updateOnboardingData('gender', gender);
  };

  const genderOptions = [
    { value: GENDER_OPTIONS.MALE, label: 'Male' },
    { value: GENDER_OPTIONS.FEMALE, label: 'Female' },
    { value: GENDER_OPTIONS.OTHER, label: 'Other' }
  ];

  return (
    <OnboardingLayout
      title="Choose your Gender"
      subtitle="This helps us calculate your calorie and nutrient needs"
      actionButton={
        <ActionButton
          onClick={goToNextStep}
          disabled={!onboardingData.gender}
        >
          Continue
        </ActionButton>
      }
    >
      <div className="flex flex-col space-y-3">
        {genderOptions.map((option, index) => (
          <div
            key={option.value}
            className={getStaggeredClasses(index)}
          >
            <SelectionButton
              label={option.label}
              selected={onboardingData.gender === option.value}
              onClick={() => handleGenderSelect(option.value)}
              enableTypewriter={false}
            />
          </div>
        ))}
      </div>
    </OnboardingLayout>
  );
};

export default GenderSelection;
