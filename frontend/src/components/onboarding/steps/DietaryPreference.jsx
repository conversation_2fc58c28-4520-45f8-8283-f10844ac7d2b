import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import SelectionCard from '../SelectionCard';
import {
  OmnivoreIcon,
  PescatarianIcon,
  VegetarianIcon,
  VeganIcon,
  KetoIcon
} from '../../../assets/icons/OnboardingIcons';

const DietaryPreference = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, DIET } = useOnboarding();

  const handleDietSelect = (diet) => {
    updateOnboardingData('diet', diet);
  };

  const diets = [
    {
      id: DIET.OMNIVORE,
      label: 'Omnivore',
      icon: <OmnivoreIcon className="w-6 h-6" />,
      description: 'I eat everything',
    },
    {
      id: DIET.PESCATARIAN,
      label: 'Pescatarian',
      icon: <PescatarianIcon className="w-6 h-6" />,
      description: 'I eat fish but not meat',
    },
    {
      id: DIET.VEGETARIAN,
      label: 'Vegetarian',
      icon: <VegetarianIcon className="w-6 h-6" />,
      description: 'No meat or fish',
    },
    {
      id: DIET.VEGAN,
      label: 'Vegan',
      icon: <VeganIcon className="w-6 h-6" />,
      description: 'No animal products',
    },
    {
      id: DIET.KETO,
      label: 'Keto',
      icon: <KetoIcon className="w-6 h-6" />,
      description: 'Low-carb, high-fat',
    },
  ];

  return (
    <OnboardingLayout
      title="Do you follow a specific diet?"
      subtitle="We'll customize your meal recommendations"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!onboardingData.diet}>
          Continue
        </ActionButton>
      }
    >
      <div className="space-y-3 py-2">
        {diets.map((item) => (
          <SelectionCard
            key={item.id}
            title={item.label}
            subtitle={item.description}
            icon={item.icon}
            selected={onboardingData.diet === item.id}
            onClick={() => handleDietSelect(item.id)}
          />
        ))}
      </div>
    </OnboardingLayout>
  );
};

export default DietaryPreference;
