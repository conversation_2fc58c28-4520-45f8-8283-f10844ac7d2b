import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import SelectionCard from '../SelectionCard';

const GoalSelection = () => {
  const { onboardingData, updateOnboardingData, goToNextStep, GOAL } = useOnboarding();

  const handleGoalSelect = (goal) => {
    updateOnboardingData('goal', goal);
  };

  return (
    <OnboardingLayout
      title="What is your goal?"
      subtitle="This will help us create your personalized plan"
      actionButton={
        <ActionButton onClick={goToNextStep} disabled={!onboardingData.goal}>
          Continue
        </ActionButton>
      }
    >
      <div className="flex flex-col space-y-4">
        <SelectionCard
          selected={onboardingData.goal === GOAL.LOSE}
          onClick={() => handleGoalSelect(GOAL.LOSE)}
          title="Lose weight"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          }
        />

        <SelectionCard
          selected={onboardingData.goal === GOAL.MAINTAIN}
          onClick={() => handleGoalSelect(GOAL.MAINTAIN)}
          title="Maintain weight"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
            </svg>
          }
        />

        <SelectionCard
          selected={onboardingData.goal === GOAL.GAIN}
          onClick={() => handleGoalSelect(GOAL.GAIN)}
          title="Gain weight"
          icon={
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
          }
        />
      </div>
    </OnboardingLayout>
  );
};

export default GoalSelection;
