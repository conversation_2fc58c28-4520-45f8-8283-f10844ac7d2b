import { useState, useEffect } from 'react';
import { useOnboarding } from '../../../context/OnboardingContext';
import OnboardingLayout from '../OnboardingLayout';
import ActionButton from '../ActionButton';
import { useOnboardingAnimations } from '../../../hooks/useOnboardingAnimations';

const WelcomeScreen = () => {
  const { goToNextStep } = useOnboarding();
  const { getStaggeredClasses, isVisible } = useOnboardingAnimations();

  return (
    <OnboardingLayout
      title="Welcome to Calcounta"
      subtitle="Let's get started on your health journey"
      showBackButton={false}
      actionButton={
        <ActionButton onClick={goToNextStep}>
          Get Started
        </ActionButton>
      }
    >
      <div className="flex flex-col items-center">
        {/* App logo/icon */}
        <div className={`w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6 ${
          isVisible ? 'animate-bounceIn' : 'opacity-0 scale-50'
        }`}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 text-black transition-transform duration-300 hover:scale-110"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
        </div>

        {/* Welcome text */}
        <h3 className={`text-xl font-semibold text-center mb-4 ${getStaggeredClasses(0)}`}>
          Your AI-Powered Nutrition Assistant
        </h3>

        <p className={`text-gray-600 text-center mb-8 ${getStaggeredClasses(1)}`}>
          Track your calories, analyze food with AI, and reach your health goals with personalized insights.
        </p>

        {/* Features list */}
        <div className="w-full space-y-4 mb-8">
          <div className={`flex items-start ${getStaggeredClasses(2)}`}>
            <div className="flex-shrink-0 h-6 w-6 text-black transition-transform duration-200 hover:scale-110">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="ml-3 text-gray-700">Snap photos of your food for instant calorie counts</p>
          </div>

          <div className={`flex items-start ${getStaggeredClasses(3)}`}>
            <div className="flex-shrink-0 h-6 w-6 text-black transition-transform duration-200 hover:scale-110">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="ml-3 text-gray-700">Track your progress with intuitive charts and insights</p>
          </div>

          <div className={`flex items-start ${getStaggeredClasses(4)}`}>
            <div className="flex-shrink-0 h-6 w-6 text-black transition-transform duration-200 hover:scale-110">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <p className="ml-3 text-gray-700">Get personalized recommendations based on your goals</p>
          </div>
        </div>
      </div>
    </OnboardingLayout>
  );
};

export default WelcomeScreen;
