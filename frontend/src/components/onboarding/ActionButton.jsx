import { useState } from 'react';
import { classNames } from '../../utils/classNames';
import { useOnboardingAnimations } from '../../hooks/useOnboardingAnimations';

const ActionButton = ({
  children,
  className,
  variant = "primary",
  disabled,
  onClick,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const { getButtonClasses } = useOnboardingAnimations();

  const handleMouseDown = () => {
    if (!disabled) {
      setIsPressed(true);
    }
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleMouseEnter = () => {
    if (!disabled) {
      setIsHovered(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsPressed(false);
  };

  const handleClick = (e) => {
    if (!disabled && onClick) {
      onClick(e);
    }
  };

  return (
    <button
      className={classNames(
        "w-full py-4 px-6 font-bold rounded-2xl transition-all duration-200 ease-out",
        "transform-gpu", // Enable hardware acceleration
        variant === "primary" && "bg-black dark:bg-custom-button dark:text-white dark:hover:bg-custom-button-hover text-white border-2 border-transparent",
        variant === "secondary" && "bg-orange-500 text-white",
        variant === "outline" && "bg-transparent border border-gray-300 dark:bg-custom-button dark:text-white dark:hover:bg-custom-button-hover text-black",
        !disabled && "hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 active:shadow-md",
        disabled && "opacity-50 cursor-not-allowed",
        getButtonClasses(isPressed, isHovered),
        className
      )}
      disabled={disabled}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      <span className={classNames(
        "transition-all duration-150",
        isPressed ? "scale-95" : "scale-100"
      )}>
        {children}
      </span>
    </button>
  );
};

export default ActionButton;
