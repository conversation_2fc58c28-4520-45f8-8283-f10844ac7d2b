import { classNames } from '../../utils/classNames';
import { useTheme } from '../../context/ThemeContext';
import TypewriterText from '../ui/TypewriterText';

const SelectionButton = ({
  label,
  selected = false,
  onClick,
  icon,
  className,
  enableTypewriter = false, // Disabled by default for minimalistic approach
  typewriterDelay = 0,
  typewriterSpeed = 30,
}) => {
  const { isDarkMode } = useTheme();

  return (
    <button
      type="button"
      className={classNames(
        'w-full py-4 px-6 rounded-2xl border text-left transition-all duration-300 mb-4 flex items-center',
        className
      )}
      style={{
        backgroundColor: selected
          ? (isDarkMode ? '#FFFFFF' : '#000000')
          : (isDarkMode ? '#242426' : '#ffffff'),
        color: selected
          ? (isDarkMode ? '#000000' : '#ffffff')
          : (isDarkMode ? '#FFFFFF' : '#000000'),
        borderColor: selected
          ? (isDarkMode ? '#FFFFFF' : '#000000')
          : (isDarkMode ? '#8B8B8D' : '#e5e7eb')
      }}
      onMouseEnter={(e) => {
        if (!selected) {
          e.target.style.backgroundColor = isDarkMode ? '#363638' : '#f9fafb';
          e.target.style.borderColor = isDarkMode ? '#FFFFFF' : '#d1d5db';
        }
      }}
      onMouseLeave={(e) => {
        if (!selected) {
          e.target.style.backgroundColor = isDarkMode ? '#242426' : '#ffffff';
          e.target.style.borderColor = isDarkMode ? '#8B8B8D' : '#e5e7eb';
        }
      }}
      onClick={onClick}
    >
      {icon && <div className="mr-3">{icon}</div>}
      <span className="font-semibold">
        {enableTypewriter ? (
          <TypewriterText
            text={label}
            speed={typewriterSpeed}
            delay={typewriterDelay}
            showCursor={false}
          />
        ) : (
          label
        )}
      </span>
    </button>
  );
};

export default SelectionButton;
