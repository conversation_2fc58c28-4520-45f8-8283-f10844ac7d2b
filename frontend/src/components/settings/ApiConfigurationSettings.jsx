import { useState } from 'react';
import { useApiConfig } from '../../context/ApiConfigContext';
import ApiSetupModal from '../modals/ApiSetupModal';

const ApiConfigurationSettings = () => {
  const { config, clearConfig, getProviders, getModelsForProvider, updateConfig, getAuthType } = useApiConfig();
  const [showSetupModal, setShowSetupModal] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  const handleClearConfig = () => {
    const success = clearConfig();
    if (success) {
      setShowClearConfirm(false);
    }
  };

  const handleProviderChange = async (e) => {
    const newProvider = e.target.value;
    if (config.isConfigured && newProvider) {
      // When changing provider, reset model to first available model for new provider
      const availableModels = getModelsForProvider(newProvider);
      const firstModel = availableModels.length > 0 ? availableModels[0].id : '';

      // Get current credentials based on auth type
      const authType = getAuthType(config.provider);
      let credentials;
      if (authType === 'apiKey' && config.apiKey) {
        credentials = { apiKey: config.apiKey };
      } else if (authType === 'clientCredentials' && config.clientId && config.clientSecret) {
        credentials = { clientId: config.clientId, clientSecret: config.clientSecret };
      }

      if (credentials) {
        await updateConfig(newProvider, firstModel, credentials);
      }
    }
  };

  const handleModelChange = async (e) => {
    const newModel = e.target.value;
    if (config.isConfigured && config.provider && newModel) {
      // Get current credentials based on auth type
      const authType = getAuthType(config.provider);
      let credentials;
      if (authType === 'apiKey' && config.apiKey) {
        credentials = { apiKey: config.apiKey };
      } else if (authType === 'clientCredentials' && config.clientId && config.clientSecret) {
        credentials = { clientId: config.clientId, clientSecret: config.clientSecret };
      }

      if (credentials) {
        await updateConfig(config.provider, newModel, credentials);
      }
    }
  };

  const getProviderName = (providerId) => {
    const providers = getProviders();
    const provider = providers.find(p => p.id === providerId);
    return provider ? provider.name : providerId;
  };

  const getModelName = (providerId, modelId) => {
    const models = getModelsForProvider(providerId);
    const model = models.find(m => m.id === modelId);
    return model ? model.name : modelId;
  };

  return (
    <>
      <div className="space-y-3">
        <h2 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide px-4">
          AI Configuration
        </h2>
        <div className="bg-white dark:bg-[#242426] rounded-xl overflow-hidden">
          {/* AI Provider */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <span className="text-gray-900 dark:text-white font-medium">AI Provider</span>
            </div>
            <div className="flex items-center space-x-2 min-w-0">
              {config.isConfigured ? (
                <select
                  value={config.provider || ''}
                  onChange={handleProviderChange}
                  className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[80px]"
                >
                  {getProviders().map(provider => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name}
                    </option>
                  ))}
                </select>
              ) : (
                <span className="text-gray-500 dark:text-gray-400 text-sm">Not configured</span>
              )}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>

          {/* Model */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              <span className="text-gray-900 dark:text-white font-medium">Model</span>
            </div>
            <div className="flex items-center space-x-2 min-w-0">
              {config.isConfigured && config.provider ? (
                <select
                  value={config.model || ''}
                  onChange={handleModelChange}
                  className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[120px]"
                >
                  {getModelsForProvider(config.provider).map(model => (
                    <option key={model.id} value={model.id}>
                      {model.name}
                    </option>
                  ))}
                </select>
              ) : (
                <span className="text-gray-500 dark:text-gray-400 text-sm">Not configured</span>
              )}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </div>

          {/* Authentication Status */}
          <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z" />
                </svg>
              </div>
              <span className="text-gray-900 dark:text-white font-medium">
                {config.isConfigured && config.provider && getAuthType(config.provider) === 'clientCredentials'
                  ? 'Client Credentials'
                  : 'API Key'
                }
              </span>
            </div>
            <div className="flex items-center space-x-2 min-w-0">
              <span className="text-gray-900 dark:text-white text-sm">
                {config.isConfigured ? 'Configured' : 'Not configured'}
              </span>
              <div className={`w-2 h-2 rounded-full flex-shrink-0 ${config.isConfigured ? 'bg-green-500' : 'bg-red-500'}`}></div>
            </div>
          </div>

          {/* Configure Button */}
          <button
            onClick={() => setShowSetupModal(true)}
            className="w-full flex items-center justify-between px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <span className="text-gray-900 dark:text-white font-medium">
                {config.isConfigured ? 'Update Configuration' : 'Configure API'}
              </span>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          {/* Clear Configuration Button */}
          {config.isConfigured && (
            <button
              onClick={() => setShowClearConfirm(true)}
              className="w-full flex items-center justify-between px-6 py-4 hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors text-red-600 dark:text-red-400"
            >
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <span className="font-medium">Clear Configuration</span>
              </div>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Setup Modal */}
      <ApiSetupModal
        isOpen={showSetupModal}
        onClose={() => setShowSetupModal(false)}
        isRequired={false}
      />

      {/* Clear Confirmation Modal */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl max-w-sm w-full">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Clear API Configuration?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                This will remove your saved API configuration. You'll need to set it up again to use food analysis features.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleClearConfig}
                  className="flex-1 py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-colors"
                >
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ApiConfigurationSettings;
