const Footer = () => {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-white py-6 border-t">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div className="mb-6 md:mb-0 text-center md:text-left">
            <div className="flex flex-col md:flex-row md:items-center">
              <span className="text-xl font-bold text-black mb-1 md:mb-0 md:mr-2">Calcounta</span>
              <span className="text-sm text-gray-500">Your AI-Powered Nutrition Assistant</span>
            </div>
            <p className="text-sm text-gray-500 mt-2">
              &copy; {currentYear} Calcounta. All rights reserved.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row sm:space-x-8 w-full md:w-auto">
            <div className="mb-6 sm:mb-0 text-center sm:text-left">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-sm text-gray-500 hover:text-black">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-sm text-gray-500 hover:text-black">
                    Nutrition Guide
                  </a>
                </li>
              </ul>
            </div>
            <div className="text-center sm:text-left">
              <h3 className="text-sm font-semibold text-gray-900 mb-2">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-sm text-gray-500 hover:text-black">
                    Privacy Policy
                  </a>
                </li>
                <li>
                  <a href="#" className="text-sm text-gray-500 hover:text-black">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
