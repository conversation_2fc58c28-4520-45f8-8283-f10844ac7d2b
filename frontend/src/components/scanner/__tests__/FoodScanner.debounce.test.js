/**
 * Test file for food detection debouncing functionality
 * This test verifies that the debouncing mechanism works correctly
 */

import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import FoodScanner from '../FoodScanner';
import { ApiConfigProvider } from '../../../context/ApiConfigContext';
import { GlobalLoadingProvider } from '../../../hooks/useGlobalLoading';

// Mock the food detector service
jest.mock('../../../services/FoodDetector', () => ({
  loadModel: jest.fn().mockResolvedValue(),
  isReady: jest.fn().mockReturnValue(true),
  classifyFromVideo: jest.fn(),
  dispose: jest.fn(),
}));

// Mock the media devices
Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn(), enabled: true, readyState: 'live', kind: 'video' }],
    }),
    enumerateDevices: jest.fn().mockResolvedValue([
      { kind: 'videoinput', deviceId: 'camera1', label: 'Camera 1' }
    ]),
  },
});

// Mock window.URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mock-url');
global.URL.revokeObjectURL = jest.fn();

const TestWrapper = ({ children }) => (
  <BrowserRouter>
    <ApiConfigProvider>
      <GlobalLoadingProvider>
        {children}
      </GlobalLoadingProvider>
    </ApiConfigProvider>
  </BrowserRouter>
);

describe('FoodScanner Debouncing', () => {
  let mockClassifyFromVideo;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Reset the food detection unavailable flag
    delete window.foodDetectionUnavailable;
    
    mockClassifyFromVideo = require('../../../services/FoodDetector').default.classifyFromVideo;
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  test('capture button is immediately enabled when food is detected', async () => {
    // Mock food detection returning positive result
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: true,
      topPrediction: { className: 'pizza', probability: 0.9 }
    });

    render(
      <TestWrapper>
        <FoodScanner isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    // Wait for component to initialize and start detection
    await waitFor(() => {
      expect(mockClassifyFromVideo).toHaveBeenCalled();
    });

    // Simulate food detection
    await act(async () => {
      // Trigger the detection interval
      jest.advanceTimersByTime(300);
    });

    // Check that capture button is enabled
    const captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();
  });

  test('capture button stays enabled for 3 seconds after food detection stops', async () => {
    // Start with food detected
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: true,
      topPrediction: { className: 'pizza', probability: 0.9 }
    });

    render(
      <TestWrapper>
        <FoodScanner isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    // Wait for initial detection
    await waitFor(() => {
      expect(mockClassifyFromVideo).toHaveBeenCalled();
    });

    // Simulate food detection
    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    // Verify button is enabled
    let captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();

    // Now simulate food no longer detected
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: false,
      topPrediction: null
    });

    // Advance time by 1 second - button should still be enabled
    await act(async () => {
      jest.advanceTimersByTime(1000);
    });

    captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();

    // Advance time by another 2 seconds - button should still be enabled
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();

    // Advance time by 1 more second (total 4 seconds) - now button should be disabled
    await act(async () => {
      jest.advanceTimersByTime(1000);
    });

    captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).toBeDisabled();
  });

  test('timer resets when food is detected again during debounce period', async () => {
    // Start with food detected
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: true,
      topPrediction: { className: 'pizza', probability: 0.9 }
    });

    render(
      <TestWrapper>
        <FoodScanner isOpen={true} onClose={jest.fn()} />
      </TestWrapper>
    );

    // Wait for initial detection
    await waitFor(() => {
      expect(mockClassifyFromVideo).toHaveBeenCalled();
    });

    // Simulate initial food detection
    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    // Simulate food no longer detected
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: false,
      topPrediction: null
    });

    // Advance time by 2 seconds
    await act(async () => {
      jest.advanceTimersByTime(2000);
    });

    // Button should still be enabled
    let captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();

    // Now food is detected again - this should reset the timer
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: true,
      topPrediction: { className: 'apple', probability: 0.8 }
    });

    await act(async () => {
      jest.advanceTimersByTime(300);
    });

    // Simulate food no longer detected again
    mockClassifyFromVideo.mockResolvedValue({
      isFoodDetected: false,
      topPrediction: null
    });

    // Advance time by 2.5 seconds - button should still be enabled because timer was reset
    await act(async () => {
      jest.advanceTimersByTime(2500);
    });

    captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).not.toBeDisabled();

    // Advance time by another 1 second (total 3.5 seconds from last detection) - now should be disabled
    await act(async () => {
      jest.advanceTimersByTime(1000);
    });

    captureButton = screen.getByLabelText('Capture image');
    expect(captureButton).toBeDisabled();
  });
});
