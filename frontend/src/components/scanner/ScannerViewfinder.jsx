import React from 'react';

/**
 * Get viewfinder configuration based on mode
 * This is exported so it can be used by other components
 *
 * @param {string} mode - The active scanning mode ('scan', 'barcode', 'label', 'nutrition')
 * @returns {Object} - Configuration object with width, height, aspectRatio, etc.
 */
export const getViewfinderConfig = (mode) => {
  switch (mode) {
    case 'barcode':
      return {
        width: '80%',
        height: '25%',
        aspectRatio: 'auto', // Override the aspect-square class
        borderRadius: '16px',
        shape: 'rectangle',
        cornerSize: '24px' // Size of corner elements (1.5x the border radius)
      };
    case 'label':
      return {
        width: '85%',
        height: '40%',
        aspectRatio: 'auto',
        borderRadius: '16px',
        shape: 'rectangle',
        cornerSize: '24px'
      };
    case 'nutrition':
      return {
        width: '80%',
        height: '45%',
        aspectRatio: 'auto',
        borderRadius: '16px',
        shape: 'rectangle',
        cornerSize: '24px'
      };
    case 'scan':
    default:
      return {
        width: '70%',
        height: 'auto', // Will be calculated as square based on width
        aspectRatio: '1/1', // Square
        borderRadius: '16px',
        shape: 'square',
        cornerSize: '24px',
        maintainSquare: true // Flag to indicate this should be calculated as square
      };
  }
};

/**
 * ScannerViewfinder component for the food scanner
 * Creates a viewfinder overlay for the camera with a semi-transparent black overlay
 * outside the focus window, matching the UI in the reference image
 *
 * @param {string} mode - The active scanning mode ('scan', 'barcode', 'label', 'nutrition')
 * @param {boolean} isFoodDetected - Whether food is detected in the frame
 * @param {boolean} foodDetectionActive - Whether food detection is active
 */
const ScannerViewfinder = ({
  mode = 'scan',
  isFoodDetected = false,
  foodDetectionActive = false
}) => {
  const config = getViewfinderConfig(mode);

  return (
    <div className="absolute inset-0 pointer-events-none scanner-viewfinder-overlay" style={{
      top: 'calc(-1 * env(safe-area-inset-top, 0))',
      height: 'calc(100% + env(safe-area-inset-top, 0))',
      zIndex: 10, // Ensure the viewfinder is above other elements but below controls
      width: '100%',
      position: 'absolute',
      left: 0,
      right: 0
    }}>
      {/* Semi-transparent black overlay covering the entire screen */}
      <div className="absolute inset-0 bg-black bg-opacity-25" style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0
      }}>
        {/* This creates the transparent "window" in the overlay */}
        <div
          className="absolute left-1/2 transform -translate-x-1/2 overflow-hidden"
          style={{
            width: config.width,
            height: config.height,
            aspectRatio: config.aspectRatio,
            borderRadius: config.borderRadius,
            top: '25%',
            // Create a "hole" in the overlay using box-shadow with a large spread
            // Always maintain the same overlay opacity regardless of food detection
            boxShadow: "0 0 0 9999px rgba(0, 0, 0, 0.25)",
            // Add a subtle border to ensure the viewfinder outline is visible
            border: '1px solid rgba(255, 255, 255, 0.3)'
          }}
        >
          {/* Top left corner */}
          <div
            className="absolute top-0 left-0 border-t-2 border-l-2 border-white"
            style={{
              width: config.cornerSize,
              height: config.cornerSize,
              borderTopLeftRadius: config.borderRadius,
              // Ensure perfect alignment with the viewfinder
              transform: 'translate(0, 0)'
            }}
          ></div>

          {/* Top right corner */}
          <div
            className="absolute top-0 right-0 border-t-2 border-r-2 border-white"
            style={{
              width: config.cornerSize,
              height: config.cornerSize,
              borderTopRightRadius: config.borderRadius,
              // Ensure perfect alignment with the viewfinder
              transform: 'translate(0, 0)'
            }}
          ></div>

          {/* Bottom left corner */}
          <div
            className="absolute bottom-0 left-0 border-b-2 border-l-2 border-white"
            style={{
              width: config.cornerSize,
              height: config.cornerSize,
              borderBottomLeftRadius: config.borderRadius,
              // Ensure perfect alignment with the viewfinder
              transform: 'translate(0, 0)'
            }}
          ></div>

          {/* Bottom right corner */}
          <div
            className="absolute bottom-0 right-0 border-b-2 border-r-2 border-white"
            style={{
              width: config.cornerSize,
              height: config.cornerSize,
              borderBottomRightRadius: config.borderRadius,
              // Ensure perfect alignment with the viewfinder
              transform: 'translate(0, 0)'
            }}
          ></div>

          {/* Barcode scanning line animation */}
          {mode === 'barcode' && (
            <div
              className="absolute left-0 right-0 h-0.5 bg-red-500"
              style={{
                top: '50%',
                animation: 'scanLine 2s infinite ease-in-out',
                boxShadow: '0 0 5px rgba(255, 0, 0, 0.7)'
              }}
            ></div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScannerViewfinder;
