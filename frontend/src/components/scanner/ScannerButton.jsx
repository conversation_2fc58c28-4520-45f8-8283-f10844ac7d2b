import React from 'react';
import { cn } from '../../lib/utils';

/**
 * ScannerButton component for the food scanner
 *
 * @param {string} icon - Icon type ('scan', 'barcode', 'label', 'nutrition')
 * @param {string} label - Button label
 * @param {boolean} active - Whether the button is active
 * @param {function} onClick - Function to call when the button is clicked
 */
const ScannerButton = ({ icon, label, active, onClick }) => {
  const renderIcon = () => {
    switch (icon) {
      case 'scan':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mx-auto ${active ? 'text-black' : 'text-white'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <rect x="4" y="4" width="16" height="16" rx="2" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" fill="none" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 12C7 9.23858 9.23858 7 12 7C14.7614 7 17 9.23858 17 12C17 14.7614 14.7614 17 12 17C9.23858 17 7 14.7614 7 12Z" />
          </svg>
        );
      case 'barcode':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mx-auto ${active ? 'text-black' : 'text-white'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <rect x="4" y="4" width="16" height="16" rx="2" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" fill="none" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 7v10M10 7v10M13 7v10M16 7v10" />
          </svg>
        );
      case 'label':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mx-auto ${active ? 'text-black' : 'text-white'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M7 7h.01M7 4h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a3 3 0 014-3z" />
          </svg>
        );
      case 'nutrition':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 mx-auto ${active ? 'text-black' : 'text-white'}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <rect x="5" y="4" width="14" height="16" rx="1" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" stroke="currentColor" fill="none" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 8h8M8 12h8M8 16h4" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <button
      onClick={onClick}
      className={cn(
        "flex flex-col items-center justify-center rounded-2xl p-2 mx-1.5 w-[80px] h-[90px] transition-all",
        active
          ? "bg-white text-black"
          : "bg-black bg-opacity-40 text-white border border-white border-opacity-30"
      )}
    >
      <div className="flex flex-col items-center justify-center h-full">
        {renderIcon()}
       <span className={`text-[10px] text-center font-medium ${active ? 'text-black' : 'text-white'}`}>{label}</span>
      </div>
    </button>
  );
};

export default ScannerButton;
