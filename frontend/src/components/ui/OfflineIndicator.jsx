import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

/**
 * OfflineIndicator component
 * Displays a notification when the user is offline with helpful information
 */
const OfflineIndicator = () => {
  const [isOffline, setIsOffline] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [dismissedUntil, setDismissedUntil] = useState(null);

  useEffect(() => {
    // Check initial online status
    setIsOffline(!navigator.onLine);

    // Check if notification was previously dismissed
    const storedDismissedUntil = localStorage.getItem('offlineNotificationDismissedUntil');
    if (storedDismissedUntil) {
      const dismissedTime = parseInt(storedDismissedUntil, 10);
      if (dismissedTime > Date.now()) {
        setDismissedUntil(dismissedTime);
      } else {
        localStorage.removeItem('offlineNotificationDismissedUntil');
      }
    }

    // Add event listeners for online/offline events
    const handleOnline = () => {
      setIsOffline(false);
      setShowDetails(false);
    };

    const handleOffline = () => {
      setIsOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleDismiss = () => {
    // Dismiss for 1 hour
    const dismissUntil = Date.now() + (60 * 60 * 1000);
    localStorage.setItem('offlineNotificationDismissedUntil', dismissUntil.toString());
    setDismissedUntil(dismissUntil);
  };

  // Don't show if online or notification was dismissed
  if (!isOffline || (dismissedUntil && dismissedUntil > Date.now())) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-yellow-500 text-white p-4 rounded-lg shadow-lg z-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <div>
            <p className="font-medium">You're offline</p>
            <p className="text-sm">Some features may be limited</p>
          </div>
        </div>

        <div className="flex items-center">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="text-white mr-2 text-sm underline"
          >
            {showDetails ? 'Hide details' : 'Show details'}
          </button>

          <button
            onClick={handleDismiss}
            className="ml-4 text-white"
            aria-label="Dismiss"
          >
            <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {showDetails && (
        <div className="mt-3 pt-3 border-t border-yellow-400">
          <p className="text-sm mb-2">While offline, you can still:</p>
          <ul className="text-sm list-disc pl-5 mb-2">
            <li>View previously loaded meals and data</li>
            <li>Add new meals (they'll sync when you're back online)</li>
            <li>Use the app's basic features</li>
          </ul>
          <Link to="/pwa-info" className="text-sm text-white underline">
            Learn more about offline capabilities
          </Link>
        </div>
      )}
    </div>
  );
};

export default OfflineIndicator;
