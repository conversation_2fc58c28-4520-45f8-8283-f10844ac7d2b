import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { isAppInstalled } from '../../utils/registerSW';

/**
 * Component to prompt users to install the PWA
 * Enhanced with better UX and more detailed instructions
 */
const PwaInstallPrompt = () => {
  const [showPrompt, setShowPrompt] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState(null);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Check if already installed as PWA
    if (isAppInstalled()) {
      return;
    }

    // Check if the prompt was recently dismissed
    const dismissedTime = localStorage.getItem('pwaPromptDismissed');
    if (dismissedTime) {
      const dismissedAt = parseInt(dismissedTime, 10);
      const now = Date.now();
      // Don't show for 3 days after dismissal
      if (now - dismissedAt < 3 * 24 * 60 * 60 * 1000) {
        return;
      }
    }

    // Detect device type
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const isIOSDevice = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
    const isAndroidDevice = /android/i.test(userAgent);

    setIsIOS(isIOSDevice);
    setIsAndroid(isAndroidDevice);

    // Listen for beforeinstallprompt event (Chrome, Edge, etc.)
    const handleBeforeInstallPrompt = (e) => {
      // Prevent Chrome 76+ from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      setDeferredPrompt(e);
      // Show the install prompt after a delay
      setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // For iOS, show the prompt after a delay if the user has visited at least twice
    if (isIOSDevice) {
      const visitCount = parseInt(localStorage.getItem('visitCount') || '0', 10);
      localStorage.setItem('visitCount', (visitCount + 1).toString());

      if (visitCount >= 1) { // Show after second visit
        setTimeout(() => {
          setShowPrompt(true);
        }, 5000);
      }
    }

    // Clean up
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  // Handle install button click
  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      return;
    }

    // Show the install prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;
    console.log(`User response to the install prompt: ${outcome}`);

    // We no longer need the prompt, clear it
    setDeferredPrompt(null);
    setShowPrompt(false);

    // If accepted, mark as installed
    if (outcome === 'accepted') {
      localStorage.setItem('pwaInstalled', 'true');
    }
  };

  // Dismiss the prompt
  const handleDismiss = () => {
    setShowPrompt(false);
    // Store in localStorage to avoid showing again for a while
    localStorage.setItem('pwaPromptDismissed', Date.now().toString());
  };

  // Remind me later
  const handleRemindLater = () => {
    setShowPrompt(false);
    // Remind in 24 hours
    const remindTime = Date.now() + (24 * 60 * 60 * 1000);
    localStorage.setItem('pwaPromptRemindAt', remindTime.toString());
  };

  if (!showPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg z-50 border border-gray-200 dark:border-gray-700">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <svg className="h-5 w-5 mr-2 text-primary-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
            </svg>
            Install Calcounta App
          </h3>

          {isIOS ? (
            <div className="mt-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Install our app on your iPhone/iPad for offline access and a better experience.
              </p>

              {showDetails ? (
                <div className="mt-2">
                  <ol className="mt-2 text-sm text-gray-600 dark:text-gray-300 list-decimal pl-5">
                    <li>Tap the share icon <span className="inline-block">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                      </svg>
                    </span> in Safari</li>
                    <li>Scroll down and tap "Add to Home Screen"</li>
                    <li>Tap "Add" in the top right</li>
                  </ol>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="mt-3 text-primary-600 dark:text-primary-500 text-sm font-medium"
                  >
                    Hide details
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowDetails(true)}
                  className="mt-2 text-primary-600 dark:text-primary-500 text-sm font-medium"
                >
                  Show me how
                </button>
              )}
            </div>
          ) : isAndroid ? (
            <div className="mt-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Install our app on your Android device for offline access and a better experience.
              </p>

              {showDetails ? (
                <div className="mt-2">
                  <ol className="mt-2 text-sm text-gray-600 dark:text-gray-300 list-decimal pl-5">
                    <li>Tap the menu icon (three dots) in Chrome</li>
                    <li>Select "Install app" or "Add to Home screen"</li>
                    <li>Follow the on-screen instructions</li>
                  </ol>
                  <button
                    onClick={() => setShowDetails(false)}
                    className="mt-3 text-primary-600 dark:text-primary-500 text-sm font-medium"
                  >
                    Hide details
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowDetails(true)}
                  className="mt-2 text-primary-600 dark:text-primary-500 text-sm font-medium"
                >
                  Show me how
                </button>
              )}
            </div>
          ) : (
            <div className="mt-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Install our app for a better experience with offline access and faster loading.
              </p>
              <div className="mt-3 flex flex-wrap gap-2">
                <button
                  onClick={handleInstallClick}
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded text-sm font-medium"
                >
                  Install App
                </button>
                <button
                  onClick={handleRemindLater}
                  className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded text-sm font-medium"
                >
                  Remind Later
                </button>
              </div>
            </div>
          )}

          <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
            <Link to="/pwa-info" className="underline">Learn more about app features</Link>
          </div>
        </div>

        <button
          onClick={handleDismiss}
          className="ml-4 text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          aria-label="Close"
        >
          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default PwaInstallPrompt;
