import React from 'react';
import { format, isSameDay } from 'date-fns';
import CircularProgress from './CircularProgress';

/**
 * CaloriesLeftCard component for displaying calories left with horizontal layout
 * Shows large calorie number on left and circular progress ring on right
 *
 * @param {Date} selectedDate - Currently selected date
 * @param {number} calorieGoal - Daily calorie goal
 * @param {number} consumedCalories - Calories consumed for selected date
 * @param {number} remainingCalories - Remaining calories for selected date
 * @param {Object} macroPercentages - Macro percentages for circular progress
 * @param {string} className - Additional CSS classes
 */
const CaloriesLeftCard = ({
  selectedDate,
  calorieGoal = 2000,
  consumedCalories = 0,
  remainingCalories = 0,
  macroPercentages = { protein: 33, carbs: 34, fat: 33 },
  className = ""
}) => {
  const today = new Date();
  const isToday = isSameDay(selectedDate, today);

  return (
    <div className={`bg-white rounded-xl shadow-sm p-6 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left side - Calories left text and number */}
        <div className="flex-1">
          <h3 className="text-lg font-medium text-gray-500 mb-2">
            Calories left
          </h3>
          <div className="flex flex-col">
            <span className="text-6xl font-bold text-gray-900 leading-none">{remainingCalories}</span>
            <span className="text-gray-400 text-lg mt-1">Kcal</span>
          </div>
        </div>

        {/* Right side - Circular Progress Ring */}
        <div className="flex-shrink-0 ml-6">
          <CircularProgress
            percentage={Math.min(100, Math.round((consumedCalories / calorieGoal) * 100) || 0)}
            size="lg"
            segments={macroPercentages}
            icon="flame"
            className="!w-32 !h-32 sm:!w-36 sm:!h-36 md:!w-40 md:!h-40 lg:!w-44 lg:!h-44 xl:!w-48 xl:!h-48"
          />
        </div>
      </div>
    </div>
  );
};

export default CaloriesLeftCard;
