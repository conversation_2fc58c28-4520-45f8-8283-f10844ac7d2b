import { useState, useEffect } from 'react';
import { classNames } from '../../utils/classNames';

/**
 * TypewriterText component that animates text with a typewriter effect
 * @param {string} text - The text to animate
 * @param {number} speed - Typing speed in milliseconds (default: 50)
 * @param {number} delay - Delay before starting animation in milliseconds (default: 0)
 * @param {boolean} showCursor - Whether to show blinking cursor (default: true)
 * @param {string} className - Additional CSS classes
 * @param {function} onComplete - Callback when typing is complete
 * @param {boolean} startAnimation - Whether to start the animation (default: true)
 */
const TypewriterText = ({
  text,
  speed = 30, // Faster default speed
  delay = 0,
  showCursor = true,
  className = '',
  onComplete,
  startAnimation = true,
  cursorChar = '|'
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [showCursorBlink, setShowCursorBlink] = useState(true);

  useEffect(() => {
    if (!startAnimation || !text) return;

    const startTimer = setTimeout(() => {
      if (currentIndex < text.length) {
        const timer = setTimeout(() => {
          setDisplayText(text.slice(0, currentIndex + 1));
          setCurrentIndex(currentIndex + 1);
        }, speed);

        return () => clearTimeout(timer);
      } else if (!isComplete) {
        setIsComplete(true);
        if (onComplete) {
          onComplete();
        }
        // Hide cursor after completion if specified
        if (!showCursor) {
          setTimeout(() => setShowCursorBlink(false), 500);
        }
      }
    }, delay);

    return () => clearTimeout(startTimer);
  }, [currentIndex, text, speed, delay, startAnimation, isComplete, onComplete, showCursor]);

  // Reset when text changes
  useEffect(() => {
    if (startAnimation) {
      setDisplayText('');
      setCurrentIndex(0);
      setIsComplete(false);
      setShowCursorBlink(true);
    }
  }, [text, startAnimation]);

  return (
    <span className={classNames('inline-block', className)}>
      {displayText}
      {showCursor && showCursorBlink && (
        <span 
          className="animate-pulse ml-0.5"
          style={{
            animation: 'blink 1s infinite',
            animationDelay: '0.5s'
          }}
        >
          {cursorChar}
        </span>
      )}
      <style jsx>{`
        @keyframes blink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0; }
        }
      `}</style>
    </span>
  );
};

/**
 * TypewriterParagraph component for multi-line text with typewriter effect
 */
export const TypewriterParagraph = ({
  lines,
  speed = 30, // Faster default speed
  lineDelay = 300, // Shorter delay between lines
  startDelay = 0,
  className = '',
  onComplete
}) => {
  const [currentLineIndex, setCurrentLineIndex] = useState(0);
  const [startCurrentLine, setStartCurrentLine] = useState(false);

  useEffect(() => {
    if (currentLineIndex === 0) {
      const timer = setTimeout(() => {
        setStartCurrentLine(true);
      }, startDelay);
      return () => clearTimeout(timer);
    }
  }, [startDelay, currentLineIndex]);

  const handleLineComplete = () => {
    if (currentLineIndex < lines.length - 1) {
      setTimeout(() => {
        setCurrentLineIndex(currentLineIndex + 1);
        setStartCurrentLine(true);
      }, lineDelay);
    } else if (onComplete) {
      onComplete();
    }
  };

  return (
    <div className={className}>
      {lines.map((line, index) => (
        <div key={index} className="min-h-[1.5em]">
          {index <= currentLineIndex && (
            <TypewriterText
              text={line}
              speed={speed}
              startAnimation={index === currentLineIndex ? startCurrentLine : index < currentLineIndex}
              showCursor={index === currentLineIndex && index === lines.length - 1}
              onComplete={index === currentLineIndex ? handleLineComplete : undefined}
            />
          )}
        </div>
      ))}
    </div>
  );
};

/**
 * TypewriterList component for animating list items with typewriter effect
 */
export const TypewriterList = ({
  items,
  speed = 25, // Faster default speed
  itemDelay = 200, // Shorter delay between items
  startDelay = 0,
  className = '',
  itemClassName = '',
  onComplete
}) => {
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [startCurrentItem, setStartCurrentItem] = useState(false);

  useEffect(() => {
    if (currentItemIndex === 0) {
      const timer = setTimeout(() => {
        setStartCurrentItem(true);
      }, startDelay);
      return () => clearTimeout(timer);
    }
  }, [startDelay, currentItemIndex]);

  const handleItemComplete = () => {
    if (currentItemIndex < items.length - 1) {
      setTimeout(() => {
        setCurrentItemIndex(currentItemIndex + 1);
        setStartCurrentItem(true);
      }, itemDelay);
    } else if (onComplete) {
      onComplete();
    }
  };

  return (
    <div className={className}>
      {items.map((item, index) => (
        <div key={index} className={classNames('min-h-[1.5em]', itemClassName)}>
          {index <= currentItemIndex && (
            <TypewriterText
              text={item}
              speed={speed}
              startAnimation={index === currentItemIndex ? startCurrentItem : index < currentItemIndex}
              showCursor={index === currentItemIndex && index === items.length - 1}
              onComplete={index === currentItemIndex ? handleItemComplete : undefined}
            />
          )}
        </div>
      ))}
    </div>
  );
};

export default TypewriterText;
