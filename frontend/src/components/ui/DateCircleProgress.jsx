import React from 'react';
import { cn } from '../../lib/utils';

/**
 * DateCircleProgress component for displaying a date with a circular progress indicator
 *
 * @param {number} percentage - Progress percentage (0-100)
 * @param {string} dateText - The date text to display inside the circle
 * @param {boolean} isSelected - Whether the date is selected
 * @param {boolean} isToday - Whether the date is today
 * @param {boolean} isDisabled - Whether the date is disabled
 * @param {string} className - Additional CSS classes
 */
const DateCircleProgress = ({
  percentage = 0,
  dateText,
  isSelected = false,
  isToday = false,
  isDisabled = false,
  className
}) => {
  // Constrain percentage between 0 and 100
  const normalizedPercentage = Math.min(100, Math.max(0, percentage));

  // Calculate the stroke-dasharray and stroke-dashoffset for the activity ring
  const outerRadius = 17; // Outer ring (activity ring)
  const innerRadius = 12; // Inner circle (date background)
  const circumference = 2 * Math.PI * outerRadius;
  const strokeDashoffset = circumference - (normalizedPercentage / 100) * circumference;

  // Determine background color based on percentage
  const getBackgroundColor = () => {
    if (isSelected) return ""; // No background color for selected dates
    if (normalizedPercentage === 0) return "";
    if (normalizedPercentage < 25) return "bg-red-100";
    if (normalizedPercentage < 75) return "bg-yellow-100";
    if (normalizedPercentage < 100) return "bg-green-100";
    return "bg-blue-100";
  };

  // Determine text color based on percentage
  const getTextColor = () => {
    if (isSelected) return "text-white"; // White text for selected dates
    if (normalizedPercentage === 0) return "";
    if (normalizedPercentage < 25) return "text-red-700";
    if (normalizedPercentage < 75) return "text-yellow-700";
    if (normalizedPercentage < 100) return "text-green-700";
    return "text-blue-700";
  };

  // Ring color is based on percentage for today, black for other days
  const getRingColor = () => {
    if (isToday) {
      // For today, use color based on percentage
      if (normalizedPercentage === 0) return "#ef4444"; // Red for no calories logged
      if (normalizedPercentage < 25) return "#ef4444"; // Red for < 25%
      if (normalizedPercentage < 75) return "#fbbf24"; // Yellow for < 75%
      if (normalizedPercentage < 100) return "#22c55e"; // Green for < 100%
      return "#3b82f6"; // Blue for >= 100%
    }
    return "#000"; // Black for other days
  };

  return (
    <div
      className={cn(
        "relative flex items-center justify-center w-8 h-8 bg-white",
        isDisabled ? "opacity-50" : "",
        className
      )}
    >
      {/* SVG for the activity ring */}
      <svg className="absolute w-full h-full" viewBox="0 0 40 40">

        {/* Activity ring - colored based on date type and percentage */}
        {(normalizedPercentage > 0 || isToday) && (
          <circle
            cx="20"
            cy="20"
            r={outerRadius}
            fill="none"
            stroke={getRingColor()}
            strokeOpacity={isSelected ? "1" : "0.9"}
            strokeWidth={isSelected ? "3.5" : "3"}
            strokeDasharray={circumference}
            strokeDashoffset={isToday && normalizedPercentage === 0 ? 0 : strokeDashoffset}
            strokeLinecap="round"
            transform="rotate(-90 20 20)"
          />
        )}
      </svg>

      {/* White circle to create a gap between the ring and colored background */}
      <div className={cn(
        "absolute bg-white rounded-full",
        isSelected ? "w-7 h-7" : "w-6 h-6"
      )}></div>

      {/* Container for the date with appropriate styling */}
      <div
        className={cn(
          "z-10 flex items-center justify-center rounded-full text-xs font-medium",
          isSelected ? "bg-black text-white w-6 h-6" :
          isToday ? "bg-black text-white w-5 h-5" :
          normalizedPercentage > 0 ? `${getBackgroundColor()} ${getTextColor()} w-5 h-5` : "bg-white border border-gray-200 w-5 h-5",
          isDisabled ? "cursor-not-allowed" : "cursor-pointer"
        )}
      >
        {dateText}
      </div>
    </div>
  );
};

export default DateCircleProgress;
