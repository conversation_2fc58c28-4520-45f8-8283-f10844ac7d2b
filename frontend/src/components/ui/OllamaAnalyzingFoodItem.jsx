import React, { useState, useEffect } from 'react';
import { classNames } from '../../utils/classNames';
import ollamaProgressService from '../../services/ollamaProgressService';

const OllamaAnalyzingFoodItem = ({ 
  food, 
  onCancel, 
  requestId,
  isMobile = false 
}) => {
  const [progressState, setProgressState] = useState({
    stage: 'initializing',
    progress: 0,
    message: 'Initializing analysis...',
    startTime: Date.now()
  });

  const [elapsedTime, setElapsedTime] = useState(0);

  // Update elapsed time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - progressState.startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [progressState.startTime]);

  // Subscribe to progress updates
  useEffect(() => {
    if (!requestId) return;

    const handleProgress = (progress) => {
      setProgressState(progress);
    };

    const handleComplete = (result) => {
      setProgressState(prev => ({
        ...prev,
        stage: 'complete',
        progress: 100,
        message: 'Analysis complete!'
      }));
    };

    const handleError = (error) => {
      setProgressState(prev => ({
        ...prev,
        stage: 'error',
        progress: 0,
        message: `Error: ${error.message}`
      }));
    };

    // Start tracking if not already tracking
    if (!ollamaProgressService.isTracking(requestId)) {
      ollamaProgressService.startTracking(requestId, {
        onProgress: handleProgress,
        onComplete: handleComplete,
        onError: handleError
      });
    }

    // Get current progress if already tracking
    const currentProgress = ollamaProgressService.getProgress(requestId);
    if (currentProgress) {
      setProgressState(currentProgress);
    }

    return () => {
      // Don't cleanup here as the request might still be ongoing
      // Cleanup will be handled by the service when request completes
    };
  }, [requestId]);

  const formatElapsedTime = (seconds) => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStageColor = (stage) => {
    switch (stage) {
      case 'uploading':
        return 'bg-blue-500';
      case 'downloading':
        return 'bg-purple-500';
      case 'loading':
        return 'bg-yellow-500';
      case 'analyzing':
        return 'bg-green-500';
      case 'complete':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStageIcon = (stage) => {
    switch (stage) {
      case 'uploading':
        return '⬆️';
      case 'downloading':
        return '⬇️';
      case 'loading':
        return '🔄';
      case 'analyzing':
        return '🔍';
      case 'complete':
        return '✅';
      case 'error':
        return '❌';
      default:
        return '⏳';
    }
  };

  if (isMobile) {
    return (
      <div className="bg-white rounded-xl border border-gray-100 overflow-hidden w-full max-w-md h-24 relative">
        <div className="flex items-start p-4 h-full">
          {/* Food image with overlay */}
          <div className="flex-shrink-0 mr-4 w-16 relative">
            <div className="w-16 h-16 rounded-lg overflow-hidden relative">
              <img
                src={food.image_url}
                alt="Analyzing food"
                className="w-full h-full object-cover"
              />
              
              {/* Analyzing overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center">
                <div className="flex flex-col items-center space-y-1">
                  <span className="text-white text-xs">{getStageIcon(progressState.stage)}</span>
                  <div className="w-8 bg-white bg-opacity-30 rounded-full h-0.5">
                    <div
                      className={classNames("h-0.5 rounded-full transition-all duration-300 ease-out", getStageColor(progressState.stage))}
                      style={{ width: `${progressState.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start mb-2">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                Analyzing food...
              </h3>
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="text-gray-400 hover:text-red-500 text-xs ml-2"
                  aria-label="Cancel analysis"
                >
                  ✕
                </button>
              )}
            </div>

            <div className="space-y-1">
              <p className="text-xs text-gray-600">{progressState.message}</p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>{Math.round(progressState.progress)}%</span>
                <span>{formatElapsedTime(elapsedTime)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Desktop version
  return (
    <div className="bg-white rounded-xl border border-gray-100 overflow-hidden transition-all hover:shadow-md w-full max-w-md">
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 rounded-lg overflow-hidden relative">
              <img
                src={food.image_url}
                alt="Analyzing food"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center">
                <span className="text-white text-lg">{getStageIcon(progressState.stage)}</span>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Analyzing Food</h3>
              <p className="text-sm text-gray-600">{progressState.message}</p>
            </div>
          </div>
          {onCancel && (
            <button
              onClick={onCancel}
              className="text-gray-400 hover:text-red-500 text-sm px-3 py-1 bg-gray-50 rounded-lg transition-colors"
              aria-label="Cancel analysis"
            >
              Cancel
            </button>
          )}
        </div>

        {/* Progress bar */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">
              {progressState.stage.charAt(0).toUpperCase() + progressState.stage.slice(1)}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progressState.progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={classNames("h-2 rounded-full transition-all duration-300 ease-out", getStageColor(progressState.stage))}
              style={{ width: `${progressState.progress}%` }}
            ></div>
          </div>
        </div>

        {/* Stats */}
        <div className="flex justify-between items-center text-sm text-gray-500">
          <span>Elapsed: {formatElapsedTime(elapsedTime)}</span>
          <span>Using Ollama</span>
        </div>

        {/* Placeholder nutritional information */}
        <div className="pt-4 border-t border-gray-100 mt-4">
          <h4 className="text-xs font-medium text-gray-700 mb-2">Analyzing Nutritional Information</h4>
          <div className="grid grid-cols-3 gap-x-4 gap-y-2 text-xs">
            {['Calories', 'Protein', 'Carbs', 'Fat', 'Fiber', 'Sugar'].map((nutrient) => (
              <div key={nutrient} className="flex justify-between items-center bg-gray-50 px-2 py-1 rounded">
                <span className="text-gray-600">{nutrient}</span>
                <div className="w-6 h-3 bg-gray-200 rounded animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OllamaAnalyzingFoodItem;
