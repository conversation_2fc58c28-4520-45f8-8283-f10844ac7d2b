
import CircularProgress from './CircularProgress';
import { cn } from '../../lib/utils';
import { ProteinIcon, CarbsIcon, FatsIcon } from '../../assets/icons/DashboardIcons';

/**
 * MacroCard component for displaying macronutrient information
 *
 * @param {string} type - Type of macronutrient ('protein', 'carbs', 'fat')
 * @param {number} value - Current value in grams
 * @param {number} total - Total goal value in grams
 * @param {string} className - Additional CSS classes
 */
const MacroCard = ({ type, value, total, className }) => {
  // Ensure value and total are valid numbers
  const safeValue = parseFloat(value) || 0;
  const safeTotal = parseFloat(total) || 1; // Default to 1 to avoid division by zero

  const remaining = safeTotal - safeValue;
  const percentage = (safeValue / safeTotal) * 100;

  const colorMap = {
    protein: 'protein-blue',
    carbs: 'carbs-green',
    fat: 'fat-yellow'
  };

  // Responsive icon sizes using viewport width for mobile scaling (50% larger)
  const iconMap = {
    protein: <ProteinIcon className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 xl:w-12 xl:h-12 text-protein-blue" />,
    carbs: <CarbsIcon className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 xl:w-12 xl:h-12 text-carbs-green" />,
    fat: <FatsIcon className="w-8 h-8 md:w-9 md:h-9 lg:w-10 lg:h-10 xl:w-12 xl:h-12 text-fat-yellow" />
  };

  const nameMap = {
    protein: 'Protein',
    carbs: 'Carbs',
    fat: 'Fat'
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl flex flex-col",
        // Rectangular card with 4:5 aspect ratio
        "macro-card-rectangle",
        // Responsive padding with overflow hidden to contain circles
        "p-2 md:p-3 lg:p-4 overflow-hidden",
        className
      )}
    >
      {/* Header with icon and name - takes 15% of height */}
      <div className="flex items-center justify-center gap-1 w-full h-[15%] min-h-[15%]">
        {iconMap[type]}
        <p className="font-medium text-gray-700 text-xs md:text-sm lg:text-base">
          {nameMap[type]}
        </p>
      </div>

      {/* Circular progress - takes 85% of height */}
      <div className="flex items-center justify-center w-full h-[85%] min-h-[85%]">
        <CircularProgress
          percentage={percentage}
          color={colorMap[type]}
          size="lg"
          strokeWidth={2}
        >
          <div className="flex flex-col items-center justify-center">
            <span className="font-bold leading-tight text-sm md:text-base lg:text-lg">
              {Math.round(remaining)}g
            </span>
            <span className="text-gray-400 leading-tight text-xs md:text-sm">
              Left
            </span>
          </div>
        </CircularProgress>
      </div>
    </div>
  );
};

export default MacroCard;
