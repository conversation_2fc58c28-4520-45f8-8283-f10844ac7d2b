import React from 'react';

/**
 * iOS-style loading spinner component
 * Features 12 radial segments with fade animation, exactly matching iOS design
 *
 * @param {string} size - Size variant: 'sm', 'md', 'lg', 'xl' (default: 'md')
 * @param {string} color - Color variant: 'light', 'dark', 'primary' (default: 'dark')
 * @param {string} className - Additional CSS classes
 */
const IOSSpinner = ({
  size = 'md',
  color = 'dark',
  className = ''
}) => {
  // Size configurations - using exact proportions
  const sizeConfig = {
    sm: { containerSize: 16, segmentWidth: 2, segmentHeight: 6 },
    md: { containerSize: 32, segmentWidth: 3, segmentHeight: 10 },
    lg: { containerSize: 48, segmentWidth: 4, segmentHeight: 14 },
    xl: { containerSize: 64, segmentWidth: 6, segmentHeight: 18 }
  };

  // Color configurations
  const colorConfig = {
    light: '#ffffff',
    dark: '#000000',
    primary: '#007AFF'
  };

  const { containerSize, segmentWidth, segmentHeight } = sizeConfig[size] || sizeConfig.md;
  const segmentColor = colorConfig[color] || colorConfig.dark;

  // Generate 12 segments with rotation and opacity
  const segments = Array.from({ length: 12 }, (_, index) => {
    const rotation = index * 30; // 360 / 12 = 30 degrees per segment
    const opacity = 1 - (index * 0.08); // Fade from 1 to ~0.12

    return (
      <div
        key={index}
        className="absolute"
        style={{
          width: `${segmentWidth}px`,
          height: `${segmentHeight}px`,
          backgroundColor: segmentColor,
          borderRadius: `${segmentWidth}px`,
          top: '2px',
          left: '50%',
          transformOrigin: `50% ${containerSize / 2 - 2}px`,
          transform: `translateX(-50%) rotate(${rotation}deg)`,
          opacity: opacity,
          animation: `iosSpinnerRotate 1.2s linear infinite`,
          animationDelay: `${-1.1 + (index * 0.1)}s`
        }}
      />
    );
  });

  return (
    <div
      className={`relative ${className}`}
      style={{
        width: `${containerSize}px`,
        height: `${containerSize}px`
      }}
    >
      {segments}
    </div>
  );
};

export default IOSSpinner;
