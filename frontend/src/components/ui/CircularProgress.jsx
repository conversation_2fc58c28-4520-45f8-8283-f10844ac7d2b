import React from 'react';
import { cn } from '../../lib/utils';
import { CalorieIcon } from '../../assets/icons/DashboardIcons';

/**
 * CircularProgress component for displaying progress in a circular format
 *
 * @param {number} percentage - Progress percentage (0-100)
 * @param {string} size - Size of the circle ('sm', 'md', 'lg')
 * @param {string} color - Color of the progress stroke
 * @param {string} className - Additional CSS classes
 * @param {React.ReactNode} children - Optional content to display inside the circle
 * @param {Object} segments - Optional segments with different colors (e.g. {protein: 30, carbs: 40, fat: 30})
 * @param {string} icon - Optional icon to display inside (e.g. 'flame' for calories)
 */
const CircularProgress = ({
  percentage,
  size = 'md',
  color = 'protein-blue',
  className,
  children,
  segments,
  icon,
  strokeWidth: customStrokeWidth
}) => {
  // Constrain percentage between 0 and 100
  const normalizedPercentage = Math.min(100, Math.max(0, percentage));

  // Calculate the stroke-dasharray and stroke-dashoffset
  const radius = size === 'xs' ? 18 : size === 'sm' ? 20 : size === 'md' ? 32 : size === 'lg' ? 40 : 46;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (normalizedPercentage / 100) * circumference;

  // Responsive sizing using viewport and container-relative dimensions
  const getSizeStyles = () => {
    let sizeClasses;
    switch (size) {
      case 'xs':
        sizeClasses = 'w-24 h-24 sm:w-28 sm:h-28'; // 96px -> 112px
        break;
      case 'sm':
        sizeClasses = 'w-32 h-32 sm:w-36 sm:h-36'; // 128px -> 144px
        break;
      case 'md':
        sizeClasses = 'w-28 h-28 sm:w-32 sm:h-32 md:w-36 md:h-36 lg:w-40 lg:h-40 xl:w-44 xl:h-44'; // 112px -> 128px -> 144px -> 160px -> 176px
        break;
      case 'lg':
        sizeClasses = 'w-40 h-40 sm:w-44 sm:h-44 md:w-48 md:h-48 lg:w-52 lg:h-52 xl:w-56 xl:h-56'; // 160px -> 176px -> 192px -> 208px -> 224px
        break;
      case 'xl':
        sizeClasses = 'w-48 h-48 sm:w-52 sm:h-52 md:w-56 md:h-56 lg:w-60 lg:h-60 xl:w-64 xl:h-64'; // 192px -> 208px -> 224px -> 240px -> 256px
        break;
      default:
        sizeClasses = 'w-32 h-32 sm:w-36 sm:h-36'; // Default to md-like size
        break;
    }

    return { className: sizeClasses };
  };

  const strokeWidth = customStrokeWidth || (size === 'xs' ? 2 : size === 'sm' ? 2 : size === 'md' ? 2.5 : size === 'lg' ? 3 : 3.5);

  // Calculate segment positions if provided
  const renderSegments = () => {
    if (!segments) return null;

    // Calculate the total percentage of the ring that should be filled
    // This is based on the overall percentage (calories consumed / calorie goal)
    const totalRingPercentage = normalizedPercentage;

    // If no calories consumed yet, don't render any segments
    if (totalRingPercentage === 0) return null;

    let startAngle = 0; // Start from the top (0 degrees)
    const segmentElements = [];

    // Define color map for macronutrients
    const colorMap = {
      protein: 'protein-blue',
      carbs: 'carbs-green',
      fat: 'fat-yellow'
    };

    // Calculate the total of all segment percentages to ensure they add up to 100%
    const totalSegmentPercentage = Object.values(segments).reduce((sum, value) => sum + value, 0);

    // Render each segment proportionally to its percentage of total macronutrients
    // and scaled to the overall percentage of calories consumed
    Object.entries(segments).forEach(([key, value], index) => {
      // Calculate what percentage of the total ring this segment should occupy
      // based on its proportion of total macronutrients
      const segmentPercentage = (value / totalSegmentPercentage) * totalRingPercentage;

      // Calculate the arc length for this segment
      const segmentLength = (segmentPercentage / 100) * circumference;

      // Calculate the offset for this segment (where it starts in the circle)
      // We subtract from circumference because SVG stroke-dashoffset goes clockwise from 3 o'clock
      // but we want to start from 12 o'clock and go clockwise
      const segmentOffset = circumference - (startAngle / 100) * circumference;

      // Only render segments that have a non-zero length
      if (segmentLength > 0) {
        segmentElements.push(
          <circle
            key={key}
            cx="50"
            cy="50"
            r={radius}
            fill="none"
            stroke={`currentColor`}
            className={`text-${colorMap[key] || color}`}
            strokeWidth={strokeWidth}
            strokeDasharray={`${segmentLength} ${circumference - segmentLength}`}
            strokeDashoffset={segmentOffset}
            strokeLinecap="round"
            transform="rotate(-90 50 50)"
          />
        );
      }

      // Update the starting angle for the next segment
      startAngle += segmentPercentage;
    });

    return segmentElements;
  };

  // Render appropriate icon
  const renderIcon = () => {
    if (children) return children;

    // Use responsive Tailwind classes for the calorie icon, scaled by size and breakpoint

    if (icon === 'flame') {
      const iconSize = size === 'xs' ? 'text-black w-9 h-12 sm:w-10 sm:h-12' :
                       size === 'sm' ? 'text-black w-12 h-16 sm:w-14 sm:h-16' :
                       size === 'md' ? 'text-black w-10 h-12 sm:w-12 sm:h-16 md:w-14 md:h-16 lg:w-16 lg:h-16 xl:w-16 xl:h-16' :
                       size === 'lg' ? 'text-black w-16 h-16 sm:w-16 sm:h-16 md:w-16 md:h-16 lg:w-16 lg:h-16 xl:w-16 xl:h-16' :
                       'text-black w-16 h-16 sm:w-16 sm:h-16 md:w-16 md:h-16 lg:w-16 lg:h-16 xl:w-16 xl:h-16';
      return (
        <CalorieIcon
          className={iconSize}
        />
      );
    }

    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className={`text-${color} w-6 h-8 sm:w-7 sm:h-9`}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" />
      </svg>
    );
  };

  const sizeStyles = getSizeStyles();

  return (
    <div
      className={cn('relative flex items-center justify-center', sizeStyles.className, className)}
    >
      <svg className="w-full h-full" viewBox="0 0 100 100">
        {/* Background circle */}
        <circle
          cx="50"
          cy="50"
          r={radius}
          fill="none"
          stroke="#e6e6e6"
          strokeWidth={strokeWidth}
        />

        {/* Render segments if provided, otherwise render single progress circle */}
        {segments ? renderSegments() : (
          <circle
            cx="50"
            cy="50"
            r={radius}
            fill="none"
            stroke={`currentColor`}
            className={`text-${color}`}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            transform="rotate(-90 50 50)"
          />
        )}
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        {renderIcon()}
      </div>
    </div>
  );
};

export default CircularProgress;
