import React, { useState, useEffect } from 'react';
import { getAbsoluteImageUrl } from '../../utils/imageUtils';
import { cn } from '../../lib/utils';

/**
 * AnalyzingFoodItem component for displaying food items that are being analyzed
 * Matches the exact dimensions and layout of the regular MealItem component
 * Features progressive loading indicators with different analysis stages
 *
 * @param {Object} item - The analyzing food item data
 * @param {boolean} isMobile - Whether the current viewport is mobile
 * @param {Function} onCancel - Function to call when user cancels the analysis
 */
const AnalyzingFoodItem = ({ item, isMobile = false, onCancel }) => {
  const currentTime = new Date().toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  // Progressive loading stages
  const analysisStages = [
    { id: 'analyzing', message: 'Analyzing food...', duration: 2000 },
    { id: 'separating', message: 'Separating ingredients...', duration: 3000 },
    { id: 'calculating', message: 'Calculating macronutrients...', duration: 2500 },
    { id: 'processing', message: 'Processing nutritional data...', duration: 2000 },
    { id: 'finalizing', message: 'Finalizing analysis...', duration: 1500 }
  ];

  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [progress, setProgress] = useState(0);

  // Progress through analysis stages
  useEffect(() => {
    if (currentStageIndex >= analysisStages.length) return;

    const currentStage = analysisStages[currentStageIndex];
    const startTime = Date.now();

    const progressInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const stageProgress = Math.min((elapsed / currentStage.duration) * 100, 100);
      setProgress(stageProgress);

      if (stageProgress >= 100) {
        clearInterval(progressInterval);
        // Move to next stage after a brief pause
        setTimeout(() => {
          setCurrentStageIndex(prev => prev + 1);
          setProgress(0);
        }, 200);
      }
    }, 50);

    return () => clearInterval(progressInterval);
  }, [currentStageIndex]);

  const getCurrentStage = () => {
    if (currentStageIndex >= analysisStages.length) {
      return { message: 'Analysis complete...', id: 'complete' };
    }
    return analysisStages[currentStageIndex];
  };

  const getOverallProgress = () => {
    const baseProgress = (currentStageIndex / analysisStages.length) * 100;
    const currentStageProgress = (progress / 100) * (100 / analysisStages.length);
    return Math.min(baseProgress + currentStageProgress, 100);
  };

  return (
    <div
      className={cn(
        "bg-white rounded-xl shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md animate-fadeIn",
        isMobile ?
          // Mobile: match exact MealItem dimensions - collapsed state
          "p-4 h-32 relative w-full" :
          // Desktop: match exact MealItem dimensions
          "p-6 relative w-full h-auto shadow-sm"
      )}
    >
      <div className={`${isMobile ? "flex justify-between" : "flex flex-col"} h-full transition-all duration-500`}>

        {/* Desktop layout - matches MealItem desktop structure */}
        {!isMobile && (
          <>
            {/* Header with name */}
            <div className="mb-3">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-bold text-gray-900 flex items-center">
                  <span>Analyzing food</span>
                  <div className="flex space-x-1 ml-2">
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
                    <div className="w-1 h-1 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
                  </div>
                </h2>
              </div>
            </div>

            {/* Image section - matches MealItem desktop image */}
            <div className="w-full h-48 mb-4 rounded-lg overflow-hidden relative">
              <img
                src={getAbsoluteImageUrl(item.image_url)}
                alt="Analyzing food"
                className="h-full w-full object-cover transition-all duration-500"
                onError={(e) => {
                  console.warn('Failed to load analyzing food image:', item.image_url);
                  e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
                }}
              />

              {/* Progressive Analyzing overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center">
                <div className="bg-white bg-opacity-20 backdrop-blur-md rounded-2xl p-4 border border-white border-opacity-30 max-w-xs">
                  <div className="flex flex-col items-center space-y-3">
                    <div className="text-white text-center">
                      <p className="text-sm font-medium">{getCurrentStage().message}</p>

                      {/* Progress bar */}
                      <div className="w-32 bg-white bg-opacity-30 rounded-full h-1.5 mt-2 mb-2">
                        <div
                          className="bg-white h-1.5 rounded-full transition-all duration-300 ease-out"
                          style={{ width: `${getOverallProgress()}%` }}
                        ></div>
                      </div>

                      {/* Stage indicators */}
                      <div className="flex space-x-1 mt-1 justify-center">
                        {analysisStages.map((stage, index) => (
                          <div
                            key={stage.id}
                            className={cn(
                              "w-1.5 h-1.5 rounded-full transition-all duration-300",
                              index < currentStageIndex ? "bg-white" :
                              index === currentStageIndex ? "bg-white bg-opacity-70 animate-pulse" : "bg-white bg-opacity-30"
                            )}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Time stamp overlay - matches MealItem style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-xs px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {currentTime}
              </div>
            </div>

            {/* Placeholder macro pills - matches MealItem desktop layout */}
            <div className="flex flex-wrap items-center gap-3 mb-3 justify-center">
              <div className="flex items-center bg-blue-100 px-3 py-1 rounded-full">
                <div className="w-5 h-5 bg-protein-blue rounded-full mr-1 animate-pulse"></div>
                <span className="text-sm text-gray-400 font-medium">--g</span>
              </div>
              <div className="flex items-center bg-green-100 px-3 py-1 rounded-full">
                <div className="w-5 h-5 bg-carbs-green rounded-full mr-1 animate-pulse"></div>
                <span className="text-sm text-gray-400 font-medium">--g</span>
              </div>
              <div className="flex items-center bg-yellow-100 px-3 py-1 rounded-full">
                <div className="w-5 h-5 bg-fat-yellow rounded-full mr-1 animate-pulse"></div>
                <span className="text-sm text-gray-400 font-medium">--g</span>
              </div>
            </div>

            {/* Progressive status and cancel button - matches MealItem desktop layout */}
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center">
                <div className="w-16 h-10 bg-gray-200 rounded animate-pulse mr-2"></div>
                <div className="flex flex-col">
                  <span className="text-sm font-normal text-gray-600">{getCurrentStage().message}</span>
                  <div className="w-24 bg-gray-200 rounded-full h-1 mt-1">
                    <div
                      className="bg-blue-500 h-1 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${getOverallProgress()}%` }}
                    ></div>
                  </div>
                </div>
              </div>
              {onCancel && (
                <button
                  onClick={onCancel}
                  className="text-gray-400 hover:text-red-500 text-sm px-3 py-1 bg-gray-50 rounded-lg transition-colors"
                  aria-label="Cancel analysis"
                >
                  Cancel
                </button>
              )}
            </div>

            {/* Placeholder nutritional information - matches MealItem desktop layout */}
            <div className="pt-2 border-t border-gray-100">
              <h4 className="text-xs font-medium text-gray-700 mb-2 sticky top-0 bg-white">Analyzing Nutritional Information</h4>
              <div className="grid grid-cols-3 gap-x-4 gap-y-2 text-xs">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <div key={i} className="flex justify-between items-center bg-gray-50 px-2 py-1 rounded">
                    <div className="w-8 h-3 bg-gray-200 rounded animate-pulse"></div>
                    <div className="w-6 h-3 bg-gray-200 rounded animate-pulse"></div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Mobile layout - matches MealItem mobile collapsed structure exactly */}
        {isMobile && (
          <div className="flex justify-between w-full transition-all duration-300 ease-in-out">
            <div className="flex flex-col flex-1 mr-4 min-w-0 transition-all duration-300">
              <div className="h-10 mb-1">
                <h3 className="font-medium line-clamp-2 transition-all duration-300 text-sm">
                  <span>{getCurrentStage().message}</span>
                </h3>
                {/* Progress bar for mobile */}
                <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                  <div
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${getOverallProgress()}%` }}
                  ></div>
                </div>
              </div>

              <div className="flex items-center gap-3 mb-2 transition-all duration-300">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-protein-blue rounded-full mr-1 animate-pulse"></div>
                  <span className="text-xs text-gray-400">--g</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-carbs-green rounded-full mr-1 animate-pulse"></div>
                  <span className="text-xs text-gray-400">--g</span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-fat-yellow rounded-full mr-1 animate-pulse"></div>
                  <span className="text-xs text-gray-400">--g</span>
                </div>
              </div>

              <div className="mt-auto transition-opacity duration-300">
                <div className="flex items-center">
                  <div className="w-12 h-8 bg-gray-200 rounded animate-pulse mr-2"></div>
                  <span className="text-xs font-normal text-gray-500">{Math.round(getOverallProgress())}% complete</span>
                </div>
              </div>
            </div>

            {/* Image section - matches MealItem mobile image exactly */}
            <div className="h-20 w-20 sm:h-24 sm:w-24 rounded-lg overflow-hidden relative flex-shrink-0 transition-all duration-500 ease-in-out">
              <img
                src={getAbsoluteImageUrl(item.image_url)}
                alt="Analyzing food"
                className="h-full w-full object-cover transition-all duration-500"
                onError={(e) => {
                  console.warn('Failed to load analyzing food image (mobile):', item.image_url);
                  e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
                }}
              />

              {/* Progressive analyzing overlay - smaller for mobile */}
              <div className="absolute inset-0 bg-black bg-opacity-40 backdrop-blur-sm flex items-center justify-center">
                <div className="flex flex-col items-center space-y-1">
                  {/* Stage indicators */}
                  <div className="flex space-x-1">
                    {analysisStages.map((stage, index) => (
                      <div
                        key={stage.id}
                        className={cn(
                          "w-1 h-1 rounded-full transition-all duration-300",
                          index < currentStageIndex ? "bg-white" :
                          index === currentStageIndex ? "bg-white bg-opacity-70 animate-pulse" : "bg-white bg-opacity-30"
                        )}
                      />
                    ))}
                  </div>
                  {/* Mini progress bar */}
                  <div className="w-8 bg-white bg-opacity-30 rounded-full h-0.5">
                    <div
                      className="bg-white h-0.5 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${getOverallProgress()}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Time stamp overlay - matches MealItem mobile style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-[10px] px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {currentTime}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyzingFoodItem;
