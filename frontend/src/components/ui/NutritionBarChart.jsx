import { cn } from '../../lib/utils';

/**
 * NutritionBarChart component for displaying nutritional data as bars
 *
 * @param {Array} data - Array of daily calorie data
 * @param {Array} proteinCalories - Array of protein calories for each day
 * @param {Array} carbsCalories - Array of carbs calories for each day
 * @param {Array} fatsCalories - Array of fats calories for each day
 * @param {number} calorieTarget - Daily calorie target (represents 100% height)
 * @param {Array} labels - Array of labels for each bar (e.g., days of week)
 * @param {string} className - Additional CSS classes
 */
const NutritionBarChart = ({
  data,
  proteinCalories,
  carbsCalories,
  fatsCalories,
  calorieTarget = 2000, // Default to 2000 calories if not provided
  labels = ['S', 'M', 'T', 'W', 'T', 'F', 'S'],
  className
}) => {
  // Ensure we have valid arrays
  const dailyData = Array.isArray(data) ? data : Array(7).fill(0);

  // These should be calorie values, not gram values
  const proteinCals = Array.isArray(proteinCalories) ? proteinCalories : Array(7).fill(0);
  const carbsCals = Array.isArray(carbsCalories) ? carbsCalories : Array(7).fill(0);
  const fatsCals = Array.isArray(fatsCalories) ? fatsCalories : Array(7).fill(0);

  // Log the data we're receiving
  console.log('Daily calorie data:', dailyData);
  console.log('Protein calories:', proteinCals);
  console.log('Carbs calories:', carbsCals);
  console.log('Fats calories:', fatsCals);

  // Check if we have any data
  const hasData = dailyData.some(calories => calories > 0);

  // Find the maximum calorie value to ensure Y-axis can accommodate all values
  // If there's no data, use the calorie target as the maximum
  const maxCalorieValue = hasData ? Math.max(...dailyData) : 0;

  // Calculate Y-axis maximum value
  // Always use the highest calorie value with sufficient padding
  // This ensures all bars are properly scaled and visible
  let yAxisMax;

  if (maxCalorieValue > 0) {
    // If we have actual data, use the maximum value with padding
    yAxisMax = Math.ceil(maxCalorieValue * 1.2 / 100) * 100; // Round up to nearest 100 with 20% padding

    // Make sure the target is visible if it's higher than any data point
    if (calorieTarget > maxCalorieValue) {
      yAxisMax = Math.max(yAxisMax, Math.ceil(calorieTarget * 1.2 / 100) * 100);
    }
  } else {
    // If no data, use the target with padding
    yAxisMax = Math.ceil(calorieTarget * 1.2 / 100) * 100;
  }

  // Ensure we have a reasonable minimum value
  yAxisMax = Math.max(yAxisMax, 1000); // At least 1000 calories

  console.log(`Chart scaling: Max calorie value: ${maxCalorieValue}, Target: ${calorieTarget}, Y-axis max: ${yAxisMax}`);

  // Calculate Y-axis tick values based on the maximum value
  // Create evenly spaced tick marks for better readability
  // For values over 3000, use 6 ticks; for smaller values, use 5 ticks
  let yAxisTicks;
  if (yAxisMax > 3000) {
    yAxisTicks = [
      0,
      Math.round(yAxisMax * 0.2),
      Math.round(yAxisMax * 0.4),
      Math.round(yAxisMax * 0.6),
      Math.round(yAxisMax * 0.8),
      yAxisMax
    ];
  } else {
    // For smaller values, use 4 ticks for cleaner display
    yAxisTicks = [
      0,
      Math.round(yAxisMax * 0.33),
      Math.round(yAxisMax * 0.67),
      yAxisMax
    ];
  }

  // Define colors for macronutrients
  const colors = {
    protein: '#3b82f6', // blue
    carbs: '#10b981',   // green
    fats: '#fbbf24'     // yellow/amber
  };

  return (
    <div className={cn("w-full", className)}>

      {/* No data message */}
      {!hasData && (
        <div className="text-center py-4 bg-gray-50 rounded-xl mb-4">
          <p className="text-gray-500">No nutrition data available for this week</p>
        </div>
      )}

      {/* Chart container with bars */}
      <div className="flex flex-col h-72"> {/* Increased height to accommodate labels below x-axis */}
        <div className="flex flex-1">
          {/* Bars container */}
          <div className="w-full relative">
            <div className="w-full flex items-end justify-between relative h-64"> {/* Fixed height for bar area */}
              {/* X-axis line */}
              <div className="absolute bottom-0 left-0 right-0 h-px bg-gray-500" style={{ zIndex: 6 }}></div>

              {/* Day bars */}
              {labels.map((label, index) => {
                // Get the calorie values for this day
                const totalCalories = dailyData[index] || 0;

                // Calculate height percentage based on the maximum Y-axis value
                const barHeight = (totalCalories / yAxisMax) * 100;

                // Get macro calories for this day (already in calorie values)
                let dayProteinCals = proteinCals[index] || 0;
                let dayCarbsCals = carbsCals[index] || 0;
                let dayFatsCals = fatsCals[index] || 0;

                // Log the actual values for debugging
                console.log(`Bar ${index} (${label}): Total calories: ${totalCalories}, Protein: ${dayProteinCals}, Carbs: ${dayCarbsCals}, Fats: ${dayFatsCals}`);

                // Debug the bar height calculation
                if (totalCalories > 0) {
                  console.log(`Bar ${index} height calculation: ${totalCalories} / ${yAxisMax} * 100 = ${barHeight}%`);
                }

                // Verify that the sum of macronutrient calories is close to the total calories
                const macroSum = dayProteinCals + dayCarbsCals + dayFatsCals;
                console.log(`Bar ${index}: Sum of macro calories: ${macroSum}, Total calories: ${totalCalories}, Difference: ${totalCalories - macroSum}`);

                // If there's a significant difference between macro sum and total calories,
                // adjust the macro values to match the total calories
                if (totalCalories > 0 && Math.abs(macroSum - totalCalories) > 10) {
                  console.log(`Bar ${index}: Adjusting macro values to match total calories`);

                  // Use the proportions from the original macro values
                  const totalMacroCalories = macroSum || 1; // Avoid division by zero
                  const proteinRatio = dayProteinCals / totalMacroCalories;
                  const carbsRatio = dayCarbsCals / totalMacroCalories;
                  const fatsRatio = dayFatsCals / totalMacroCalories;

                  // Adjust the macro values to match the total calories
                  dayProteinCals = Math.round(totalCalories * proteinRatio);
                  dayCarbsCals = Math.round(totalCalories * carbsRatio);
                  dayFatsCals = Math.round(totalCalories * fatsRatio);

                  console.log(`Bar ${index}: Adjusted macros - Protein: ${dayProteinCals}, Carbs: ${dayCarbsCals}, Fats: ${dayFatsCals}, New Sum: ${dayProteinCals + dayCarbsCals + dayFatsCals}`);
                }

                // Calculate absolute height percentages for each macronutrient based on yAxisMax
                const proteinHeight = (dayProteinCals / yAxisMax) * 100;
                const carbsHeight = (dayCarbsCals / yAxisMax) * 100;
                const fatsHeight = (dayFatsCals / yAxisMax) * 100;

                // Calculate percentages of total calories for each macronutrient (for tooltips)
                const proteinPercent = totalCalories > 0 ? dayProteinCals / totalCalories : 0;
                const carbsPercent = totalCalories > 0 ? dayCarbsCals / totalCalories : 0;
                const fatsPercent = totalCalories > 0 ? dayFatsCals / totalCalories : 0;

                return (
                  <div key={index} className="flex flex-col items-center w-full mx-1">
                    {/* Bar container - aligned with the chart height */}
                    <div className="flex flex-col justify-end items-center h-56 w-8 relative pb-1"> {/* Fixed height matches chart area, increased width */}
                      {totalCalories > 0 ? (
                        <div
                          className="w-full relative group flex flex-col justify-end"
                          style={{ height: `${barHeight}%`, minHeight: '12px', transition: 'height 0.3s' }}
                          title={`${totalCalories} calories (${Math.round(barHeight)}% of chart height)`}
                        >
                          {/* Enhanced tooltip that appears on hover */}
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-20">
                            <div className="font-bold">{totalCalories} calories</div>
                            {dayProteinCals > 0 && (
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-sm mr-1" style={{ backgroundColor: colors.protein }}></div>
                                <span>Protein: {dayProteinCals} cal ({Math.round(proteinPercent * 100)}%)</span>
                              </div>
                            )}
                            {dayCarbsCals > 0 && (
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-sm mr-1" style={{ backgroundColor: colors.carbs }}></div>
                                <span>Carbs: {dayCarbsCals} cal ({Math.round(carbsPercent * 100)}%)</span>
                              </div>
                            )}
                            {dayFatsCals > 0 && (
                              <div className="flex items-center">
                                <div className="w-2 h-2 rounded-sm mr-1" style={{ backgroundColor: colors.fats }}></div>
                                <span>Fats: {dayFatsCals} cal ({Math.round(fatsPercent * 100)}%)</span>
                              </div>
                            )}
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-black"></div>
                          </div>
                          {/* Stack macronutrient segments from bottom to top */}
                          <div className="flex flex-col w-full h-full rounded-t-md overflow-hidden">
                            {/* Protein at the bottom */}
                            {dayProteinCals > 0 && (
                              <div
                                className="w-full"
                                style={{
                                  height: `${proteinHeight / barHeight * 100}%`,
                                  backgroundColor: colors.protein,
                                  minHeight: proteinHeight > 0 ? '4px' : '0'
                                }}
                                title={`Protein: ${dayProteinCals} calories (${Math.round(proteinPercent * 100)}% of total)`}
                              ></div>
                            )}
                            {/* Carbs in the middle */}
                            {dayCarbsCals > 0 && (
                              <div
                                className="w-full"
                                style={{
                                  height: `${carbsHeight / barHeight * 100}%`,
                                  backgroundColor: colors.carbs,
                                  minHeight: carbsHeight > 0 ? '4px' : '0'
                                }}
                                title={`Carbs: ${dayCarbsCals} calories (${Math.round(carbsPercent * 100)}% of total)`}
                              ></div>
                            )}
                            {/* Fats at the top */}
                            {dayFatsCals > 0 && (
                              <div
                                className="w-full"
                                style={{
                                  height: `${fatsHeight / barHeight * 100}%`,
                                  backgroundColor: colors.fats,
                                  minHeight: fatsHeight > 0 ? '4px' : '0'
                                }}
                                title={`Fats: ${dayFatsCals} calories (${Math.round(fatsPercent * 100)}% of total)`}
                              ></div>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="w-full h-4 bg-gray-100 rounded-md mb-1"></div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* X-axis labels - now positioned below the chart */}
        <div className="flex w-full mt-1">
          <div className="w-full flex justify-between">
            {labels.map((label, index) => {
              const totalCalories = dailyData[index] || 0;
              return (
                <div key={`x-label-${index}`} className="flex flex-col items-center w-8">
                  <span className="text-xs font-medium">{label}</span>
                  <span className="text-xs text-gray-600 font-medium">
                    {totalCalories > 0 ? totalCalories : '-'}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NutritionBarChart;
