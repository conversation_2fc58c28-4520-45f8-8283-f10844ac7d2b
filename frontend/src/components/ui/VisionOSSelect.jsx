import React, { useState, useRef, useEffect } from 'react';

/**
 * Vision OS style select component with custom dropdown
 * 
 * @param {string} value - Current selected value
 * @param {function} onChange - Function to call when selection changes
 * @param {Array} options - Array of option objects {value, label}
 * @param {string} className - Additional CSS classes
 * @param {boolean} disabled - Whether the select is disabled
 * @param {string} placeholder - Placeholder text
 */
const VisionOSSelect = ({ 
  value = '', 
  onChange, 
  options = [],
  className = '', 
  disabled = false,
  placeholder = 'Select option'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value);
  const selectRef = useRef(null);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update selected value when prop changes
  useEffect(() => {
    setSelectedValue(value);
  }, [value]);

  // Get display text for selected value
  const getDisplayText = () => {
    if (!selectedValue) return placeholder;
    const option = options.find(opt => opt.value === selectedValue);
    return option ? option.label : selectedValue;
  };

  // Handle option selection
  const handleOptionSelect = (optionValue) => {
    setSelectedValue(optionValue);
    if (onChange) {
      onChange({ target: { value: optionValue } });
    }
    setIsOpen(false);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (disabled) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // Navigate to next option
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const nextIndex = Math.min(currentIndex + 1, options.length - 1);
          if (nextIndex !== currentIndex) {
            handleOptionSelect(options[nextIndex].value);
          }
        }
        break;
      case 'ArrowUp':
        e.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // Navigate to previous option
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const prevIndex = Math.max(currentIndex - 1, 0);
          if (prevIndex !== currentIndex) {
            handleOptionSelect(options[prevIndex].value);
          }
        }
        break;
    }
  };

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      {/* Select Button */}
      <button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onKeyDown={handleKeyDown}
        disabled={disabled}
        className={`
          visionos-select w-full text-left flex items-center justify-between
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isOpen ? 'ring-2 ring-blue-500 ring-opacity-30' : ''}
        `}
      >
        <span className={selectedValue ? 'text-gray-900' : 'text-gray-500'}>
          {getDisplayText()}
        </span>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div 
          ref={dropdownRef}
          className="absolute top-full left-0 right-0 mt-2 z-50"
        >
          <div className="visionos-card py-2 shadow-2xl max-h-60 overflow-y-auto">
            {options.map((option, index) => (
              <button
                key={option.value || index}
                onClick={() => handleOptionSelect(option.value)}
                className={`
                  w-full text-left px-4 py-3 transition-all duration-200
                  hover:bg-blue-50 hover:text-blue-600
                  ${selectedValue === option.value 
                    ? 'bg-blue-100 text-blue-600 font-medium' 
                    : 'text-gray-900'
                  }
                  ${index === 0 ? 'rounded-t-lg' : ''}
                  ${index === options.length - 1 ? 'rounded-b-lg' : ''}
                `}
              >
                <div className="flex items-center justify-between">
                  <span>{option.label}</span>
                  {selectedValue === option.value && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
              </button>
            ))}
            
            {options.length === 0 && (
              <div className="px-4 py-3 text-gray-500 text-center">
                No options available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VisionOSSelect;
