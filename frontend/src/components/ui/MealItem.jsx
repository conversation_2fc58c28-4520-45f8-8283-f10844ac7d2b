import React, { useState, useRef } from 'react';
import { cn } from '../../lib/utils';
import { ProteinIcon, CarbsIcon, FatsIcon } from '../../assets/icons/DashboardIcons';

// Helper function to ensure image URLs are absolute
const getAbsoluteImageUrl = (imageUrl) => {
  if (!imageUrl) return null;

  // If it's already an absolute URL, return it as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a relative URL starting with /uploads, make it absolute
  if (imageUrl.startsWith('/uploads')) {
    // Use the current host with the correct port
    const host = window.location.hostname;
    const port = window.location.port || '86'; // Default to port 86 if not specified
    return `http://${host}:${port}${imageUrl}`;
  }

  // Return the original URL if it doesn't match any of the above conditions
  return imageUrl;
};

/**
 * MealItem component for displaying meal information
 *
 * @param {string} name - Name of the meal
 * @param {number} calories - Calories in the meal
 * @param {string} time - Time the meal was consumed
 * @param {Object} macros - Macronutrient information (protein, carbs, fat)
 * @param {Object} nutrition - Additional nutritional information
 * @param {string} image - URL of the meal image
 * @param {Array} items - Food breakdown items (name, calories, portion)
 * @param {Function} onDelete - Function to call when delete button is clicked
 * @param {boolean} expanded - Whether the meal item is expanded (only used for mobile)
 * @param {Function} onExpand - Function to call when the card is clicked (only used for mobile)
 * @param {boolean} isMobile - Whether the current viewport is mobile
 * @param {string} className - Additional CSS classes
 */
const MealItem = ({
  name,
  calories,
  time,
  macros = { protein: 0, carbs: 0, fat: 0 },
  nutrition = {},
  image,
  items = [],
  onDelete,
  expanded = false,
  onExpand,
  isMobile = false,
  className
}) => {
  // State for tracking active card in the carousel
  const [activeCard, setActiveCard] = useState(0); // 0 = Food Breakdown, 1 = Nutrition Breakdown
  const desktopScrollRef = useRef(null);
  const mobileScrollRef = useRef(null);

  // Only handle card click for mobile view
  const handleCardClick = () => {
    if (isMobile && onExpand) {
      onExpand();
    }
  };

  // Function to smoothly scroll to a specific card
  const scrollToCard = (cardIndex) => {
    const scrollContainer = isMobile && expanded ? mobileScrollRef.current : desktopScrollRef.current;
    if (!scrollContainer) return;

    const cards = scrollContainer.children;

    // Adjust card index if there are no food items (only nutrition card)
    let actualCardIndex = cardIndex;
    if (!items || items.length === 0) {
      // If no food items, only nutrition card exists, so index 0 = nutrition card
      actualCardIndex = 0;
    }

    if (actualCardIndex >= 0 && actualCardIndex < cards.length) {
      const targetCard = cards[actualCardIndex];
      const scrollLeft = targetCard.offsetLeft - scrollContainer.offsetLeft;

      scrollContainer.scrollTo({
        left: scrollLeft,
        behavior: 'smooth'
      });

      setActiveCard(cardIndex);
    }
  };

  // Handle card click navigation
  const handleCardNavigation = (cardIndex, event) => {
    event.stopPropagation(); // Prevent parent click handlers
    scrollToCard(cardIndex);
  };

  // Handle dot indicator click
  const handleDotClick = (cardIndex, event) => {
    event.stopPropagation(); // Prevent parent click handlers
    scrollToCard(cardIndex);
  };

  return (
    <div
      className={cn(
        "bg-white rounded-3xl shadow-sm transition-all duration-500 ease-in-out transform hover:shadow-md",
        isMobile ? (
          expanded ?
            // Mobile expanded: expand in place
            "p-4 h-auto shadow-lg pb-6 relative z-10 mb-4 mt-2 border border-gray-200 w-full cursor-pointer" :
            // Mobile non-expanded: original rectangular layout
            "p-4 h-32 relative w-full cursor-pointer"
        ) : (
          // Desktop: always show full details - wider card
          "p-6 relative w-full h-auto shadow-sm"
        ),
        className
      )}
      onClick={handleCardClick}
    >
      <div className={`${isMobile ? (expanded ? "flex flex-col" : "flex justify-between") : "flex flex-col"} h-full transition-all duration-500`}>
        {isMobile && expanded && (
          <div className="mb-3 animate-fadeIn" style={{ animationDelay: '100ms' }}>
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-bold text-gray-900">{name}</h2>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  if (onExpand) onExpand();
                }}
                className="text-gray-400 hover:text-gray-600"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* Desktop layout - always show full details */}
        {!isMobile && (
          <>
            {/* Header with name and time */}
            <div className="mb-3">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-bold text-gray-900">{name}</h2>
              </div>
            </div>

            {/* Image section */}
            <div className="w-full h-48 mb-4 rounded-2xl overflow-hidden relative">
              <img
                src={getAbsoluteImageUrl(image)}
                alt={name}
                className="h-full w-full object-cover transition-all duration-500"
                onError={(e) => {
                  e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
                }}
              />
              {/* Time stamp overlay - pill style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-xs px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {time}
              </div>
            </div>

            {/* Macro pills */}
            <div className="flex flex-wrap items-center gap-3 mb-3 justify-center">
              <div className="flex items-center bg-blue-100 px-3 py-1 rounded-full">
                <ProteinIcon className="w-5 h-5 text-protein-blue mr-1" />
                <span className="text-sm text-protein-blue font-medium">{parseFloat(macros.protein) || 0}g</span>
              </div>
              <div className="flex items-center bg-green-100 px-3 py-1 rounded-full">
                <CarbsIcon className="w-5 h-5 text-carbs-green mr-1" />
                <span className="text-sm text-carbs-green font-medium">{parseFloat(macros.carbs) || 0}g</span>
              </div>
              <div className="flex items-center bg-yellow-100 px-3 py-1 rounded-full">
                <FatsIcon className="w-5 h-5 text-fat-yellow mr-1" />
                <span className="text-sm text-fat-yellow font-medium">{parseFloat(macros.fat) || 0}g</span>
              </div>
            </div>

            {/* Calories and delete button */}
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center">
                <span className="text-4xl font-bold">{calories}</span>
                <span className="text-sm font-normal text-gray-500 ml-1">Kcal</span>
              </div>
              {onDelete && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDelete();
                  }}
                  className="text-gray-400 hover:text-red-500 text-sm px-3 py-1 bg-gray-50 rounded-lg"
                  aria-label="Delete meal"
                >
                  Delete
                </button>
              )}
            </div>

            {/* Marquee Cards Container */}
            <div className="pt-2 border-t border-gray-100">
              <div
                ref={desktopScrollRef}
                className="flex overflow-x-auto scrollbar-hide space-x-4 pb-2"
                style={{ scrollSnapType: 'x mandatory' }}
              >

                {/* Food Breakdown Card */}
                {items && items.length > 0 && (
                  <div
                    className="flex-shrink-0 w-full max-w-lg bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-4 border border-blue-200 cursor-pointer hover:shadow-md transition-shadow duration-200"
                    style={{ scrollSnapAlign: 'start' }}
                    onClick={(e) => handleCardNavigation(0, e)}
                  >
                    <div className="flex items-center mb-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </div>
                      <h4 className="text-sm font-semibold text-blue-900">Food Breakdown</h4>
                    </div>
                    <div className="space-y-2 max-h-48 overflow-y-auto scrollbar-hide">
                      {items.map((item, index) => (
                        <div key={index} className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-3 py-2 rounded-lg border border-blue-200">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-blue-900 truncate">{item.name}</p>
                            <p className="text-xs text-blue-600">{item.portion}</p>
                          </div>
                          <div className="ml-2 flex-shrink-0">
                            <span className="text-sm font-bold text-blue-900">{item.calories}</span>
                            <span className="text-xs text-blue-600 ml-1">cal</span>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-3 pt-2 border-t border-blue-200">
                      <div className="flex justify-between items-center text-xs text-blue-700">
                        <span>Total Items:</span>
                        <span className="font-semibold">{items.length}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Nutritional Information Card */}
                <div
                  className="flex-shrink-0 w-full max-w-lg bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-4 border border-green-200 cursor-pointer hover:shadow-md transition-shadow duration-200"
                  style={{ scrollSnapAlign: 'start' }}
                  onClick={(e) => handleCardNavigation(1, e)}
                >
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <h4 className="text-sm font-semibold text-green-900">Nutritional Information</h4>
                  </div>
                  <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto scrollbar-hide">
                    {nutrition.fiber_g && parseFloat(nutrition.fiber_g) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Fiber:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.fiber_g}g</span>
                      </div>
                    )}
                    {nutrition.sugar_g && parseFloat(nutrition.sugar_g) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Sugar:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.sugar_g}g</span>
                      </div>
                    )}
                    {nutrition.sodium_mg && parseFloat(nutrition.sodium_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Sodium:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.sodium_mg}mg</span>
                      </div>
                    )}
                    {nutrition.cholesterol_mg && parseFloat(nutrition.cholesterol_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Chol:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.cholesterol_mg}mg</span>
                      </div>
                    )}
                    {nutrition.saturated_fat_g && parseFloat(nutrition.saturated_fat_g) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Sat Fat:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.saturated_fat_g}g</span>
                      </div>
                    )}
                    {nutrition.trans_fat_g && parseFloat(nutrition.trans_fat_g) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Trans Fat:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.trans_fat_g}g</span>
                      </div>
                    )}
                    {nutrition.vitamin_c_mg && parseFloat(nutrition.vitamin_c_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Vit C:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.vitamin_c_mg}mg</span>
                      </div>
                    )}
                    {nutrition.calcium_mg && parseFloat(nutrition.calcium_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Calcium:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.calcium_mg}mg</span>
                      </div>
                    )}
                    {nutrition.iron_mg && parseFloat(nutrition.iron_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Iron:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.iron_mg}mg</span>
                      </div>
                    )}
                    {nutrition.potassium_mg && parseFloat(nutrition.potassium_mg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Potassium:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.potassium_mg}mg</span>
                      </div>
                    )}
                    {nutrition.vitamin_d_mcg && parseFloat(nutrition.vitamin_d_mcg) > 0 && (
                      <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-green-200">
                        <span className="text-xs text-green-700 font-medium">Vit D:</span>
                        <span className="text-xs font-bold text-green-900">{nutrition.vitamin_d_mcg}mcg</span>
                      </div>
                    )}
                  </div>
                  <div className="mt-3 pt-2 border-t border-green-200">
                    <div className="flex justify-between items-center text-xs text-green-700">
                      <span>Nutrients:</span>
                      <span className="font-semibold">{Object.values(nutrition).filter(val => val && parseFloat(val) > 0).length}</span>
                    </div>
                  </div>
                </div>

              </div>

              {/* Scroll Indicator */}
              <div className="flex justify-center mt-2 space-x-1">
                {items && items.length > 0 && (
                  <button
                    className={`w-2 h-2 rounded-full transition-all duration-200 ${
                      activeCard === 0 ? 'bg-blue-600 scale-110' : 'bg-blue-300 hover:bg-blue-400'
                    }`}
                    onClick={(e) => handleDotClick(0, e)}
                    aria-label="Navigate to Food Breakdown"
                  />
                )}
                <button
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    activeCard === 1 ? 'bg-green-600 scale-110' : 'bg-green-300 hover:bg-green-400'
                  }`}
                  onClick={(e) => handleDotClick(1, e)}
                  aria-label="Navigate to Nutrition Breakdown"
                />
              </div>
            </div>
          </>
        )}

        {/* Mobile layout (original) */}
        {!expanded && isMobile && (
          <div className="flex justify-between w-full transition-all duration-300 ease-in-out">
            <div className={`flex flex-col flex-1 mr-4 min-w-0 transition-all duration-300`}>
              <div className="h-10 mb-1">
                <h3 className={`font-medium line-clamp-2 transition-all duration-300 ${name.length > 20 ? 'text-xs' : name.length > 15 ? 'text-sm' : 'text-base'}`}>{name}</h3>
              </div>

              <div className="flex items-center gap-2 mb-2 transition-all duration-300">
                <div className="flex items-center bg-blue-100 px-2 py-1 rounded-full">
                  <ProteinIcon className="w-3 h-3 text-protein-blue mr-1" />
                  <span className="text-xs text-protein-blue font-medium">{parseFloat(macros.protein) || 0}g</span>
                </div>
                <div className="flex items-center bg-green-100 px-2 py-1 rounded-full">
                  <CarbsIcon className="w-3 h-3 text-carbs-green mr-1" />
                  <span className="text-xs text-carbs-green font-medium">{parseFloat(macros.carbs) || 0}g</span>
                </div>
                <div className="flex items-center bg-yellow-100 px-2 py-1 rounded-full">
                  <FatsIcon className="w-3 h-3 text-fat-yellow mr-1" />
                  <span className="text-xs text-fat-yellow font-medium">{parseFloat(macros.fat) || 0}g</span>
                </div>
              </div>

              <div className="mt-auto transition-opacity duration-300">
                <span className="text-3xl sm:text-4xl font-bold">{calories}</span>
                <span className="text-sm font-normal text-gray-500 ml-1">Kcal</span>
              </div>
            </div>

            <div className="h-28 w-28 sm:h-32 sm:w-32 rounded-2xl overflow-hidden relative flex-shrink-0 transition-all duration-500 ease-in-out">
              <img
                src={getAbsoluteImageUrl(image)}
                alt={name}
                className="h-full w-full object-cover transition-all duration-500"
                onError={(e) => {
                  console.error('Image failed to load:', image);
                  e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
                }}
              />
              {/* Time stamp overlay - pill style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-[10px] px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {time}
              </div>
            </div>
          </div>
        )}

        {/* Mobile expanded view */}
        {expanded && isMobile && (
          <div>
            <div className="w-full h-48 mb-4 max-w-md mx-auto rounded-2xl overflow-hidden relative">
              <img
                src={getAbsoluteImageUrl(image)}
                alt={name}
                className="h-full w-full object-cover transition-all duration-500"
                onError={(e) => {
                  e.target.src = 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
                }}
              />
              {/* Time stamp overlay - pill style */}
              <div className="absolute bottom-3 right-2 bg-white bg-opacity-80 text-[10px] px-2 py-0.5 rounded-full text-gray-700 font-medium">
                {time}
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-3 mb-3 justify-center transition-all duration-300 animate-fadeIn" style={{ animationDelay: '150ms' }}>
              <div className="flex items-center bg-blue-100 px-3 py-1 rounded-full">
                <ProteinIcon className="w-5 h-5 text-protein-blue mr-1" />
                <span className="text-sm text-protein-blue font-medium">{parseFloat(macros.protein) || 0}g</span>
              </div>
              <div className="flex items-center bg-green-100 px-3 py-1 rounded-full">
                <CarbsIcon className="w-5 h-5 text-carbs-green mr-1" />
                <span className="text-sm text-carbs-green font-medium">{parseFloat(macros.carbs) || 0}g</span>
              </div>
              <div className="flex items-center bg-yellow-100 px-3 py-1 rounded-full">
                <FatsIcon className="w-5 h-5 text-fat-yellow mr-1" />
                <span className="text-sm text-fat-yellow font-medium">{parseFloat(macros.fat) || 0}g</span>
              </div>
            </div>

            <div className="mt-2 animate-fadeIn transition-all duration-500 ease-in-out" style={{ animationDelay: '200ms' }}>
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center">
                  <span className="text-4xl font-bold">{calories}</span>
                  <span className="text-sm font-normal text-gray-500 ml-1">Kcal</span>
                </div>
                {onDelete && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent card click event
                      onDelete();
                    }}
                    className="text-gray-400 hover:text-red-500 text-sm px-3 py-1 bg-gray-50 rounded-lg"
                    aria-label="Delete meal"
                  >
                    Delete
                  </button>
                )}
              </div>

              {/* Mobile Marquee Cards Container */}
              <div className="pt-2 border-t border-gray-100 animate-fadeIn" style={{ animationDelay: '250ms' }}>
                <div
                  ref={mobileScrollRef}
                  className="flex overflow-x-auto scrollbar-hide space-x-3 pb-2"
                  style={{ scrollSnapType: 'x mandatory' }}
                >

                  {/* Food Breakdown Card - Mobile */}
                  {items && items.length > 0 && (
                    <div
                      className="flex-shrink-0 w-72 bg-gradient-to-br from-blue-50 to-blue-100 rounded-3xl p-3 border border-blue-200 cursor-pointer hover:shadow-md transition-shadow duration-200"
                      style={{ scrollSnapAlign: 'start' }}
                      onClick={(e) => handleCardNavigation(0, e)}
                    >
                      <div className="flex items-center mb-2">
                        <div className="w-6 h-6 bg-blue-500 rounded-lg flex items-center justify-center mr-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                          </svg>
                        </div>
                        <h4 className="text-xs font-semibold text-blue-900">Food Breakdown</h4>
                      </div>
                      <div className="space-y-1.5 max-h-32 overflow-y-auto scrollbar-hide">
                        {items.map((item, index) => (
                          <div key={index} className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-2 py-1.5 rounded-lg border border-blue-200">
                            <div className="flex-1 min-w-0">
                              <p className="text-xs font-medium text-blue-900 truncate">{item.name}</p>
                              <p className="text-[10px] text-blue-600">{item.portion}</p>
                            </div>
                            <div className="ml-1 flex-shrink-0">
                              <span className="text-xs font-bold text-blue-900">{item.calories}</span>
                              <span className="text-[10px] text-blue-600 ml-0.5">cal</span>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="mt-2 pt-1.5 border-t border-blue-200">
                        <div className="flex justify-between items-center text-[10px] text-blue-700">
                          <span>Items:</span>
                          <span className="font-semibold">{items.length}</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Nutritional Information Card - Mobile */}
                  <div
                    className="flex-shrink-0 w-72 bg-gradient-to-br from-green-50 to-green-100 rounded-3xl p-3 border border-green-200 cursor-pointer hover:shadow-md transition-shadow duration-200"
                    style={{ scrollSnapAlign: 'start' }}
                    onClick={(e) => handleCardNavigation(1, e)}
                  >
                    <div className="flex items-center mb-2">
                      <div className="w-6 h-6 bg-green-500 rounded-lg flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                      </div>
                      <h4 className="text-xs font-semibold text-green-900">Nutritional Info</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-1.5 max-h-32 overflow-y-auto scrollbar-hide">
                      {nutrition.fiber_g && parseFloat(nutrition.fiber_g) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Fiber:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.fiber_g}g</span>
                        </div>
                      )}
                      {nutrition.sugar_g && parseFloat(nutrition.sugar_g) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Sugar:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.sugar_g}g</span>
                        </div>
                      )}
                      {nutrition.sodium_mg && parseFloat(nutrition.sodium_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Sodium:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.sodium_mg}mg</span>
                        </div>
                      )}
                      {nutrition.cholesterol_mg && parseFloat(nutrition.cholesterol_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Chol:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.cholesterol_mg}mg</span>
                        </div>
                      )}
                      {nutrition.saturated_fat_g && parseFloat(nutrition.saturated_fat_g) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Sat Fat:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.saturated_fat_g}g</span>
                        </div>
                      )}
                      {nutrition.trans_fat_g && parseFloat(nutrition.trans_fat_g) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Trans Fat:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.trans_fat_g}g</span>
                        </div>
                      )}
                      {nutrition.vitamin_c_mg && parseFloat(nutrition.vitamin_c_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Vit C:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.vitamin_c_mg}mg</span>
                        </div>
                      )}
                      {nutrition.calcium_mg && parseFloat(nutrition.calcium_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Calcium:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.calcium_mg}mg</span>
                        </div>
                      )}
                      {nutrition.iron_mg && parseFloat(nutrition.iron_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Iron:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.iron_mg}mg</span>
                        </div>
                      )}
                      {nutrition.potassium_mg && parseFloat(nutrition.potassium_mg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Potassium:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.potassium_mg}mg</span>
                        </div>
                      )}
                      {nutrition.vitamin_d_mcg && parseFloat(nutrition.vitamin_d_mcg) > 0 && (
                        <div className="flex justify-between items-center bg-white bg-opacity-70 backdrop-blur-sm px-1.5 py-1 rounded border border-green-200">
                          <span className="text-[10px] text-green-700 font-medium">Vit D:</span>
                          <span className="text-[10px] font-bold text-green-900">{nutrition.vitamin_d_mcg}mcg</span>
                        </div>
                      )}
                    </div>
                    <div className="mt-2 pt-1.5 border-t border-green-200">
                      <div className="flex justify-between items-center text-[10px] text-green-700">
                        <span>Nutrients:</span>
                        <span className="font-semibold">{Object.values(nutrition).filter(val => val && parseFloat(val) > 0).length}</span>
                      </div>
                    </div>
                  </div>

                </div>

                {/* Mobile Scroll Indicator */}
                <div className="flex justify-center mt-2 space-x-1">
                  {items && items.length > 0 && (
                    <button
                      className={`w-1.5 h-1.5 rounded-full transition-all duration-200 ${
                        activeCard === 0 ? 'bg-blue-600 scale-110' : 'bg-blue-300 hover:bg-blue-400'
                      }`}
                      onClick={(e) => handleDotClick(0, e)}
                      aria-label="Navigate to Food Breakdown"
                    />
                  )}
                  <button
                    className={`w-1.5 h-1.5 rounded-full transition-all duration-200 ${
                      activeCard === 1 ? 'bg-green-600 scale-110' : 'bg-green-300 hover:bg-green-400'
                    }`}
                    onClick={(e) => handleDotClick(1, e)}
                    aria-label="Navigate to Nutrition Breakdown"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MealItem;
