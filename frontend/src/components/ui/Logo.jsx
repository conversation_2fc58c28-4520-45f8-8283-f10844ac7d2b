import { useTheme } from '../../context/ThemeContext';
import lightLogo from '../../assets/logos/logo.svg';
import darkLogo from '../../assets/logos/logo-for-darkmode.svg';

/**
 * Logo component that displays the appropriate logo based on the current theme
 *
 * @param {Object} props - Component props
 * @param {number} props.width - Width of the logo in pixels
 * @param {number} props.height - Height of the logo in pixels
 * @param {string} props.className - Additional CSS classes
 * @returns {JSX.Element} - Logo component
 */
const Logo = ({ width = 40, height = 40, className = '' }) => {
  const { isDarkMode } = useTheme();

  // Use the appropriate logo based on the theme
  const logoSrc = isDarkMode ? darkLogo : lightLogo;

  return (
    <img
      src={logoSrc}
      alt="Calcounta Logo"
      width={width}
      height={height}
      className={className}
    />
  );
};

export default Logo;
