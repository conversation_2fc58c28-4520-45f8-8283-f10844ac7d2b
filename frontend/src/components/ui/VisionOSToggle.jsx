import React from 'react';

/**
 * iOS style toggle switch component that matches native iOS switches
 *
 * @param {boolean} checked - Whether the toggle is checked
 * @param {function} onChange - Function to call when toggle changes
 * @param {string} className - Additional CSS classes
 * @param {boolean} disabled - Whether the toggle is disabled
 */
const VisionOSToggle = ({ checked = false, onChange, className = '', disabled = false }) => {
  return (
    <label className={`ios-toggle ${className}`}>
      <input
        type="checkbox"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
      <span className="ios-toggle-slider"></span>
    </label>
  );
};

export default VisionOSToggle;
