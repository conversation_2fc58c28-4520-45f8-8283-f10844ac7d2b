import React from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useLoading } from '../../context/LoadingContext';

/**
 * GlobalLoadingIndicator component with simple three pulsating dots
 * in brand colors: blue, green, yellow
 */
const GlobalLoadingIndicator = () => {
  const { isDarkMode } = useTheme();
  const { isLoading } = useLoading();

  // Don't render if not loading
  if (!isLoading) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm transition-all duration-300 ease-in-out ${
        isDarkMode
          ? 'bg-black bg-opacity-80'
          : 'bg-white bg-opacity-90'
      }`}
      style={{
        animation: 'fadeIn 0.3s ease-in-out'
      }}
    >
      {/* Three pulsating dots */}
      <div className="flex space-x-3">
        <div
          className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"
          style={{ animationDelay: '0ms', animationDuration: '1.5s' }}
        />
        <div
          className="w-4 h-4 bg-green-500 rounded-full animate-pulse"
          style={{ animationDelay: '200ms', animationDuration: '1.5s' }}
        />
        <div
          className="w-4 h-4 bg-yellow-500 rounded-full animate-pulse"
          style={{ animationDelay: '400ms', animationDuration: '1.5s' }}
        />
      </div>

      {/* CSS animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  );
};

export default GlobalLoadingIndicator;
