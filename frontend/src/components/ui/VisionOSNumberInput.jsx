import React, { useState, useRef } from 'react';

/**
 * Vision OS style number input component with increment/decrement buttons
 *
 * @param {number|string} value - Current value
 * @param {function} onChange - Function to call when value changes
 * @param {string} className - Additional CSS classes
 * @param {boolean} disabled - Whether the input is disabled
 * @param {string} placeholder - Placeholder text
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @param {number} step - Step increment
 * @param {string} unit - Unit to display (e.g., 'kg', 'cm')
 */
const VisionOSNumberInput = ({
  value = '',
  onChange,
  className = '',
  disabled = false,
  placeholder = '0',
  min,
  max,
  step = 1,
  unit = ''
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef(null);

  // Handle value change
  const handleChange = (newValue) => {
    let numValue = parseFloat(newValue);

    // Apply min/max constraints
    if (min !== undefined && numValue < min) numValue = min;
    if (max !== undefined && numValue > max) numValue = max;

    if (onChange) {
      onChange({ target: { value: isNaN(numValue) ? '' : numValue } });
    }
  };

  // Handle increment
  const handleIncrement = () => {
    const currentValue = parseFloat(value) || 0;
    const newValue = currentValue + step;
    handleChange(newValue);
  };

  // Handle decrement
  const handleDecrement = () => {
    const currentValue = parseFloat(value) || 0;
    const newValue = currentValue - step;
    handleChange(newValue);
  };

  // Handle input change
  const handleInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue === '' || !isNaN(inputValue)) {
      handleChange(inputValue);
    }
  };

  // Handle key press for increment/decrement
  const handleKeyDown = (e) => {
    if (e.key === 'ArrowUp') {
      e.preventDefault();
      handleIncrement();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      handleDecrement();
    }
  };

  return (
    <div className={`relative flex items-center ${className}`}>
      {/* Decrement Button */}
      <button
        type="button"
        onClick={handleDecrement}
        disabled={disabled || (min !== undefined && parseFloat(value) <= min)}
        className={`
          visionos-button p-1.5 mr-2 rounded-full w-8 h-8 flex items-center justify-center
          ${disabled || (min !== undefined && parseFloat(value) <= min)
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-opacity-20 active:scale-95'
          }
        `}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
        </svg>
      </button>

      {/* Circular Number Input Container */}
      <div className="relative">
        <input
          ref={inputRef}
          type="number"
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          placeholder={placeholder}
          min={min}
          max={max}
          step={step}
          className={`
            visionos-number-input
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            ${isFocused ? 'ring-2 ring-black/50 ring-opacity-50' : ''}
          `}
          style={{
            paddingTop: unit ? '8px' : '12px',
            paddingBottom: unit ? '16px' : '12px'
          }}
        />
        {unit && (
          <span
            className="absolute bottom-2 left-1/2 transform -translate-x-1/2 text-xs font-medium text-black/50 pointer-events-none"
            style={{
              textShadow: '0 1px 3px rgba(255, 255, 255, 0.5)',
              fontSize: '10px'
            }}
          >
            {unit}
          </span>
        )}
      </div>

      {/* Increment Button */}
      <button
        type="button"
        onClick={handleIncrement}
        disabled={disabled || (max !== undefined && parseFloat(value) >= max)}
        className={`
          visionos-button p-1.5 ml-2 rounded-full w-8 h-8 flex items-center justify-center
          ${disabled || (max !== undefined && parseFloat(value) >= max)
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-opacity-20 active:scale-95'
          }
        `}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      </button>
    </div>
  );
};

export default VisionOSNumberInput;
