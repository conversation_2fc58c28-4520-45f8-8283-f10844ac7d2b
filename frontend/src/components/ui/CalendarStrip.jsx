import React, { useState, useEffect } from 'react';
import {
  format,
  startOfToday,
  addDays,
  subDays,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameMonth,
  isSameDay,
  addMonths,
  subMonths,
  getDay
} from 'date-fns';
import { useIsMobile } from '../../hooks/useIsMobile';
import { cn } from '../../lib/utils';
import DateCircleProgress from './DateCircleProgress';
import CircularProgress from './CircularProgress';
import { formatLocalDateToISO, logDateInfo } from '../../utils/dateUtils';

/**
 * CalendarWithTodo component for displaying a calendar with daily goal summary
 * Combines calendar functionality with daily calorie tracking
 *
 * @param {Function} onDateSelect - Function to call when a date is selected
 * @param {string} className - Additional CSS classes
 * @param {Array} previousDaysData - Array of objects containing date and calorie data
 *                                  [{date: '2023-05-01', totalCalories: 1500, hasData: true}, ...]
 * @param {number} calorieGoal - Daily calorie goal for calculating percentage
 * @param {number} consumedCalories - Calories consumed for selected date
 * @param {number} remainingCalories - Remaining calories for selected date
 * @param {Date} selectedDate - Currently selected date
 */
const CalendarWithTodo = ({
  onDateSelect,
  className,
  previousDaysData = [],
  calorieGoal = 2000,
  consumedCalories = 0,
  remainingCalories = 0,
  selectedDate
}) => {
  const isMobile = useIsMobile();
  const today = startOfToday();
  const [internalSelectedDate, setInternalSelectedDate] = useState(selectedDate || today);
  const [weekDates, setWeekDates] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(selectedDate || today);
  const [monthDays, setMonthDays] = useState([]);

  useEffect(() => {
    // Log current date information for debugging
    logDateInfo(today, 'CalendarStrip Initialization');

    // Generate array of dates for the week view
    const dates = [];
    for (let i = -3; i <= 3; i++) {
      dates.push(i === 0 ? today : (i < 0 ? subDays(today, Math.abs(i)) : addDays(today, i)));
    }
    setWeekDates(dates);

    // Generate array of dates for the month view
    updateMonthDays(today);
  }, []);

  // Debug: Log previousDaysData when it changes
  useEffect(() => {
    if (previousDaysData && previousDaysData.length > 0) {
      console.log('Calendar previousDaysData:', previousDaysData);
    }
  }, [previousDaysData]);

  const updateMonthDays = (date) => {
    const monthStart = startOfMonth(date);
    const monthEnd = endOfMonth(date);
    const daysInMonth = eachDayOfInterval({ start: monthStart, end: monthEnd });

    // Get the day of the week for the first day of the month (0 = Sunday, 1 = Monday, etc.)
    const startDay = getDay(monthStart);

    // Add empty days at the beginning to align with the correct day of the week
    const emptyDays = Array(startDay).fill(null);

    setMonthDays([...emptyDays, ...daysInMonth]);
  };

  const handleDateSelect = (date) => {
    setInternalSelectedDate(date);
    if (onDateSelect) {
      onDateSelect(date);
    }
  };

  const handlePrevMonth = () => {
    const prevMonth = subMonths(currentMonth, 1);
    setCurrentMonth(prevMonth);
    updateMonthDays(prevMonth);
  };

  const handleNextMonth = () => {
    const nextMonth = addMonths(currentMonth, 1);
    setCurrentMonth(nextMonth);
    updateMonthDays(nextMonth);
  };

  // Check if a date has calorie data
  const hasCalorieData = (date) => {
    if (!previousDaysData || previousDaysData.length === 0) return false;

    // Use our utility to ensure correct local date format
    const localDate = new Date(date);
    const dateString = formatLocalDateToISO(localDate).split('T')[0];
    const dayData = previousDaysData.find(day => day.date === dateString);

    // Only return true if we have actual data for this date
    return dayData && dayData.totalCalories > 0;
  };

  // Check if a date is in the future
  const isFutureDate = (date) => {
    const today = startOfToday();
    return date > today;
  };

  // Calculate time difference for events
  const getEventTimeLabel = (eventDate) => {
    const today = startOfToday();
    const eventDateObj = new Date(eventDate);
    const diffInDays = Math.ceil((eventDateObj - today) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today!';
    if (diffInDays === 1) return 'Tomorrow';
    if (diffInDays > 0) return `${diffInDays}d`;
    return `${Math.abs(diffInDays)}d ago`;
  };

  // Get day number from date string
  const getDayFromDate = (dateString) => {
    return new Date(dateString).getDate();
  };

  // Get calorie percentage for a date (consumed / goal)
  const getCaloriePercentage = (date) => {
    if (!previousDaysData || previousDaysData.length === 0) return 0;

    // Use our utility to ensure correct local date format
    const localDate = new Date(date);
    const dateString = formatLocalDateToISO(localDate).split('T')[0];
    const dayData = previousDaysData.find(day => day.date === dateString);

    if (!dayData || !dayData.totalCalories) return 0;

    const percentage = Math.min(100, Math.round((dayData.totalCalories / calorieGoal) * 100));

    // Debug log for a past date to see what's happening
    if (date < startOfToday() && dayData) {
      console.log(`Date: ${dateString}, Calories: ${dayData.totalCalories}, Goal: ${calorieGoal}, Percentage: ${percentage}%`);
    }

    return percentage;
  };

  // Get border color based on calorie percentage
  const getBorderColorClass = (date) => {
    const percentage = getCaloriePercentage(date);

    if (percentage === 0) return ""; // No data
    if (percentage < 25) return "border-red-500 bg-red-100"; // Below 25% - reddish (more visible)
    if (percentage < 75) return "border-yellow-500 bg-yellow-100"; // Below 75% - yellowish
    if (percentage < 100) return "border-green-500 bg-green-100"; // Below 100% - greenish
    return "border-blue-500 bg-blue-100"; // 100% or more - bluish
  };

  // Daily Summary component - compact horizontal layout
  const DailySummary = () => {
    const currentSelectedDate = selectedDate || internalSelectedDate;
    const isToday = isSameDay(currentSelectedDate, today);

    return (
      <div className="flex flex-col h-full">
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-700 mb-1">
            {format(currentSelectedDate, 'EEEE, MMMM d, yyyy')}
          </h3>
          <p className="text-gray-500 text-sm">
            {isToday ? 'Today' : format(currentSelectedDate, 'PPP')}
          </p>
        </div>

        {/* Calorie Summary - Without Ring */}
        <div className="flex-1 flex flex-col justify-center">
          <div className="text-center mb-4">
            <p className="text-gray-500 mb-2 text-sm">
              {isToday ? "Calories left" : `Calories left (${format(currentSelectedDate, 'MMM d')})`}
            </p>
            <div className="flex flex-col items-center mb-6">
              <span className="text-4xl font-bold">{remainingCalories}</span>
              <span className="text-gray-400 text-sm">Kcal</span>
            </div>
          </div>

          {/* Daily Goal Breakdown - Horizontal Layout */}
          <div className="grid grid-cols-3 gap-2 text-center p-3 bg-gray-50 rounded-xl">
            <div>
              <p className="text-xs text-gray-500 mb-1">Daily Goal</p>
              <p className="text-sm font-bold">{calorieGoal}</p>
              <p className="text-xs text-gray-400">Kcal</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-1">Consumed</p>
              <p className="text-sm font-bold">{consumedCalories}</p>
              <p className="text-xs text-gray-400">Kcal</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 mb-1">Remaining</p>
              <p className="text-sm font-bold">{remainingCalories}</p>
              <p className="text-xs text-gray-400">Kcal</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Weekly calendar strip for mobile - single card layout
  const WeeklyView = () => (
    <div className={`bg-white rounded-xl shadow-sm p-4 ${className || ''}`}>
      {/* Calendar Section */}
      <div className="flex justify-between mb-4">
        {weekDates.map((date, index) => {
          const isToday = isSameDay(date, today);
          const isSelected = isSameDay(date, selectedDate || internalSelectedDate);
          const dateHasData = hasCalorieData(date);
          const isFuture = isFutureDate(date);
          const isPast = date < today;
          const borderColorClass = isPast && dateHasData ? getBorderColorClass(date) : "";

          // Calculate percentage for the date
          const percentage = dateHasData ? getCaloriePercentage(date) : 0;

          return (
            <div
              key={index}
              className={cn(
                "flex flex-col items-center",
                isFuture ? "cursor-not-allowed" : "cursor-pointer"
              )}
              onClick={() => !isFuture && handleDateSelect(date)}
            >
              <span className="text-xs text-gray-500 mb-1">{format(date, 'E')[0]}</span>
              <DateCircleProgress
                percentage={percentage}
                dateText={format(date, 'd')}
                isSelected={isSelected}
                isToday={isToday}
                isDisabled={isFuture}
              />
            </div>
          );
        })}
      </div>

      {/* Daily Summary Section for Mobile */}
      <div className="border-t pt-4">
        <DailySummary />
      </div>
    </div>
  );

  // Monthly calendar for desktop - single card with 70/30 split
  const MonthlyView = () => (
    <div className={`bg-white rounded-xl shadow-sm p-6 ${className || ''}`}>
      <div className="flex gap-6 h-auto">
        {/* Calendar Section - 70% */}
        <div className="flex-[7]">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handlePrevMonth}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </button>
            <h2 className="text-lg font-medium">{format(currentMonth, 'MMMM yyyy')}</h2>
            <button
              onClick={handleNextMonth}
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <div className="grid grid-cols-7 gap-1 mb-2">
            {['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((day, index) => (
              <div key={index} className="text-center text-sm font-medium text-gray-500 py-1">
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {monthDays.map((date, index) => {
              if (!date) {
                return <div key={`empty-${index}`} className="h-8" />;
              }

              const isToday = isSameDay(date, today);
              const isSelected = isSameDay(date, selectedDate || internalSelectedDate);
              const isCurrentMonth = isSameMonth(date, currentMonth);
              const dateHasData = hasCalorieData(date);
              const isFuture = isFutureDate(date);
              const isPast = date < today;
              const borderColorClass = isPast && dateHasData && isCurrentMonth ? getBorderColorClass(date) : "";

              // Calculate percentage for the date
              const percentage = dateHasData && isCurrentMonth ? getCaloriePercentage(date) : 0;

              return (
                <div
                  key={index}
                  className="flex justify-center"
                >
                  <button
                    onClick={() => !isFuture && handleDateSelect(date)}
                    disabled={isFuture}
                    className="p-0 bg-transparent border-0"
                  >
                    <DateCircleProgress
                      percentage={percentage}
                      dateText={format(date, 'd')}
                      isSelected={isSelected}
                      isToday={isToday}
                      isDisabled={!isCurrentMonth || isFuture}
                      className={!isCurrentMonth ? "opacity-50" : ""}
                    />
                  </button>
                </div>
              );
            })}
          </div>
        </div>

        {/* Daily Summary Section - 30% */}
        <div className="flex-[3] border-l pl-6 min-h-0">
          <DailySummary />
        </div>
      </div>
    </div>
  );

  return isMobile ? <WeeklyView /> : <MonthlyView />;
};

export default CalendarWithTodo;
