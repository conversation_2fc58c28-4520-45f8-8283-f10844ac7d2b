import React, { useState, useRef, useEffect } from 'react';

/**
 * Vision OS style date picker component
 * 
 * @param {string} value - Current date value in YYYY-MM-DD format
 * @param {function} onChange - Function to call when date changes
 * @param {string} className - Additional CSS classes
 * @param {boolean} disabled - Whether the picker is disabled
 * @param {string} placeholder - Placeholder text
 */
const VisionOSDatePicker = ({ 
  value = '', 
  onChange, 
  className = '', 
  disabled = false,
  placeholder = 'Select date'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value);
  const pickerRef = useRef(null);
  const inputRef = useRef(null);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Format date for display
  const formatDisplayDate = (dateString) => {
    if (!dateString) return placeholder;
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle date selection
  const handleDateSelect = (dateString) => {
    setSelectedDate(dateString);
    if (onChange) {
      onChange({ target: { value: dateString } });
    }
    setIsOpen(false);
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth();
    
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const days = [];
    const current = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      days.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    
    return days;
  };

  const calendarDays = generateCalendarDays();
  const today = new Date();
  const currentMonth = today.getMonth();

  return (
    <div className={`relative ${className}`} ref={pickerRef}>
      {/* Date Input Display */}
      <button
        ref={inputRef}
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`
          visionos-date-input w-full text-left flex items-center justify-between
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          ${isOpen ? 'ring-2 ring-blue-500 ring-opacity-30' : ''}
        `}
      >
        <span className={selectedDate ? 'text-gray-900' : 'text-gray-500'}>
          {formatDisplayDate(selectedDate)}
        </span>
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 z-50">
          <div className="visionos-card p-4 shadow-2xl">
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">
                {today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="visionos-button p-2 text-gray-500 hover:text-gray-700"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Days of Week */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
                <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((day, index) => {
                const isCurrentMonth = day.getMonth() === currentMonth;
                const isToday = day.toDateString() === today.toDateString();
                const isSelected = selectedDate && day.toISOString().split('T')[0] === selectedDate;
                const dayString = day.toISOString().split('T')[0];

                return (
                  <button
                    key={index}
                    onClick={() => isCurrentMonth && handleDateSelect(dayString)}
                    disabled={!isCurrentMonth}
                    className={`
                      w-8 h-8 text-sm rounded-full transition-all duration-200
                      ${isCurrentMonth 
                        ? 'text-gray-900 hover:bg-blue-100 cursor-pointer' 
                        : 'text-gray-300 cursor-not-allowed'
                      }
                      ${isToday 
                        ? 'bg-blue-500 text-white hover:bg-blue-600' 
                        : ''
                      }
                      ${isSelected && !isToday 
                        ? 'bg-blue-100 text-blue-600 ring-2 ring-blue-500 ring-opacity-30' 
                        : ''
                      }
                    `}
                  >
                    {day.getDate()}
                  </button>
                );
              })}
            </div>

            {/* Quick Actions */}
            <div className="flex justify-between mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => handleDateSelect(today.toISOString().split('T')[0])}
                className="visionos-button px-3 py-1 text-sm text-blue-600 hover:text-blue-700"
              >
                Today
              </button>
              <button
                onClick={() => handleDateSelect('')}
                className="visionos-button px-3 py-1 text-sm text-gray-600 hover:text-gray-700"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisionOSDatePicker;
