import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '../../lib/utils';

/**
 * Modal component for displaying content in a modal dialog
 *
 * @param {boolean} isOpen - Whether the modal is open
 * @param {function} onClose - Function to call when the modal is closed
 * @param {React.ReactNode} children - Content to display in the modal
 * @param {string} className - Additional CSS classes for the modal content
 * @param {boolean} fullScreen - Whether the modal should be full screen
 * @param {boolean} showCloseButton - Whether to show the close button
 * @param {string} position - Position of the modal ('center', 'bottom')
 */
const Modal = ({
  isOpen,
  onClose,
  children,
  className,
  fullScreen = false,
  showCloseButton = true,
  position = 'center',
}) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscapeKey);

    // Prevent body scrolling when modal is open
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal content */}
      <div
        className={cn(
          "relative z-10 transition-all duration-300 ease-in-out",
          fullScreen ? "w-full h-full" : "max-w-lg w-full mx-4",
          position === 'center' ? "flex items-center justify-center" : "",
          position === 'bottom' ? "absolute bottom-0 left-0 right-0" : "",
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {fullScreen ? (
          <div className="bg-black text-white w-full h-full overflow-auto modal-container" style={{
            top: 0,
            height: '100%',
            paddingTop: 0,
            width: '100%',
            position: 'relative'
          }}>
            {/* iOS notch area is now managed by NotchAreaManager component */}

            {showCloseButton && (
              <button
                onClick={onClose}
                className="absolute safe-area-inset-top z-20 p-2 rounded-full bg-black bg-opacity-50 text-white border border-white border-opacity-30 safe-area-margin-top"
                style={{ top: 'calc(env(safe-area-inset-top, 0) + 1rem)', left: '1rem' }}
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            {children}
          </div>
        ) : (
          <div className={cn(
            "bg-white rounded-xl shadow-xl overflow-hidden",
            position === 'bottom' ? "rounded-b-none w-full" : ""
          )}>
            {showCloseButton && (
              <button
                onClick={onClose}
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
                aria-label="Close"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
            {children}
          </div>
        )}
      </div>
    </div>,
    document.body
  );
};

export default Modal;
