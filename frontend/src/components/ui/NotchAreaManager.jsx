import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';

/**
 * Component to manage the iOS notch area color based on the current page
 * and theme settings.
 *
 * This component doesn't render anything visible but directly modifies
 * the theme-color meta tag to control the notch area color.
 */
const NotchAreaManager = () => {
  const location = useLocation();
  const { isDarkMode } = useTheme();

  // Get the appropriate color for the current path and theme
  const getColorForPath = (pathname, isDark) => {
    // Check for specific pages first
    if (pathname.startsWith('/scanner') || pathname.includes('add-meal')) {
      return 'transparent'; // Transparent for scanner and add meal pages to allow semi-transparent styling
    }

    // Default theme colors
    if (isDark) {
      return '#000000'; // Black for iOS dark mode
    } else {
      return '#ffffff'; // White for light mode
    }
  };

  // Check if the current page is the scanner
  const isScanner = (pathname) => {
    return pathname.startsWith('/scanner') || pathname.includes('add-meal');
  };

  useEffect(() => {
    // Check if we're on iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

    // Only apply on iOS devices
    if (!isIOS) return;

    // Find all theme-color meta tags (there might be multiple)
    const metaThemeColors = document.querySelectorAll('meta[name="theme-color"]');

    // If none exist, create one
    if (metaThemeColors.length === 0) {
      const newMeta = document.createElement('meta');
      newMeta.name = 'theme-color';
      document.head.appendChild(newMeta);
      metaThemeColors.push(newMeta);
    }

    // Find the apple-mobile-web-app-status-bar-style meta tag
    const statusBarStyleMeta = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]');

    // Check if current page is scanner
    const scannerPage = isScanner(location.pathname);

    // Add or remove scanner-page class to body
    if (scannerPage) {
      document.body.classList.add('scanner-page');
    } else {
      document.body.classList.remove('scanner-page');
    }

    // Get base color for the current path and theme
    const color = getColorForPath(location.pathname, isDarkMode);

    // For scanner pages, we'll use a semi-transparent black background
    // For other pages, we'll use the solid color from getColorForPath
    const themeColor = scannerPage ? '#000000' : color; // Theme color meta tag needs a solid color

    // Update all theme-color meta tags
    metaThemeColors.forEach(meta => {
      meta.setAttribute('content', themeColor);
    });

    // Update the apple-mobile-web-app-status-bar-style meta tag
    if (statusBarStyleMeta) {
      // Always use black-translucent for scanner pages to allow our custom styling
      // For other pages, use default or black based on theme
      statusBarStyleMeta.setAttribute('content', 'black-translucent');

      // Force a re-render of the status bar by toggling the content
      setTimeout(() => {
        statusBarStyleMeta.setAttribute('content', 'black-translucent');
      }, 50);
    }

    // Also update the theme-color meta tag for scanner pages
    if (scannerPage) {
      // For scanner pages, use transparent or black
      metaThemeColors.forEach(meta => {
        meta.setAttribute('content', 'black');

        // Force a re-render by toggling the content
        setTimeout(() => {
          meta.setAttribute('content', 'rgba(0, 0, 0, 0.25)');
        }, 50);

        // Apply again after a longer delay to handle any race conditions
        setTimeout(() => {
          meta.setAttribute('content', 'rgba(0, 0, 0, 0.25)');
        }, 500);
      });

      // Create a new meta tag specifically for the scanner
      let scannerMetaTag = document.getElementById('scanner-theme-color');
      if (!scannerMetaTag) {
        scannerMetaTag = document.createElement('meta');
        scannerMetaTag.id = 'scanner-theme-color';
        scannerMetaTag.name = 'theme-color';
        scannerMetaTag.setAttribute('content', 'rgba(0, 0, 0, 0.25)');
        document.head.appendChild(scannerMetaTag);
      } else {
        scannerMetaTag.setAttribute('content', 'rgba(0, 0, 0, 0.25)');
      }
    }

    // For scanner pages, add an additional style tag to force the status bar appearance
    if (scannerPage) {
      let scannerStatusBarStyle = document.getElementById('scanner-status-bar-style');
      if (!scannerStatusBarStyle) {
        scannerStatusBarStyle = document.createElement('style');
        scannerStatusBarStyle.id = 'scanner-status-bar-style';
        document.head.appendChild(scannerStatusBarStyle);
      }

      scannerStatusBarStyle.textContent = `
        @supports (-webkit-touch-callout: none) {
          /* iOS specific styles */
          html {
            --sat: env(safe-area-inset-top);
            background-color: transparent !important;
          }

          body {
            padding-top: 0 !important;
            margin-top: 0 !important;
            position: relative;
            min-height: 100vh;
            min-height: -webkit-fill-available;
          }

          /* Make status bar transparent to allow our custom styling */
          :root {
            --status-bar-height: env(safe-area-inset-top, 0);
          }

          /* Remove any existing notch area styling */
          body::before {
            display: none !important;
          }

          /* Force the status bar to be transparent with the correct color */
          body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: env(safe-area-inset-top, 0);
            background-color: rgba(0, 0, 0, 0.25) !important;
            z-index: 99999 !important;
            pointer-events: none;
          }

          /* Ensure the scanner container extends to the top edge */
          .scanner-container {
            top: 0 !important;
            padding-top: 0 !important;
            margin-top: 0 !important;
          }

          /* Hide any other notch covers */
          .ios-notch-cover:not(#ios-notch-cover) {
            display: none !important;
          }

          /* Ensure the iOS notch cover has the correct styling */
          #ios-notch-cover {
            background-color: rgba(0, 0, 0, 0.25) !important;
            opacity: 1 !important;
            z-index: 99999 !important;
            width: 100% !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: env(safe-area-inset-top, 0) !important;
            pointer-events: none !important;
          }
        }
      `;
    } else {
      // Remove the scanner status bar style if not on scanner page
      const scannerStatusBarStyle = document.getElementById('scanner-status-bar-style');
      if (scannerStatusBarStyle) {
        document.head.removeChild(scannerStatusBarStyle);
      }
    }

    // Also create a div that covers the notch area with the appropriate styling
    let notchCover = document.getElementById('ios-notch-cover');
    if (!notchCover) {
      notchCover = document.createElement('div');
      notchCover.id = 'ios-notch-cover';
      notchCover.style.position = 'fixed';
      notchCover.style.top = '0';
      notchCover.style.left = '0';
      notchCover.style.right = '0';
      notchCover.style.width = '100%';
      notchCover.style.height = 'env(safe-area-inset-top, 0)';
      notchCover.style.zIndex = '9999';
      notchCover.style.pointerEvents = 'none';

      // Add to the document body
      document.body.appendChild(notchCover);

      // Force a reflow to ensure the element is properly rendered
      notchCover.offsetHeight;
    }

    // Update the notch cover styling based on page type
    if (scannerPage) {
      // First, remove any existing notch cover elements to avoid conflicts
      const existingNotchCovers = document.querySelectorAll('.ios-notch-cover:not(#ios-notch-cover)');
      existingNotchCovers.forEach(cover => {
        if (cover !== notchCover && document.body.contains(cover)) {
          document.body.removeChild(cover);
        }
      });

      // Apply the scanner-specific class for consistent styling
      notchCover.className = 'ios-notch-cover ios-notch-cover-scanner';

      // Semi-transparent black for scanner pages - matching the scanner UI elements
      // Using the same background color as the scanner overlay (rgba(0, 0, 0, 0.25))
      notchCover.style.backgroundColor = 'rgba(0, 0, 0, 0.25)'; // Match the scanner overlay opacity
      notchCover.style.backdropFilter = 'none'; // Remove blur to match scanner UI

      // Ensure the notch area is fully visible and integrated with the scanner UI
      notchCover.style.zIndex = '99999'; // Use an extremely high z-index to ensure it's above everything
      notchCover.style.transition = 'none'; // Remove transition to ensure immediate styling

      // Make sure the notch cover extends to the edges
      notchCover.style.width = '100%';
      notchCover.style.left = '0';
      notchCover.style.right = '0';
      notchCover.style.top = '0';

      // Ensure it's visible by setting opacity to 1
      notchCover.style.opacity = '1';

      // Set !important for critical styles
      notchCover.style.cssText += ' background-color: rgba(0, 0, 0, 0.25) !important; z-index: 99999 !important; opacity: 1 !important;';

      // Force a reflow to ensure the styling is applied
      notchCover.offsetHeight;

      // Apply the styling again after a short delay to ensure it's applied
      setTimeout(() => {
        notchCover.style.cssText += ' background-color: rgba(0, 0, 0, 0.25) !important; z-index: 99999 !important; opacity: 1 !important;';
      }, 100);

      // Apply again after a longer delay to handle any race conditions
      setTimeout(() => {
        notchCover.style.cssText += ' background-color: rgba(0, 0, 0, 0.25) !important; z-index: 99999 !important; opacity: 1 !important;';
      }, 500);
    } else {
      // Remove scanner-specific class if present
      notchCover.classList.remove('ios-notch-cover-scanner');
      // Solid color for regular pages
      notchCover.style.backgroundColor = color;
      notchCover.style.backdropFilter = 'none';
    }

    // Also add a style tag to ensure the status bar is properly colored
    let styleTag = document.getElementById('notch-area-style');
    if (!styleTag) {
      styleTag = document.createElement('style');
      styleTag.id = 'notch-area-style';
      document.head.appendChild(styleTag);
    }

    // Add CSS to override any other styles that might affect the notch area
    // Different styling for scanner vs regular pages
    styleTag.textContent = scannerPage
      ? `
        @media screen and (display-mode: standalone) {
          /* Remove any existing notch area styling */
          body::before {
            display: none !important;
          }

          /* Force the status bar to be transparent with the correct color */
          body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: env(safe-area-inset-top, 0);
            background-color: rgba(0, 0, 0, 0.25) !important; /* Match scanner overlay opacity */
            z-index: 99999 !important; /* Use an extremely high z-index to ensure it's above everything */
            pointer-events: none;
          }

          /* Ensure the scanner UI extends to the edges of the screen */
          .full-screen-cover {
            top: 0 !important;
            height: 100% !important;
            padding-top: 0 !important;
          }

          /* Specific styling for the scanner container */
          .scanner-container {
            top: 0 !important;
            height: 100% !important;
            padding-top: 0 !important;
            margin-top: 0 !important;
          }

          /* Ensure video and captured image extend to cover the notch area */
          .scanner-video, .scanner-image {
            height: calc(100% + env(safe-area-inset-top, 0)) !important;
            top: calc(-1 * env(safe-area-inset-top, 0)) !important;
            width: 100% !important;
          }

          /* Ensure the viewfinder overlay extends to cover the notch area */
          .scanner-viewfinder-overlay {
            top: calc(-1 * env(safe-area-inset-top, 0)) !important;
            height: calc(100% + env(safe-area-inset-top, 0)) !important;
            width: 100% !important;
          }

          /* Ensure the iOS notch cover has the correct styling */
          #ios-notch-cover {
            background-color: rgba(0, 0, 0, 0.25) !important;
            opacity: 1 !important;
            z-index: 99999 !important;
            width: 100% !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            height: env(safe-area-inset-top, 0) !important;
            pointer-events: none !important;
          }

          /* Hide any other notch covers */
          .ios-notch-cover:not(#ios-notch-cover) {
            display: none !important;
          }

          /* Override any other styles that might affect the notch area */
          html, body {
            background-color: transparent !important;
          }

          /* Ensure the modal container extends to the top edge */
          .modal-container {
            top: 0 !important;
            padding-top: 0 !important;
            margin-top: 0 !important;
          }
        }
      `
      : `
        @media screen and (display-mode: standalone) {
          body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: env(safe-area-inset-top, 0);
            background-color: ${color};
            z-index: 9999;
            pointer-events: none;
          }
        }
      `;

    // Clean up on unmount
    return () => {
      // Reset theme color meta tags
      metaThemeColors.forEach(meta => {
        meta.setAttribute('content', '#ffffff');
      });

      // Reset status bar style
      if (statusBarStyleMeta) {
        statusBarStyleMeta.setAttribute('content', 'default');
      }

      // Remove the notch cover
      if (notchCover && document.body.contains(notchCover)) {
        document.body.removeChild(notchCover);
      }

      // Remove the style tag
      if (styleTag && document.head.contains(styleTag)) {
        document.head.removeChild(styleTag);
      }

      // Remove the scanner status bar style
      const scannerStatusBarStyle = document.getElementById('scanner-status-bar-style');
      if (scannerStatusBarStyle && document.head.contains(scannerStatusBarStyle)) {
        document.head.removeChild(scannerStatusBarStyle);
      }

      // Remove the scanner theme color meta tag
      const scannerMetaTag = document.getElementById('scanner-theme-color');
      if (scannerMetaTag && document.head.contains(scannerMetaTag)) {
        document.head.removeChild(scannerMetaTag);
      }

      // Remove any other notch covers that might have been created
      const existingNotchCovers = document.querySelectorAll('.ios-notch-cover');
      existingNotchCovers.forEach(cover => {
        if (document.body.contains(cover)) {
          document.body.removeChild(cover);
        }
      });

      // Remove scanner-page class from body
      document.body.classList.remove('scanner-page');
    };
  }, [location.pathname, isDarkMode]);

  // This component doesn't render anything visible
  return null;
};

export default NotchAreaManager;
