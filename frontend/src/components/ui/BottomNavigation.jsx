import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useIsMobile } from '../../hooks/useIsMobile';
import { cn } from '../../lib/utils';

/**
 * BottomNavigation component for mobile navigation
 *
 * @param {string} className - Additional CSS classes
 * @param {Function} onAddClick - Function to call when the add button is clicked
 */
const BottomNavigation = ({ className, onAddClick }) => {
  const location = useLocation();
  const path = location.pathname;
  const isMobile = useIsMobile();

  // Don't show navigation on desktop
  if (!isMobile) {
    return null;
  }

  return (
    <div className={cn("fixed bottom-6 left-0 right-0 z-50 flex justify-center", className)}>
      <div className="flex items-center bg-black text-white px-6 py-3 rounded-full gap-12 shadow-xl">
        <Link
          to="/"
          aria-label="Home"
          className={cn(
            "flex items-center justify-center transition-all duration-200",
            path === "/" ? "text-white scale-110" : "text-gray-400 hover:text-gray-200"
          )}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
        </Link>
        <button
          onClick={onAddClick}
          aria-label="Add meal"
          className="flex items-center justify-center transition-all duration-200 text-gray-400 hover:text-gray-200"
        >
          <div className="flex items-center justify-center rounded-full p-2 bg-gray-800 hover:bg-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
          </div>
        </button>
        <Link
          to="/profile"
          aria-label="Profile"
          className={cn(
            "flex items-center justify-center transition-all duration-200",
            path === "/profile" ? "text-white scale-110" : "text-gray-400 hover:text-gray-200"
          )}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default BottomNavigation;
