import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';
import { cn } from '../../lib/utils';
import { getWeightDisplayValue, getWeightUnitLabel } from '../../utils/unitConversion';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

/**
 * WeightProgressChart component for displaying weight progress over time
 *
 * @param {Array} weightData - Array of weight log objects with date and weight
 * @param {number} targetWeight - Target weight for goal line
 * @param {string} weightUnit - Weight unit (kg or lbs)
 * @param {string} className - Additional CSS classes
 */
const WeightProgressChart = ({ 
  weightData = [], 
  targetWeight = null, 
  weightUnit = 'kg',
  className 
}) => {
  // Prepare data for Chart.js with adaptive date formatting
  const getAdaptiveDateLabels = (weightData) => {
    if (!weightData || weightData.length === 0) return [];

    // Calculate the time span of the data
    const dates = weightData.map(entry => new Date(entry.date));
    const minDate = new Date(Math.min(...dates));
    const maxDate = new Date(Math.max(...dates));
    const daysDiff = Math.ceil((maxDate - minDate) / (1000 * 60 * 60 * 24));

    return weightData.map(entry => {
      const date = new Date(entry.date);

      // For periods longer than 90 days, show month/year
      if (daysDiff > 90) {
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      }
      // For periods longer than 30 days, show month/day
      else if (daysDiff > 30) {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
      // For shorter periods, show month/day
      else {
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      }
    });
  };

  const labels = getAdaptiveDateLabels(weightData);

  const weights = weightData.map(entry => 
    parseFloat(getWeightDisplayValue(entry.weight, weightUnit))
  );

  // Calculate Y-axis range with some padding
  const minWeight = Math.min(...weights);
  const maxWeight = Math.max(...weights);
  const weightRange = maxWeight - minWeight;
  const padding = Math.max(weightRange * 0.1, 2); // At least 2 units padding

  let yAxisMin = minWeight - padding;
  let yAxisMax = maxWeight + padding;

  // Include target weight in the range if provided
  if (targetWeight) {
    const targetWeightDisplay = parseFloat(getWeightDisplayValue(targetWeight, weightUnit));
    yAxisMin = Math.min(yAxisMin, targetWeightDisplay - padding);
    yAxisMax = Math.max(yAxisMax, targetWeightDisplay + padding);
  }

  // Create datasets
  const datasets = [
    {
      label: 'Weight',
      data: weights,
      borderColor: '#3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      borderWidth: 3,
      pointBackgroundColor: '#3b82f6',
      pointBorderColor: '#ffffff',
      pointBorderWidth: 2,
      pointRadius: 6,
      pointHoverRadius: 8,
      fill: true,
      tension: 0.4
    }
  ];

  // Add target weight line if provided
  if (targetWeight && weightData.length > 0) {
    const targetWeightDisplay = parseFloat(getWeightDisplayValue(targetWeight, weightUnit));
    datasets.push({
      label: 'Target Weight',
      data: new Array(weightData.length).fill(targetWeightDisplay),
      borderColor: '#10b981',
      backgroundColor: 'transparent',
      borderWidth: 2,
      borderDash: [5, 5],
      pointRadius: 0,
      pointHoverRadius: 0,
      fill: false
    });
  }

  const data = {
    labels,
    datasets
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: targetWeight ? true : false,
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#3b82f6',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            if (context.datasetIndex === 0) {
              return `Weight: ${context.parsed.y} ${getWeightUnitLabel(weightUnit)}`;
            } else {
              return `Target: ${context.parsed.y} ${getWeightUnitLabel(weightUnit)}`;
            }
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          maxTicksLimit: (() => {
            // Calculate the time span to determine appropriate tick limit
            if (!weightData || weightData.length === 0) return 10;

            const dates = weightData.map(entry => new Date(entry.date));
            const minDate = new Date(Math.min(...dates));
            const maxDate = new Date(Math.max(...dates));
            const daysDiff = Math.ceil((maxDate - minDate) / (1000 * 60 * 60 * 24));

            // Adjust max ticks based on time period to avoid clutter
            if (daysDiff > 365) return 6;  // 1 year+: show ~6 labels
            if (daysDiff > 180) return 8;  // 6 months: show ~8 labels
            if (daysDiff > 90) return 10;  // 3 months: show ~10 labels
            if (daysDiff > 30) return 12;  // 1-3 months: show ~12 labels
            return 15; // 30 days or less: show up to 15 labels
          })()
        }
      },
      y: {
        min: yAxisMin,
        max: yAxisMax,
        grid: {
          color: 'rgba(107, 114, 128, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          },
          color: '#6b7280',
          callback: function(value) {
            return `${value} ${getWeightUnitLabel(weightUnit)}`;
          }
        }
      }
    },
    interaction: {
      mode: 'nearest',
      axis: 'x',
      intersect: false
    },
    elements: {
      point: {
        hoverBorderWidth: 3
      }
    }
  };

  // Show message if no data
  if (!weightData || weightData.length === 0) {
    return (
      <div className={cn("flex items-center justify-center h-64 bg-gray-50 rounded-xl", className)}>
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">No weight data available</p>
          <p className="text-gray-400 text-xs mt-1">Start logging your weight to see progress</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("w-full", className)}>
      <div className="h-64">
        <Line data={data} options={options} />
      </div>
    </div>
  );
};

export default WeightProgressChart;
