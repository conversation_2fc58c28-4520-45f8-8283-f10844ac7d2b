import { classNames } from '../../utils/classNames';

/**
 * DashboardHeader component for displaying the dashboard header with user greeting
 * 
 * @param {Object} user - User profile data
 * @param {Object} goal - Current goal data
 * @param {string} className - Additional CSS classes
 */
const DashboardHeader = ({ user, goal, className }) => {
  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour >= 5 && hour < 12) return 'Good morning';
    if (hour >= 12 && hour < 18) return 'Good afternoon';
    return 'Good evening';
  };
  
  // Get user's first name from email
  const getFirstName = () => {
    if (!user || !user.email) return '';
    return user.email.split('@')[0].split('.')[0];
  };
  
  // Format goal message
  const getGoalMessage = () => {
    if (!goal) return 'Set your goals to get started';
    
    const { goalType } = user || {};
    const { dailyCalorieGoal } = goal || {};
    
    if (goalType === 'lose') {
      return `You're on track to lose weight with a ${dailyCalorieGoal} calorie goal`;
    } else if (goalType === 'gain') {
      return `You're on track to gain weight with a ${dailyCalorieGoal} calorie goal`;
    } else {
      return `You're on track to maintain your weight with a ${dailyCalorieGoal} calorie goal`;
    }
  };
  
  return (
    <div className={classNames(
      "bg-gradient-to-r from-black to-gray-800 text-white rounded-2xl p-6 mb-6",
      className
    )}>
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold mb-1">
            {getGreeting()}, {getFirstName()}
          </h1>
          <p className="text-gray-300">
            {getGoalMessage()}
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-300">
            {new Date().toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;
