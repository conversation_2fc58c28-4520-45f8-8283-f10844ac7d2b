import { classNames } from '../../utils/classNames';
import MacroNutrient from './MacroNutrient';
import { ProteinIcon, CarbsIcon, FatsIcon } from '../../assets/icons/DashboardIcons';

/**
 * MacroCard component for displaying macronutrient information
 *
 * @param {Object} macros - Object containing protein, carbs, and fats values
 * @param {Object} goals - Object containing protein, carbs, and fats goals
 * @param {string} className - Additional CSS classes
 */
const MacroCard = ({ macros, goals = {}, className }) => {
  // Default goals if not provided
  const defaultGoals = {
    protein: 50,
    carbs: 200,
    fats: 65
  };

  // Merge provided goals with defaults
  const macroGoals = {
    protein: goals.protein || defaultGoals.protein,
    carbs: goals.carbs || defaultGoals.carbs,
    fats: goals.fats || defaultGoals.fats
  };

  return (
    <div className={classNames(
      "bg-white rounded-2xl border border-gray-100 p-6 shadow-sm",
      className
    )}>
      <h2 className="text-lg font-semibold mb-6">Macronutrients</h2>

      <div className="space-y-6">
        <MacroNutrient
          name="Protein"
          value={macros.protein}
          goal={macroGoals.protein}
          color="#3B82F6" // blue-500
          icon={<ProteinIcon className="w-10 h-10" />}
        />

        <MacroNutrient
          name="Carbs"
          value={macros.carbs}
          goal={macroGoals.carbs}
          color="#10B981" // green-500
          icon={<CarbsIcon className="w-10 h-10" />}
        />

        <MacroNutrient
          name="Fats"
          value={macros.fats}
          goal={macroGoals.fats}
          color="#F59E0B" // amber-500
          icon={<FatsIcon className="w-10 h-10" />}
        />
      </div>
    </div>
  );
};

export default MacroCard;
