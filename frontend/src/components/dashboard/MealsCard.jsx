import { classNames } from '../../utils/classNames';
import MealCard from './MealCard';
import { AddIcon, MealIcon } from '../../assets/icons/DashboardIcons';

/**
 * MealsCard component for displaying meals
 * 
 * @param {Array} meals - Array of meal objects
 * @param {Function} onAddMeal - Function to call when add meal button is clicked
 * @param {Function} onDeleteMeal - Function to call when delete meal button is clicked
 * @param {string} className - Additional CSS classes
 */
const MealsCard = ({ meals = [], onAddMeal, onDeleteMeal, className }) => {
  return (
    <div className={classNames(
      "bg-white rounded-2xl border border-gray-100 p-6 shadow-sm",
      className
    )}>
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="p-2 bg-black bg-opacity-10 rounded-full mr-3">
            <MealIcon className="w-5 h-5 text-black" />
          </div>
          <h2 className="text-lg font-semibold">Today's Meals</h2>
        </div>
        
        <button 
          onClick={onAddMeal}
          className="flex items-center px-4 py-2 bg-black text-white rounded-xl hover:bg-opacity-90 transition-colors"
        >
          <AddIcon className="w-4 h-4 mr-2" />
          <span>Add Meal</span>
        </button>
      </div>
      
      {meals.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-xl">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <MealIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">No meals logged today</h3>
          <p className="text-gray-500 mb-6">
            Start tracking your food intake by adding a meal
          </p>
          <button 
            onClick={onAddMeal}
            className="inline-flex items-center px-4 py-2 bg-black text-white rounded-xl hover:bg-opacity-90 transition-colors"
          >
            <AddIcon className="w-4 h-4 mr-2" />
            <span>Add Your First Meal</span>
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {meals.map((meal) => (
            <MealCard 
              key={meal.id} 
              meal={meal} 
              onDelete={onDeleteMeal}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default MealsCard;
