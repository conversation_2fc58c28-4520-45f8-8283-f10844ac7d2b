import { classNames } from '../../utils/classNames';

/**
 * MacroNutrient component for displaying macronutrient information
 * 
 * @param {string} name - Name of the macronutrient (e.g., "Protein")
 * @param {number} value - Current value in grams
 * @param {number} goal - Goal value in grams
 * @param {string} color - Color for the progress indicator
 * @param {React.ReactNode} icon - Icon component to display
 * @param {string} className - Additional CSS classes
 */
const MacroNutrient = ({
  name,
  value,
  goal,
  color,
  icon,
  className
}) => {
  // Calculate percentage of goal
  const percentage = goal > 0 ? Math.min(100, Math.round((value / goal) * 100)) : 0;
  
  return (
    <div className={classNames("flex flex-col", className)}>
      <div className="flex items-center mb-2">
        <div className={`p-2 rounded-full mr-3`} style={{ backgroundColor: `${color}20` }}>
          <div className="text-black">{icon}</div>
        </div>
        <div>
          <h3 className="font-medium">{name}</h3>
          <div className="flex items-center text-sm">
            <span className="font-bold">{Math.round(value)}g</span>
            <span className="text-gray-500 mx-1">/</span>
            <span className="text-gray-500">{goal}g</span>
          </div>
        </div>
      </div>
      
      {/* Progress bar */}
      <div className="w-full bg-gray-100 rounded-full h-1.5">
        <div
          className="h-full rounded-full"
          style={{ 
            width: `${percentage}%`,
            backgroundColor: color
          }}
        ></div>
      </div>
    </div>
  );
};

export default MacroNutrient;
