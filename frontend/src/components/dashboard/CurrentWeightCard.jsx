import { classNames } from '../../utils/classNames';
import { getWeightDisplayValue, getWeightUnitLabel } from '../../utils/unitConversion';

/**
 * CurrentWeightCard component for displaying current weight with update functionality
 * 
 * @param {number} currentWeight - Current weight in kg
 * @param {string} weightUnit - Weight unit preference (kg or lbs)
 * @param {Function} onUpdateClick - Function to call when update button is clicked
 * @param {string} className - Additional CSS classes
 */
const CurrentWeightCard = ({ 
  currentWeight, 
  weightUnit = 'kg', 
  onUpdateClick, 
  className 
}) => {
  const displayWeight = currentWeight ? getWeightDisplayValue(currentWeight, weightUnit) : '--';
  const unitLabel = getWeightUnitLabel(weightUnit);

  return (
    <div className={classNames(
      "bg-white rounded-2xl border border-gray-100 p-6 shadow-sm",
      className
    )}>
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
            <span className="text-white text-lg">⚖️</span>
          </div>
          <div>
            <p className="text-gray-500 text-sm mb-1">Current Weight</p>
            <div className="flex items-baseline">
              <span className="text-2xl font-bold">
                {displayWeight}
              </span>
              <span className="ml-1 text-gray-500 text-sm">{unitLabel}</span>
            </div>
          </div>
        </div>
        
        <button
          onClick={onUpdateClick}
          className="px-4 py-2 bg-black text-white rounded-xl hover:bg-gray-800 transition-colors font-medium"
        >
          Update
        </button>
      </div>
      
      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500">
          Track your progress by updating your weight regularly
        </p>
      </div>
    </div>
  );
};

export default CurrentWeightCard;
