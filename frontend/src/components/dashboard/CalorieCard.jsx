import { classNames } from '../../utils/classNames';
import CircularProgress from './CircularProgress';
import { CalorieIcon } from '../../assets/icons/DashboardIcons';

/**
 * CalorieCard component for displaying calorie information
 * 
 * @param {number} goal - Calorie goal for the day
 * @param {number} consumed - Calories consumed so far
 * @param {string} className - Additional CSS classes
 */
const CalorieCard = ({ goal, consumed, className }) => {
  // Calculate remaining calories
  const remaining = goal - consumed;
  
  // Calculate percentage of goal consumed
  const percentage = goal > 0 ? Math.min(100, Math.round((consumed / goal) * 100)) : 0;
  
  // Determine color based on percentage
  const getColor = () => {
    if (percentage >= 100) return '#EF4444'; // red-500
    if (percentage >= 90) return '#F59E0B'; // amber-500
    return '#000000'; // black
  };
  
  return (
    <div className={classNames(
      "bg-white rounded-2xl border border-gray-100 p-6 shadow-sm",
      className
    )}>
      <div className="flex items-center mb-4">
        <div className="p-2 bg-black bg-opacity-10 rounded-full mr-3">
          <CalorieIcon className="w-5 h-5 text-black" />
        </div>
        <h2 className="text-lg font-semibold">Calories</h2>
      </div>
      
      <div className="flex items-center justify-between">
        <CircularProgress 
          progress={percentage}
          size={120}
          strokeWidth={10}
          color={getColor()}
          value={`${percentage}%`}
        />
        
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">Goal</p>
            <p className="text-xl font-bold">{goal}</p>
          </div>
          
          <div className="text-center">
            <p className="text-sm text-gray-500">Consumed</p>
            <p className="text-xl font-bold">{consumed}</p>
          </div>
          
          <div className="text-center">
            <p className="text-sm text-gray-500">Remaining</p>
            <p className={`text-xl font-bold ${remaining < 0 ? 'text-red-500' : ''}`}>
              {remaining}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CalorieCard;
