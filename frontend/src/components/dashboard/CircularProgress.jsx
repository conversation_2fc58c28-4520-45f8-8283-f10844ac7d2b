import { classNames } from '../../utils/classNames';

/**
 * CircularProgress component for displaying progress in a circular format
 * 
 * @param {number} progress - Progress percentage (0-100)
 * @param {number} size - Size of the circle in pixels
 * @param {number} strokeWidth - Width of the progress stroke
 * @param {string} color - Color of the progress stroke
 * @param {string} label - Optional label to display below the circle
 * @param {string} value - Optional value to display inside the circle
 * @param {string} className - Additional CSS classes
 */
const CircularProgress = ({
  progress,
  size = 80,
  strokeWidth = 6,
  color = '#000000',
  label,
  value,
  className
}) => {
  // Constraints
  const actualProgress = Math.min(100, Math.max(0, progress));

  // SVG parameters
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const dash = (circumference * actualProgress) / 100;
  const center = size / 2;

  return (
    <div className={classNames("flex flex-col items-center", className)}>
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size}>
          {/* Background circle */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke="#E5E7EB"
            strokeWidth={strokeWidth}
          />

          {/* Progress circle */}
          <circle
            cx={center}
            cy={center}
            r={radius}
            fill="none"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeDasharray={`${dash} ${circumference - dash}`}
            strokeLinecap="round"
            transform={`rotate(-90 ${center} ${center})`}
          />
        </svg>
        {value ? (
          <div className="absolute inset-0 flex items-center justify-center text-sm font-bold">
            {value}
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center text-sm font-bold">
            {progress}%
          </div>
        )}
      </div>
      {label && <span className="mt-2 text-xs text-gray-500">{label}</span>}
    </div>
  );
};

export default CircularProgress;
