import { Outlet, useLocation } from 'react-router-dom'
import Navbar from './Navbar'
import Footer from './Footer'
import { useIsMobile } from '../hooks/useIsMobile'

const Layout = () => {
  const location = useLocation();
  const isMobile = useIsMobile();

  // Don't show footer on dashboard, profile, or BMI pages when on mobile
  const showFooter = !(isMobile && (location.pathname === '/' || location.pathname === '/profile' || location.pathname === '/bmi'));

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <main className="flex-grow container mx-auto px-4 py-8">
        <Outlet />
      </main>
      {showFooter && <Footer />}
    </div>
  )
}

export default Layout
