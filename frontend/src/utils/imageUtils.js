/**
 * Image utility functions for handling image URLs and processing
 */

/**
 * Helper function to ensure image URLs are absolute
 * @param {string} imageUrl - The image URL to process
 * @returns {string|null} - The absolute image URL or null if no URL provided
 */
export const getAbsoluteImageUrl = (imageUrl) => {
  if (!imageUrl) return null;

  // If it's already an absolute URL, return it as is
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's a blob URL, check if it's still valid and return fallback if not
  if (imageUrl.startsWith('blob:')) {
    // For blob URLs that may have become invalid after page navigation,
    // we'll return a fallback image to prevent "Not allowed to load local resource" errors
    try {
      // Check if the blob URL is still accessible
      // If this is an old blob URL from localStorage, it may be invalid
      if (imageUrl.includes('blob:') && !imageUrl.includes(window.location.origin)) {
        console.warn('Invalid blob URL detected, using fallback image');
        return 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
      }
      return imageUrl;
    } catch (error) {
      console.warn('Blob URL error, using fallback image:', error);
      return 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop';
    }
  }

  // If it's a relative URL starting with /uploads, make it absolute
  if (imageUrl.startsWith('/uploads')) {
    // Use the current host with the correct port
    const host = window.location.hostname;
    const port = window.location.port || '86'; // Default to port 86 if not specified
    return `http://${host}:${port}${imageUrl}`;
  }

  // Return the original URL if it doesn't match any of the above conditions
  return imageUrl;
};

/**
 * Convert a blob to a data URL
 * @param {Blob} blob - The blob to convert
 * @returns {Promise<string>} - Promise that resolves to the data URL
 */
export const blobToDataUrl = (blob) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result);
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

/**
 * Convert a data URL to a blob
 * @param {string} dataUrl - The data URL to convert
 * @returns {Blob} - The resulting blob
 */
export const dataUrlToBlob = (dataUrl) => {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new Blob([u8arr], { type: mime });
};

/**
 * Resize an image to fit within specified dimensions while maintaining aspect ratio
 * @param {HTMLImageElement} img - The image element
 * @param {number} maxWidth - Maximum width
 * @param {number} maxHeight - Maximum height
 * @returns {Object} - Object with width and height properties
 */
export const calculateImageDimensions = (img, maxWidth, maxHeight) => {
  let { width, height } = img;

  // Calculate the scaling factor
  const scaleX = maxWidth / width;
  const scaleY = maxHeight / height;
  const scale = Math.min(scaleX, scaleY);

  // Apply the scale if the image is larger than the max dimensions
  if (scale < 1) {
    width *= scale;
    height *= scale;
  }

  return { width: Math.round(width), height: Math.round(height) };
};

/**
 * Create a canvas element with the specified dimensions
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @returns {HTMLCanvasElement} - The created canvas element
 */
export const createCanvas = (width, height) => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
};

/**
 * Load an image from a URL
 * @param {string} url - The image URL
 * @returns {Promise<HTMLImageElement>} - Promise that resolves to the loaded image
 */
export const loadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.crossOrigin = 'anonymous'; // Enable CORS for external images
    img.src = url;
  });
};

/**
 * Compress an image to a specified quality
 * @param {HTMLImageElement} img - The image to compress
 * @param {number} quality - Quality factor (0-1)
 * @param {string} format - Output format ('image/jpeg' or 'image/png')
 * @returns {Promise<Blob>} - Promise that resolves to the compressed image blob
 */
export const compressImage = (img, quality = 0.8, format = 'image/jpeg') => {
  return new Promise((resolve) => {
    const canvas = createCanvas(img.width, img.height);
    const ctx = canvas.getContext('2d');

    ctx.drawImage(img, 0, 0);

    canvas.toBlob(resolve, format, quality);
  });
};

/**
 * Get the file extension from a filename or URL
 * @param {string} filename - The filename or URL
 * @returns {string} - The file extension (without the dot)
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  const lastDot = filename.lastIndexOf('.');
  return lastDot !== -1 ? filename.substring(lastDot + 1).toLowerCase() : '';
};

/**
 * Check if a file is an image based on its extension
 * @param {string} filename - The filename to check
 * @returns {boolean} - True if the file is an image
 */
export const isImageFile = (filename) => {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = getFileExtension(filename);
  return imageExtensions.includes(extension);
};

/**
 * Generate a fallback image URL for when an image fails to load
 * @param {string} type - The type of fallback image ('food', 'meal', 'default')
 * @returns {string} - The fallback image URL
 */
export const getFallbackImageUrl = (type = 'food') => {
  const fallbackImages = {
    food: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?q=80&w=1000&auto=format&fit=crop',
    meal: 'https://images.unsplash.com/photo-1490818387583-1baba5e638af?q=80&w=1000&auto=format&fit=crop',
    default: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?q=80&w=1000&auto=format&fit=crop'
  };

  return fallbackImages[type] || fallbackImages.default;
};
