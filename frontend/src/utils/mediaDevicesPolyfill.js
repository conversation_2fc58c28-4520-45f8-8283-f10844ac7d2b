/**
 * Polyfill for the MediaDevices API
 * This helps ensure compatibility across different browsers, including iOS Safari
 */
export function applyMediaDevicesPolyfill() {
  // Older browsers might not implement mediaDevices at all, so we set an empty object first
  if (navigator.mediaDevices === undefined) {
    navigator.mediaDevices = {};
  }

  // Some browsers partially implement mediaDevices. We can't just assign an object
  // with getUserMedia as it would overwrite existing properties.
  // Here, we will just add the getUserMedia property if it's missing.
  if (navigator.mediaDevices.getUserMedia === undefined) {
    navigator.mediaDevices.getUserMedia = function(constraints) {
      // First get ahold of the legacy getUserMedia, if present
      const getUserMedia = navigator.webkitGetUserMedia ||
                          navigator.mozGetUserMedia ||
                          navigator.msGetUserMedia;

      // Some browsers just don't implement it - return a rejected promise with an error
      // to keep a consistent interface
      if (!getUserMedia) {
        return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
      }

      // Otherwise, wrap the call to the old navigator.getUserMedia with a Promise
      return new Promise(function(resolve, reject) {
        getUserMedia.call(navigator, constraints, resolve, reject);
      });
    };
  }

  // iOS Safari specific fixes
  if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
    // Fix for iOS Safari not properly stopping streams
    if (typeof MediaStreamTrack !== 'undefined' &&
        typeof MediaStreamTrack.prototype.stop === 'undefined') {
      MediaStreamTrack.prototype.stop = function() {
        this.enabled = false;
      };
    }

    // Fix for enumerateDevices not being available in older iOS Safari
    if (navigator.mediaDevices &&
        typeof navigator.mediaDevices.enumerateDevices === 'undefined') {
      navigator.mediaDevices.enumerateDevices = function() {
        return Promise.resolve([{
          kind: 'videoinput',
          deviceId: 'default',
          label: 'Camera',
          groupId: ''
        }]);
      };
    }
  }
}
