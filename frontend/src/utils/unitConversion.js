/**
 * Unit conversion utilities for height and weight
 */

// Conversion constants
const CM_TO_INCHES = 2.54;
const INCHES_PER_FOOT = 12;
const KG_TO_LBS = 2.20462;

/**
 * Convert centimeters to feet and inches
 * @param {number} cm - Height in centimeters
 * @returns {Object} - Object with feet and inches properties
 */
export const cmToFeetInches = (cm) => {
  if (!cm || cm <= 0) return { feet: 0, inches: 0 };
  
  const totalInches = cm / CM_TO_INCHES;
  const feet = Math.floor(totalInches / INCHES_PER_FOOT);
  const inches = Math.round((totalInches % INCHES_PER_FOOT) * 10) / 10; // Round to 1 decimal place
  
  return { feet, inches };
};

/**
 * Convert feet and inches to centimeters
 * @param {number} feet - Height in feet
 * @param {number} inches - Additional inches
 * @returns {number} - Height in centimeters
 */
export const feetInchesToCm = (feet, inches = 0) => {
  if (!feet && !inches) return 0;
  
  const totalInches = (feet || 0) * INCHES_PER_FOOT + (inches || 0);
  return Math.round(totalInches * CM_TO_INCHES * 10) / 10; // Round to 1 decimal place
};

/**
 * Convert kilograms to pounds
 * @param {number} kg - Weight in kilograms
 * @returns {number} - Weight in pounds
 */
export const kgToLbs = (kg) => {
  if (!kg || kg <= 0) return 0;
  return Math.round(kg * KG_TO_LBS * 10) / 10; // Round to 1 decimal place
};

/**
 * Convert pounds to kilograms
 * @param {number} lbs - Weight in pounds
 * @returns {number} - Weight in kilograms
 */
export const lbsToKg = (lbs) => {
  if (!lbs || lbs <= 0) return 0;
  return Math.round((lbs / KG_TO_LBS) * 10) / 10; // Round to 1 decimal place
};

/**
 * Format height for display based on unit preference
 * @param {number} heightCm - Height in centimeters (stored value)
 * @param {string} unit - Display unit ("cm" or "ft")
 * @returns {string} - Formatted height string
 */
export const formatHeight = (heightCm, unit = "cm") => {
  if (!heightCm) return "";
  
  if (unit === "ft") {
    const { feet, inches } = cmToFeetInches(heightCm);
    return `${feet}'${inches}"`;
  }
  
  return `${heightCm} cm`;
};

/**
 * Format weight for display based on unit preference
 * @param {number} weightKg - Weight in kilograms (stored value)
 * @param {string} unit - Display unit ("kg" or "lbs")
 * @returns {string} - Formatted weight string
 */
export const formatWeight = (weightKg, unit = "kg") => {
  if (!weightKg) return "";
  
  if (unit === "lbs") {
    const lbs = kgToLbs(weightKg);
    return `${lbs} lbs`;
  }
  
  return `${weightKg} kg`;
};

/**
 * Get display value for height based on unit preference
 * @param {number} heightCm - Height in centimeters (stored value)
 * @param {string} unit - Display unit ("cm" or "ft")
 * @returns {number|string} - Display value
 */
export const getHeightDisplayValue = (heightCm, unit = "cm") => {
  if (!heightCm) return "";

  if (unit === "ft") {
    const { feet, inches } = cmToFeetInches(heightCm);
    return `${feet}'${inches}"`;
  }

  return heightCm;
};

/**
 * Get numeric display value for height input fields
 * @param {number} heightCm - Height in centimeters (stored value)
 * @param {string} unit - Display unit ("cm" or "ft")
 * @returns {number} - Numeric display value
 */
export const getHeightInputValue = (heightCm, unit = "cm") => {
  if (!heightCm) return "";

  if (unit === "ft") {
    // Convert to decimal feet for input
    const totalInches = heightCm / 2.54;
    const decimalFeet = totalInches / 12;
    return Math.round(decimalFeet * 100) / 100; // Round to 2 decimal places
  }

  return heightCm;
};

/**
 * Get display value for weight based on unit preference
 * @param {number} weightKg - Weight in kilograms (stored value)
 * @param {string} unit - Display unit ("kg" or "lbs")
 * @returns {number} - Display value
 */
export const getWeightDisplayValue = (weightKg, unit = "kg") => {
  if (!weightKg) return "";
  
  if (unit === "lbs") {
    return kgToLbs(weightKg);
  }
  
  return weightKg;
};

/**
 * Get unit label for height
 * @param {string} unit - Unit ("cm" or "ft")
 * @returns {string} - Unit label
 */
export const getHeightUnitLabel = (unit = "cm") => {
  return unit === "ft" ? "ft/in" : "cm";
};

/**
 * Get unit label for weight
 * @param {string} unit - Unit ("kg" or "lbs")
 * @returns {string} - Unit label
 */
export const getWeightUnitLabel = (unit = "kg") => {
  return unit === "lbs" ? "lbs" : "kg";
};

/**
 * Convert weight between units
 * @param {number} value - Weight value
 * @param {string} fromUnit - Source unit ("kg" or "lbs")
 * @param {string} toUnit - Target unit ("kg" or "lbs")
 * @returns {number} - Converted weight value
 */
export const convertWeight = (value, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;

  if (fromUnit === "kg" && toUnit === "lbs") {
    return kgToLbs(value);
  } else if (fromUnit === "lbs" && toUnit === "kg") {
    return lbsToKg(value);
  }

  return value;
};

/**
 * Convert input value to storage format (always cm for height, kg for weight)
 * @param {number} value - Input value
 * @param {string} unit - Input unit
 * @param {string} type - "height" or "weight"
 * @returns {number} - Value in storage format
 */
export const convertToStorageFormat = (value, unit, type) => {
  if (!value) return null;

  if (type === "height") {
    // Height is always stored in cm
    if (unit === "ft") {
      // For feet input, we need to handle feet'inches" format
      // This would need special handling in the UI
      return value; // Assume already converted
    }
    return value;
  } else if (type === "weight") {
    // Weight is always stored in kg
    if (unit === "lbs") {
      return lbsToKg(value);
    }
    return value;
  }

  return value;
};

/**
 * Parse height input in feet'inches" format
 * @param {string} input - Input string like "5'10"" or "5'10"
 * @returns {Object} - Object with feet and inches
 */
export const parseHeightInput = (input) => {
  if (!input) return { feet: 0, inches: 0 };
  
  // Handle string input like "5'10"" or "5'10"
  if (typeof input === 'string') {
    const match = input.match(/(\d+)'(\d*\.?\d*)"?/);
    if (match) {
      const feet = parseInt(match[1]) || 0;
      const inches = parseFloat(match[2]) || 0;
      return { feet, inches };
    }
  }
  
  return { feet: 0, inches: 0 };
};

// Export constants for use in validation
export const CONVERSION_CONSTANTS = {
  CM_TO_INCHES,
  INCHES_PER_FOOT,
  KG_TO_LBS
};
