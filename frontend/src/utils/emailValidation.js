import axios from 'axios';

/**
 * Email validation utilities for the signup process
 */

/**
 * Validates email format using a regex pattern
 * @param {string} email - The email to validate
 * @returns {boolean} - True if email format is valid
 */
export const isValidEmailFormat = (email) => {
  if (!email || typeof email !== 'string') {
    return false;
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

/**
 * Checks if an email is already registered in the system
 * @param {string} email - The email to check
 * @returns {Promise<{exists: boolean, error: string|null}>} - Result of the check
 */
export const checkEmailExists = async (email) => {
  try {
    // First validate email format to avoid unnecessary API calls
    if (!isValidEmailFormat(email)) {
      return {
        exists: false,
        error: 'Invalid email format'
      };
    }

    const response = await axios.get('/api/auth/check-email', {
      params: { email: email.trim().toLowerCase() }
    });

    return {
      exists: response.data.exists,
      error: null
    };
  } catch (error) {
    console.error('Error checking email:', error);
    
    // Handle different types of errors
    if (error.response?.status === 400) {
      return {
        exists: false,
        error: error.response.data.error || 'Invalid email'
      };
    } else if (error.response?.status >= 500) {
      return {
        exists: false,
        error: 'Server error. Please try again later.'
      };
    } else if (error.code === 'NETWORK_ERROR' || !error.response) {
      return {
        exists: false,
        error: 'Network error. Please check your connection.'
      };
    }
    
    return {
      exists: false,
      error: 'Unable to validate email. Please try again.'
    };
  }
};

/**
 * Debounce function to limit API calls
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} - Debounced function
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};
