/**
 * Service Worker registration utility
 *
 * Note: This utility works alongside the Vite PWA plugin.
 * The plugin handles the actual registration, but we use this
 * to add additional functionality like update notifications.
 */

export function registerServiceWorker() {
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
      // The Vite PWA plugin will handle the actual registration
      // We just need to set up listeners for updates

      // Check if there's already a controller (service worker)
      if (navigator.serviceWorker.controller) {
        console.log('Service Worker is already controlling this page');

        // Set up message listener for updates from the Vite PWA plugin
        navigator.serviceWorker.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'VITE_PWA_UPDATE_AVAILABLE') {
            console.log('Update available from Vite PWA plugin');
            showUpdateNotification();
          }
        });
      }

      // Listen for new service worker taking control
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        console.log('Service Worker controller changed');
      });

      // Fallback registration in case Vite PWA plugin doesn't register
      // This shouldn't normally be needed, but it's a good safety net
      setTimeout(() => {
        if (!navigator.serviceWorker.controller) {
          console.log('No service worker controller found, attempting manual registration');
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker manually registered with scope:', registration.scope);

              // Set up update listener
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                console.log('Service Worker update found!');

                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    showUpdateNotification();
                  }
                });
              });
            })
            .catch(error => {
              console.error('Manual Service Worker registration failed:', error);
            });
        }
      }, 3000); // Wait 3 seconds to see if Vite PWA registers a service worker
    });
  } else {
    console.log('Service Workers are not supported in this browser');
  }
}

// Function to show update notification
function showUpdateNotification() {
  // Create notification element
  const notification = document.createElement('div');
  notification.className = 'fixed bottom-4 right-4 bg-primary-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center';
  notification.innerHTML = `
    <div class="mr-3">
      <p class="font-medium">App update available!</p>
      <p class="text-sm">Refresh to get the latest version.</p>
    </div>
    <button id="update-app" class="bg-white text-primary-600 px-4 py-2 rounded font-medium">
      Update
    </button>
  `;

  // Add to DOM
  document.body.appendChild(notification);

  // Add event listener to update button
  document.getElementById('update-app').addEventListener('click', () => {
    window.location.reload();
  });

  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 10000);
}

// Function to check if the app is installed
export function isAppInstalled() {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true;
}

// Function to handle offline status changes
export function setupOfflineListener(onOffline, onOnline) {
  window.addEventListener('online', () => {
    console.log('App is online');
    if (typeof onOnline === 'function') {
      onOnline();
    }
  });

  window.addEventListener('offline', () => {
    console.log('App is offline');
    if (typeof onOffline === 'function') {
      onOffline();
    }
  });

  // Initial check
  if (!navigator.onLine) {
    console.log('App is offline (initial check)');
    if (typeof onOffline === 'function') {
      onOffline();
    }
  }
}

// Function to request background sync
export function requestBackgroundSync(tag) {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    navigator.serviceWorker.ready
      .then(registration => {
        return registration.sync.register(tag);
      })
      .then(() => {
        console.log(`Background sync registered: ${tag}`);
        return true;
      })
      .catch(error => {
        console.error('Background sync registration failed:', error);
        return false;
      });
  } else {
    console.log('Background Sync is not supported');
    return false;
  }
}

// Function to store data for offline sync
export async function storeForOfflineSync(storeName, data, token) {
  if (!('indexedDB' in window)) {
    console.log('IndexedDB not supported');
    return false;
  }

  return new Promise((resolve, reject) => {
    const request = indexedDB.open('calcounta-offline', 1);

    request.onupgradeneeded = event => {
      const db = event.target.result;
      if (!db.objectStoreNames.contains(storeName)) {
        db.createObjectStore(storeName, { keyPath: 'id', autoIncrement: true });
      }
    };

    request.onsuccess = event => {
      const db = event.target.result;
      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);

      const item = {
        data: data,
        timestamp: new Date().getTime(),
        token: token
      };

      const addRequest = store.add(item);

      addRequest.onsuccess = () => {
        resolve(true);
      };

      addRequest.onerror = error => {
        console.error('Error storing data for offline sync:', error);
        reject(error);
      };
    };

    request.onerror = error => {
      console.error('Error opening IndexedDB:', error);
      reject(error);
    };
  });
}
