/**
 * Weight timeline calculation utilities for Calcounta
 */

// Standard weight loss/gain rates (per week)
const TRADITIONAL_DIET_RATES = {
  LOSE: 1.0, // 1 lb per week (conservative traditional approach)
  GAIN: 0.5, // 0.5 lb per week (conservative muscle gain)
};

const CALCOUNTA_RATES = {
  LOSE: 1.5, // 1.5 lbs per week (optimized AI approach)
  GAIN: 0.8, // 0.8 lbs per week (optimized muscle gain)
};

// Convert kg to lbs for calculations
const KG_TO_LBS = 2.20462;

/**
 * Calculate BMR (Basal Metabolic Rate) using Mifflin-St Jeor Equation
 * @param {string} gender - 'male' or 'female'
 * @param {number} weightKg - Weight in kilograms
 * @param {number} heightCm - Height in centimeters
 * @param {number} age - Age in years
 * @returns {number} - BMR in calories per day
 */
export const calculateBMR = (gender, weightKg, heightCm, age) => {
  if (!gender || !weightKg || !heightCm || !age) return 0;

  if (gender === 'male') {
    return 10 * weightKg + 6.25 * heightCm - 5 * age + 5;
  } else {
    return 10 * weightKg + 6.25 * heightCm - 5 * age - 161;
  }
};

/**
 * Calculate TDEE (Total Daily Energy Expenditure)
 * @param {number} bmr - Basal Metabolic Rate
 * @param {string} activityLevel - Activity level
 * @returns {number} - TDEE in calories per day
 */
export const calculateTDEE = (bmr, activityLevel) => {
  if (!bmr || !activityLevel) return bmr;

  const activityMultipliers = {
    'sedentary': 1.2,
    'light': 1.375,
    'moderate': 1.55,
    'active': 1.725,
    'very_active': 1.9
  };

  const multiplier = activityMultipliers[activityLevel] || 1.2;
  return bmr * multiplier;
};

/**
 * Calculate daily calorie goal based on user profile and goals
 * @param {string} gender - 'male' or 'female'
 * @param {number} heightCm - Height in centimeters
 * @param {number} weightKg - Weight in kilograms
 * @param {string|Date} dob - Date of birth
 * @param {string} activityLevel - Activity level
 * @param {string} goalType - 'lose', 'gain', or 'maintain'
 * @param {number} goalRate - Goal rate in kg per week (default 0.5)
 * @returns {number} - Daily calorie goal
 */
export const calculateDailyCalorieGoal = (gender, heightCm, weightKg, dob, activityLevel, goalType, goalRate = 0.5) => {
  if (!gender || !heightCm || !weightKg || !dob || !activityLevel || !goalType) {
    return 2000; // Default fallback
  }

  // Calculate age from date of birth
  const today = new Date();
  const birthDate = new Date(dob);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  // Calculate BMR and TDEE
  const bmr = calculateBMR(gender, weightKg, heightCm, age);
  const tdee = calculateTDEE(bmr, activityLevel);
  const maintenanceCalories = Math.round(tdee);

  // Adjust based on goal type and pace
  let dailyCalorieGoal;
  switch (goalType) {
    case 'lose':
      // Create a calorie deficit (1kg of fat is about 7700 calories)
      // goalRate is in kg/week, so multiply by 7700 and divide by 7 for daily deficit
      const dailyDeficit = Math.round((goalRate * 7700) / 7);
      dailyCalorieGoal = maintenanceCalories - dailyDeficit;
      break;
    case 'gain':
      // Create a calorie surplus
      const dailySurplus = Math.round((goalRate * 7700) / 7);
      dailyCalorieGoal = maintenanceCalories + dailySurplus;
      break;
    case 'maintain':
    default:
      dailyCalorieGoal = maintenanceCalories;
  }

  // Ensure minimum calorie intake (1200 for women, 1500 for men)
  const minCalories = gender === 'male' ? 1500 : 1200;
  dailyCalorieGoal = Math.max(dailyCalorieGoal, minCalories);

  return Math.round(dailyCalorieGoal);
};

/**
 * Calculate timeline for traditional diet approach
 * @param {number} currentWeight - Current weight in user's preferred unit
 * @param {number} targetWeight - Target weight in user's preferred unit
 * @param {string} goal - 'lose', 'gain', or 'maintain'
 * @param {string} unit - 'kg' or 'lbs'
 * @returns {number} - Timeline in weeks
 */
export const calculateTraditionalTimeline = (currentWeight, targetWeight, goal, unit = 'kg') => {
  if (!currentWeight || !targetWeight || goal === 'maintain') return 0;

  // Convert to lbs for calculation if needed
  const currentLbs = unit === 'kg' ? currentWeight * KG_TO_LBS : currentWeight;
  const targetLbs = unit === 'kg' ? targetWeight * KG_TO_LBS : targetWeight;
  
  const weightDifference = Math.abs(targetLbs - currentLbs);
  const ratePerWeek = goal === 'lose' ? TRADITIONAL_DIET_RATES.LOSE : TRADITIONAL_DIET_RATES.GAIN;
  
  return Math.ceil(weightDifference / ratePerWeek);
};

/**
 * Calculate timeline for Calcounta approach
 * @param {number} currentWeight - Current weight in user's preferred unit
 * @param {number} targetWeight - Target weight in user's preferred unit
 * @param {string} goal - 'lose', 'gain', or 'maintain'
 * @param {string} unit - 'kg' or 'lbs'
 * @param {number} userGoalRate - User's selected goal rate (optional)
 * @returns {number} - Timeline in weeks
 */
export const calculateCalcountaTimeline = (currentWeight, targetWeight, goal, unit = 'kg', userGoalRate = null) => {
  if (!currentWeight || !targetWeight || goal === 'maintain') return 0;

  // Convert to lbs for calculation if needed
  const currentLbs = unit === 'kg' ? currentWeight * KG_TO_LBS : currentWeight;
  const targetLbs = unit === 'kg' ? targetWeight * KG_TO_LBS : targetWeight;

  const weightDifference = Math.abs(targetLbs - currentLbs);

  // Use user's goal rate if available, otherwise use optimized AI rate
  let ratePerWeek;
  if (userGoalRate) {
    // Convert user goal rate to lbs if needed
    ratePerWeek = unit === 'kg' ? userGoalRate * KG_TO_LBS : userGoalRate;
  } else {
    ratePerWeek = goal === 'lose' ? CALCOUNTA_RATES.LOSE : CALCOUNTA_RATES.GAIN;
  }

  return Math.ceil(weightDifference / ratePerWeek);
};

/**
 * Calculate SVG path coordinates for weight journey visualization
 * @param {number} startWeight - Starting weight
 * @param {number} targetWeight - Target weight
 * @param {number} timelineWeeks - Timeline in weeks
 * @param {number} svgWidth - SVG container width
 * @param {number} svgHeight - SVG container height
 * @param {string} pathType - 'traditional' or 'calcounta'
 * @returns {string} - SVG path string
 */
export const calculateWeightJourneyPath = (
  startWeight,
  targetWeight,
  timelineWeeks,
  svgWidth = 350,
  svgHeight = 200,
  pathType = 'calcounta'
) => {
  const startX = 20;
  const endX = svgWidth - 20;
  const startY = svgHeight * 0.45; // Starting position (45% from top)
  
  // Calculate end Y position based on weight change
  const weightChange = targetWeight - startWeight;
  const isWeightLoss = weightChange < 0;
  
  // For weight loss, end point should be higher (lower Y value)
  // For weight gain, end point should be lower (higher Y value)
  const endY = isWeightLoss ? svgHeight * 0.2 : svgHeight * 0.8;
  
  // Create different path shapes for traditional vs Calcounta
  if (pathType === 'traditional') {
    // Traditional diet: more volatile path with ups and downs
    const midX1 = startX + (endX - startX) * 0.3;
    const midY1 = startY; // Stay at start level initially
    const midX2 = startX + (endX - startX) * 0.6;
    const midY2 = isWeightLoss ? svgHeight * 0.15 : svgHeight * 0.85; // Extreme point
    const midX3 = startX + (endX - startX) * 0.8;
    const midY3 = isWeightLoss ? svgHeight * 0.35 : svgHeight * 0.65; // Rebound

    return `M ${startX},${startY} C ${midX1},${midY1} ${midX2},${midY2} ${midX3},${midY3} C ${midX3},${midY3} ${endX},${endY} ${endX},${endY}`;
  } else {
    // Calcounta: smoother, more consistent path
    const midX1 = startX + (endX - startX) * 0.4;
    const midY1 = startY + (endY - startY) * 0.3;
    const midX2 = startX + (endX - startX) * 0.7;
    const midY2 = startY + (endY - startY) * 0.7;

    return `M ${startX},${startY} C ${midX1},${midY1} ${midX2},${midY2} ${endX},${endY}`;
  }
};

/**
 * Format timeline for display
 * @param {number} weeks - Timeline in weeks
 * @returns {string} - Formatted timeline string
 */
export const formatTimeline = (weeks) => {
  if (weeks === 0) return 'Maintain current';
  if (weeks < 4) return `${weeks} week${weeks > 1 ? 's' : ''}`;
  
  const months = Math.round(weeks / 4.33); // Average weeks per month
  if (months === 1) return '1 month';
  if (months < 12) return `${months} months`;
  
  const years = Math.round(months / 12);
  return `${years} year${years > 1 ? 's' : ''}`;
};

/**
 * Get weight difference for display
 * @param {number} currentWeight - Current weight
 * @param {number} targetWeight - Target weight
 * @param {string} unit - Weight unit
 * @returns {string} - Formatted weight difference
 */
export const getWeightDifference = (currentWeight, targetWeight, unit) => {
  if (!currentWeight || !targetWeight) return '';
  
  const diff = Math.abs(targetWeight - currentWeight);
  return `${diff.toFixed(1)} ${unit}`;
};
