/* Animation styles for Calcounta app */

/* ===== ONBOARDING ANIMATIONS ===== */

/* Page Transitions */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(-30px);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(30px);
  }
}

/* Text Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blinkCursor {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Component Animations */
@keyframes staggerFadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button Animations */
@keyframes buttonPress {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.98);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes buttonHover {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-2px);
  }
}

/* Progress Animations */
@keyframes progressFill {
  from {
    width: 0%;
  }
  to {
    width: var(--progress-width);
  }
}

/* Legacy animations (keeping for compatibility) */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out forwards;
}

@keyframes scaleUp {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.05);
  }
}

.animate-scaleUp {
  animation: scaleUp 0.3s ease-in-out forwards;
}

@keyframes scaleDown {
  from {
    transform: scale(1.05);
  }
  to {
    transform: scale(1);
  }
}

.animate-scaleDown {
  animation: scaleDown 0.3s ease-in-out forwards;
}

/* iOS Spinner Animation */
@keyframes iosSpinnerRotate {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.15;
  }
}

/* Global Loading Indicator Animations */
@keyframes globalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-globalFadeIn {
  animation: globalFadeIn 0.3s ease-in-out forwards;
}

/* Macro Icons Pulse Animation */
@keyframes macroPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.animate-macroPulse {
  animation: macroPulse 2s ease-in-out infinite;
}

/* Glow effect for loading indicator */
@keyframes glow {
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.4;
  }
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Weight Journey Path Animations */
@keyframes drawPath {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

.animate-drawPath {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: drawPath 2s ease-in-out forwards;
}

.animate-drawPath-delayed {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: drawPath 2s ease-in-out 0.5s forwards;
}

/* Circle animations for weight journey */
@keyframes circleAppear {
  0% {
    opacity: 0;
    transform: scale(0);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-circleAppear {
  animation: circleAppear 0.5s ease-out forwards;
}

.animate-circleAppear-delayed {
  animation: circleAppear 0.5s ease-out 2.5s forwards;
}

/* Label fade in animations */
@keyframes labelFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-labelFadeIn {
  animation: labelFadeIn 0.5s ease-out 1s forwards;
}

.animate-labelFadeIn-delayed {
  animation: labelFadeIn 0.5s ease-out 3s forwards;
}

/* ===== ONBOARDING UTILITY CLASSES ===== */

/* Page Transition Classes */
.animate-slideInRight {
  animation: slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slideInLeft {
  animation: slideInLeft 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slideOutLeft {
  animation: slideOutLeft 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

.animate-slideOutRight {
  animation: slideOutRight 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19) forwards;
}

/* Text Animation Classes */
.animate-fadeInUp {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-fadeInUp-delay-100 {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s forwards;
  opacity: 0;
}

.animate-fadeInUp-delay-200 {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
  opacity: 0;
}

.animate-fadeInUp-delay-300 {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
  opacity: 0;
}

.animate-fadeInDown {
  animation: fadeInDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-typewriter {
  animation: typewriter 2s steps(40, end) forwards, blinkCursor 0.75s step-end infinite;
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid;
  width: 0;
}

/* Component Animation Classes */
.animate-staggerFadeIn {
  animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-staggerFadeIn-delay-100 {
  animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s forwards;
  opacity: 0;
}

.animate-staggerFadeIn-delay-200 {
  animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
  opacity: 0;
}

.animate-staggerFadeIn-delay-300 {
  animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
  opacity: 0;
}

.animate-staggerFadeIn-delay-400 {
  animation: staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s forwards;
  opacity: 0;
}

.animate-bounceIn {
  animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-slideUp {
  animation: slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Button Animation Classes */
.animate-buttonPress {
  animation: buttonPress 0.15s ease-in-out;
}

.animate-buttonHover {
  animation: buttonHover 0.2s ease-out forwards;
}

/* Progress Animation Classes */
.animate-progressFill {
  animation: progressFill 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Typewriter Animation Classes */
.animate-typewriter-fast {
  animation: typewriter 1.5s steps(30, end) forwards, blink 0.75s step-end infinite;
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid;
  width: 0;
}

.animate-typewriter-slow {
  animation: typewriter 3s steps(50, end) forwards, blink 0.75s step-end infinite;
  overflow: hidden;
  white-space: nowrap;
  border-right: 2px solid;
  width: 0;
}

.typewriter-cursor {
  animation: blink 1s infinite;
}

.typewriter-no-cursor {
  border-right: none !important;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .animate-slideInRight,
  .animate-slideInLeft,
  .animate-slideOutLeft,
  .animate-slideOutRight,
  .animate-fadeInUp,
  .animate-fadeInUp-delay-100,
  .animate-fadeInUp-delay-200,
  .animate-fadeInUp-delay-300,
  .animate-fadeInDown,
  .animate-staggerFadeIn,
  .animate-staggerFadeIn-delay-100,
  .animate-staggerFadeIn-delay-200,
  .animate-staggerFadeIn-delay-300,
  .animate-staggerFadeIn-delay-400,
  .animate-bounceIn,
  .animate-slideUp,
  .animate-buttonHover,
  .animate-progressFill {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }

  .animate-typewriter,
  .animate-typewriter-fast,
  .animate-typewriter-slow {
    animation: none !important;
    width: 100% !important;
    border-right: none !important;
    overflow: visible !important;
    white-space: normal !important;
  }

  .typewriter-cursor {
    animation: none !important;
    opacity: 0 !important;
  }
}
