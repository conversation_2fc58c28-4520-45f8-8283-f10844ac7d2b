/* Safe area utilities for iOS devices */

/* Padding that respects the safe area insets */
.safe-area-padding-top {
  padding-top: env(safe-area-inset-top, 0);
}

.safe-area-padding-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.safe-area-padding-left {
  padding-left: env(safe-area-inset-left, 0);
}

.safe-area-padding-right {
  padding-right: env(safe-area-inset-right, 0);
}

.safe-area-padding {
  padding-top: env(safe-area-inset-top, 0);
  padding-bottom: env(safe-area-inset-bottom, 0);
  padding-left: env(safe-area-inset-left, 0);
  padding-right: env(safe-area-inset-right, 0);
}

/* Margin that respects the safe area insets */
.safe-area-margin-top {
  margin-top: env(safe-area-inset-top, 0);
}

.safe-area-margin-bottom {
  margin-bottom: env(safe-area-inset-bottom, 0);
}

.safe-area-margin-left {
  margin-left: env(safe-area-inset-left, 0);
}

.safe-area-margin-right {
  margin-right: env(safe-area-inset-right, 0);
}

/* Inset that respects the safe area insets */
.safe-area-inset-top {
  top: env(safe-area-inset-top, 0);
}

.safe-area-inset-bottom {
  bottom: env(safe-area-inset-bottom, 0);
}

.safe-area-inset-left {
  left: env(safe-area-inset-left, 0);
}

.safe-area-inset-right {
  right: env(safe-area-inset-right, 0);
}

/* Full-bleed utilities that extend to the edge of the screen */
.full-bleed {
  width: 100vw;
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
}

/* Full-screen utilities that cover the entire viewport */
.full-screen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Full-screen utilities that cover the entire viewport including safe areas */
.full-screen-cover {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* For non-scanner pages, add padding for the status bar area on iOS */
  /* For scanner pages, this is overridden in NotchAreaManager.jsx */
  padding-top: env(safe-area-inset-top, 0);
}

/* Specific utility for iOS notch area */
.ios-notch-cover {
  background-color: inherit;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: env(safe-area-inset-top, 0);
  z-index: 50;
  transition: background-color 0.3s ease;
}

/* Light and dark mode specific notch covers */
.ios-notch-cover-light {
  background-color: white;
}

.ios-notch-cover-dark {
  background-color: #000000; /* black for iOS dark mode */
}

/* Scanner-specific notch cover */
.ios-notch-cover-scanner {
  background-color: rgba(0, 0, 0, 0.25) !important;
  opacity: 1 !important;
  z-index: 9999 !important;
  width: 100% !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: env(safe-area-inset-top, 0) !important;
  pointer-events: none !important;
}

/* Ensure scanner elements extend to cover the notch area */
.scanner-viewfinder-overlay {
  top: calc(-1 * env(safe-area-inset-top, 0)) !important;
  height: calc(100% + env(safe-area-inset-top, 0)) !important;
  width: 100% !important;
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
}

/* Ensure video and captured image extend to cover the notch area */
.scanner-video, .scanner-image {
  height: calc(100% + env(safe-area-inset-top, 0)) !important;
  top: calc(-1 * env(safe-area-inset-top, 0)) !important;
  width: 100% !important;
  position: absolute !important;
  left: 0 !important;
  right: 0 !important;
}

/* Specific styling for the scanner container */
.scanner-container {
  top: 0 !important;
  height: 100% !important;
  padding-top: 0 !important;
  margin-top: 0 !important;
  width: 100% !important;
  position: relative !important;
  left: 0 !important;
  right: 0 !important;
}
