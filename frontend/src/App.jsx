import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './hooks/useAuth'
import { useTheme } from './context/ThemeContext'
import { useLoading } from './context/LoadingContext'
import { useEffect, useState } from 'react'

// Pages
import Login from './pages/Login'
import Register from './pages/Register'
import Onboarding from './pages/Onboarding'
import Dashboard from './pages/Dashboard'
import AddMeal from './pages/AddMeal'
import Profile from './pages/Profile'
import BMI from './pages/BMI'
import Settings from './pages/Settings'
import NotFound from './pages/NotFound'
import PwaInfo from './pages/PwaInfo'


// Layout components
import Layout from './components/Layout'
import ProtectedRoute from './components/ProtectedRoute'
import OfflineIndicator from './components/ui/OfflineIndicator'
import PwaInstallPrompt from './components/ui/PwaInstallPrompt'
import NotchAreaManager from './components/ui/NotchAreaManager'
import GlobalLoadingIndicator from './components/ui/GlobalLoadingIndicator'
import ErrorBoundary from './components/ui/ErrorBoundary'
import { AddOptionsProvider } from './context/AddOptionsContext'

function App() {
  const { user, loading } = useAuth()
  const { isDarkMode } = useTheme()
  const { setLoading } = useLoading()

  // Apply dark mode class to body
  useEffect(() => {
    if (isDarkMode) {
      document.body.classList.add('dark-mode')
      document.documentElement.classList.add('dark')
    } else {
      document.body.classList.remove('dark-mode')
      document.documentElement.classList.remove('dark')
    }
  }, [isDarkMode])

  // Set auth loading state in global loading context
  useEffect(() => {
    setLoading('auth', loading, loading ? 'Authenticating...' : '')
  }, [loading, setLoading])

  return (
    <AddOptionsProvider>
      <OfflineIndicator />
      <PwaInstallPrompt />
      <NotchAreaManager />
      <GlobalLoadingIndicator />
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={user ? <Navigate to="/" /> : <Login />} />
        <Route path="/register" element={user ? <Navigate to="/" /> : <Register />} />

        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          {/* Routes with Layout */}
          <Route element={<Layout />}>
            <Route path="/" element={<ErrorBoundary><Dashboard /></ErrorBoundary>} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/bmi" element={<BMI />} />
            <Route path="/pwa-info" element={<PwaInfo />} />
          </Route>

          {/* Routes without Layout */}
          <Route path="/onboarding" element={<Onboarding />} />
          <Route path="/add-meal" element={<AddMeal />} />
          <Route path="/settings" element={<Settings />} />

        </Route>

        {/* 404 route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </AddOptionsProvider>
  )
}

export default App
