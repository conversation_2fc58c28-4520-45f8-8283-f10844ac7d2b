import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import { useAuth } from '../hooks/useAuth'
import { useGlobalLoading } from '../hooks/useGlobalLoading'

// Import onboarding components from the calorie-quest-onboarding-flow
import { OnboardingProvider } from '../context/OnboardingContext'
import OnboardingFlow from '../components/onboarding/OnboardingFlow'

const Onboarding = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const globalLoading = useGlobalLoading()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [userProfile, setUserProfile] = useState(null)

  useEffect(() => {
    // Check if user has already completed onboarding
    const checkOnboardingStatus = async () => {
      try {
        const response = await axios.get('/api/users/profile')
        setUserProfile(response.data)

        // If user has already set their goal type, they've completed onboarding
        if (response.data.goal_type) {
          navigate('/')
        }

        setLoading(false)
      } catch (error) {
        console.error('Error checking onboarding status:', error)
        setError('Failed to load user profile')
        setLoading(false)
      }
    }

    checkOnboardingStatus()
  }, [user, navigate])

  const handleOnboardingComplete = async (onboardingData) => {
    try {
      setLoading(true)
      globalLoading.savingData()

      // Convert onboarding data to API format
      const profileData = {
        gender: onboardingData.gender,
        heightCm: onboardingData.unitSystem === 'metric'
          ? onboardingData.height.cm
          : Math.round((onboardingData.height.ft * 30.48) + (onboardingData.height.in * 2.54)),
        weightKg: onboardingData.unitSystem === 'metric'
          ? onboardingData.weight.kg
          : Math.round(onboardingData.weight.lbs * 0.453592),
        goalType: onboardingData.goal,
        targetWeight: onboardingData.unitSystem === 'metric'
          ? onboardingData.targetWeight.kg
          : Math.round(onboardingData.targetWeight.lbs * 0.453592),
        activityLevel: mapWorkoutFrequencyToActivityLevel(onboardingData.workoutFrequency),
        dob: `${onboardingData.birthDate.year}-${String(onboardingData.birthDate.month).padStart(2, '0')}-${String(onboardingData.birthDate.day).padStart(2, '0')}`
      }

      console.log('Sending profile data to backend:', profileData);

      // Update user profile
      await axios.put('/api/users/profile', profileData)

      // Note: Initial weight log is now created during user registration
      // No need to create it again here to avoid duplicates

      // Create initial goal
      // Calculate daily calorie goal based on user profile and goal
      const dailyCalorieGoal = calculateDailyCalorieGoal(
        profileData.gender,
        profileData.heightCm,
        profileData.weightKg,
        profileData.dob,
        profileData.activityLevel,
        profileData.goalType,
        onboardingData.goalRate || 0.5
      );

      const goalData = {
        targetChange: calculateTargetChange(profileData.weightKg, profileData.targetWeight),
        pace: onboardingData.goalRate || 0.5, // Default to 0.5 kg/week if not set
        dailyCalorieGoal: parseInt(dailyCalorieGoal) // Ensure it's an integer
      }

      console.log('Sending goal data to backend:', goalData);

      try {
        const response = await axios.put('/api/goals/current', goalData);
        console.log('Goal creation response:', response.data);
      } catch (error) {
        console.error('Error creating goal:', error.response?.data || error.message);
        throw error;
      }

      // Navigate to dashboard
      globalLoading.stop.data()
      navigate('/')
    } catch (error) {
      console.error('Error saving onboarding data:', error)
      setError('Failed to save your information. Please try again.')
      globalLoading.stop.data()
    } finally {
      setLoading(false)
    }
  }

  // Helper function to map workout frequency to activity level
  const mapWorkoutFrequencyToActivityLevel = (frequency) => {
    switch (frequency) {
      case 'none': return 'sedentary'
      case '1-3': return 'light'
      case '4-6': return 'moderate'
      case '7+': return 'active'
      default: return 'moderate'
    }
  }

  // Helper function to calculate target change in kg
  const calculateTargetChange = (currentWeight, targetWeight) => {
    return targetWeight - currentWeight
  }

  // Helper function to calculate daily calorie goal
  const calculateDailyCalorieGoal = (gender, heightCm, weightKg, dob, activityLevel, goalType, pace) => {
    // Calculate age from date of birth
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Calculate BMR (Basal Metabolic Rate) using Mifflin-St Jeor Equation
    let bmr;
    if (gender === 'male') {
      bmr = 10 * weightKg + 6.25 * heightCm - 5 * age + 5;
    } else {
      bmr = 10 * weightKg + 6.25 * heightCm - 5 * age - 161;
    }

    // Apply activity multiplier
    let activityMultiplier;
    switch (activityLevel) {
      case 'sedentary': activityMultiplier = 1.2; break;
      case 'light': activityMultiplier = 1.375; break;
      case 'moderate': activityMultiplier = 1.55; break;
      case 'active': activityMultiplier = 1.725; break;
      default: activityMultiplier = 1.375; // Default to light activity
    }

    const maintenanceCalories = Math.round(bmr * activityMultiplier);

    // Adjust based on goal type and pace
    let dailyCalorieGoal;
    switch (goalType) {
      case 'lose':
        // Create a calorie deficit (1kg of fat is about 7700 calories)
        // pace is in kg/week, so multiply by 7700 and divide by 7 for daily deficit
        const dailyDeficit = Math.round((pace * 7700) / 7);
        dailyCalorieGoal = maintenanceCalories - dailyDeficit;
        break;
      case 'gain':
        // Create a calorie surplus
        const dailySurplus = Math.round((pace * 7700) / 7);
        dailyCalorieGoal = maintenanceCalories + dailySurplus;
        break;
      case 'maintain':
      default:
        dailyCalorieGoal = maintenanceCalories;
    }

    // Ensure the calorie goal is not too low (minimum 1200 for women, 1500 for men)
    const minCalories = gender === 'male' ? 1500 : 1200;
    return Math.max(dailyCalorieGoal, minCalories);
  }

  // Loading is now handled by global loading indicator

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
          {error}
        </div>
        <button
          onClick={() => window.location.reload()}
          className="btn btn-primary"
        >
          Try Again
        </button>
      </div>
    )
  }

  return (
    <OnboardingProvider>
      <OnboardingFlow onComplete={handleOnboardingComplete} />
    </OnboardingProvider>
  )
}

export default Onboarding
