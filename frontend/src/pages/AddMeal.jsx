import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { useIsMobile } from '../hooks/useIsMobile';
import BottomNavigation from '../components/ui/BottomNavigation';
import FoodScanner from '../components/scanner/FoodScanner';
import { useAddOptionsContext } from '../context/AddOptionsContext';
import { formatLocalDateToISO, logDateInfo } from '../utils/dateUtils';

const AddMeal = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const { openAddOptions } = useAddOptionsContext();
  // Log current date information for debugging
  useEffect(() => {
    logDateInfo(new Date(), 'AddMeal Component Initialization');
  }, []);

  const [formData, setFormData] = useState({
    name: '',
    calories: '',
    protein_g: '',
    carbs_g: '',
    fats_g: '',
    fiber_g: '',
    sugar_g: '',
    sodium_mg: '',
    cholesterol_mg: '',
    saturated_fat_g: '',
    trans_fat_g: '',
    vitamin_c_mg: '',
    calcium_mg: '',
    iron_mg: '',
    potassium_mg: '',
    vitamin_d_mcg: '',
    serving_size: 1,
    items: [],
    timestamp: formatLocalDateToISO(),
    image_url: '',
  });

  // Check if we have analyzed food data from the scanner
  useEffect(() => {
    if (location.state?.analyzedFood) {
      const { analyzedFood } = location.state;
      console.log('Received analyzed food data:', analyzedFood);

      // Check if we have a server image URL or a blob URL
      const isServerImage = analyzedFood.is_server_image || false;
      console.log('Is server image:', isServerImage);
      console.log('Image URL:', analyzedFood.image_url);

      setFormData({
        name: analyzedFood.name || '',
        calories: analyzedFood.calories || '',
        protein_g: analyzedFood.protein_g || '',
        carbs_g: analyzedFood.carbs_g || '',
        fats_g: analyzedFood.fats_g || '',
        fiber_g: analyzedFood.fiber_g || '',
        sugar_g: analyzedFood.sugar_g || '',
        sodium_mg: analyzedFood.sodium_mg || '',
        cholesterol_mg: analyzedFood.cholesterol_mg || '',
        saturated_fat_g: analyzedFood.saturated_fat_g || '',
        trans_fat_g: analyzedFood.trans_fat_g || '',
        vitamin_c_mg: analyzedFood.vitamin_c_mg || '',
        calcium_mg: analyzedFood.calcium_mg || '',
        iron_mg: analyzedFood.iron_mg || '',
        potassium_mg: analyzedFood.potassium_mg || '',
        vitamin_d_mcg: analyzedFood.vitamin_d_mcg || '',
        serving_size: analyzedFood.serving_size || 1,
        items: analyzedFood.items || [],
        timestamp: formatLocalDateToISO(), // Use the utility function to ensure local date
        image_url: analyzedFood.image_url || '',
        is_server_image: isServerImage,
      });
    }
  }, [location.state]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // List of numeric fields that should be parsed as floats
    const numericFields = [
      'calories', 'protein_g', 'carbs_g', 'fats_g', 'fiber_g', 'sugar_g',
      'sodium_mg', 'cholesterol_mg', 'saturated_fat_g', 'trans_fat_g',
      'vitamin_c_mg', 'calcium_mg', 'iron_mg', 'potassium_mg', 'vitamin_d_mcg',
      'serving_size'
    ];

    setFormData({
      ...formData,
      [name]: numericFields.includes(name)
        ? parseFloat(value) || ''
        : value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      // Validate form data
      if (!formData.name || !formData.calories) {
        setError('Name and calories are required');
        setLoading(false);
        return;
      }

      // Prepare meal data
      const mealData = {
        name: formData.name,
        calories: parseFloat(formData.calories),
        proteinG: formData.protein_g ? parseFloat(formData.protein_g) : 0,
        carbsG: formData.carbs_g ? parseFloat(formData.carbs_g) : 0,
        fatsG: formData.fats_g ? parseFloat(formData.fats_g) : 0,
        fiberG: formData.fiber_g ? parseFloat(formData.fiber_g) : null,
        sugarG: formData.sugar_g ? parseFloat(formData.sugar_g) : null,
        sodiumMg: formData.sodium_mg ? parseFloat(formData.sodium_mg) : null,
        cholesterolMg: formData.cholesterol_mg ? parseFloat(formData.cholesterol_mg) : null,
        saturatedFatG: formData.saturated_fat_g ? parseFloat(formData.saturated_fat_g) : null,
        transFatG: formData.trans_fat_g ? parseFloat(formData.trans_fat_g) : null,
        vitaminCMg: formData.vitamin_c_mg ? parseFloat(formData.vitamin_c_mg) : null,
        calciumMg: formData.calcium_mg ? parseFloat(formData.calcium_mg) : null,
        ironMg: formData.iron_mg ? parseFloat(formData.iron_mg) : null,
        potassiumMg: formData.potassium_mg ? parseFloat(formData.potassium_mg) : null,
        vitaminDMcg: formData.vitamin_d_mcg ? parseFloat(formData.vitamin_d_mcg) : null,
        // Ensure servingSizeG is always a valid number (required by the backend)
        servingSizeG: formData.serving_size ? parseFloat(formData.serving_size) : 1,
        // Handle image URLs - use the URL directly if it's a server URL, otherwise set to null
        imageUrl: formData.is_server_image ? formData.image_url : null,
        timestamp: formData.timestamp
      };

      // Debug log for serving size specifically
      console.log('===== SERVING SIZE DEBUG =====');
      console.log('Original serving_size:', formData.serving_size);
      console.log('Parsed servingSizeG:', mealData.servingSizeG);
      console.log('Type of servingSizeG:', typeof mealData.servingSizeG);

      // Debug log for image URL
      console.log('===== IMAGE URL DEBUG =====');
      console.log('Original image_url:', formData.image_url);
      console.log('Is server image:', formData.is_server_image);
      console.log('Processed imageUrl:', mealData.imageUrl);
      console.log('Is blob URL:', formData.image_url && formData.image_url.startsWith('blob:'));

      // Debug log for timestamp
      console.log('===== TIMESTAMP DEBUG =====');
      console.log('Original timestamp:', formData.timestamp);
      console.log('Timestamp in meal data:', mealData.timestamp);
      console.log('Local date from timestamp:', new Date(mealData.timestamp).toLocaleString());
      console.log('UTC date from timestamp:', new Date(mealData.timestamp).toUTCString());

      // Enhanced logging for debugging
      console.log('===== ADD MEAL REQUEST =====');
      console.log('Sending meal data to API:', JSON.stringify(mealData, null, 2));
      console.log('Request headers:', JSON.stringify(axios.defaults.headers.common, null, 2));

      try {
        // Submit the meal with detailed logging
        const response = await axios.post('/api/meals', mealData);
        console.log('===== ADD MEAL RESPONSE =====');
        console.log('Status:', response.status);
        console.log('Response data:', JSON.stringify(response.data, null, 2));

        // Navigate back to dashboard
        navigate('/');
      } catch (apiError) {
        console.error('===== ADD MEAL ERROR =====');
        console.error('Status:', apiError.response?.status);
        console.error('Error data:', JSON.stringify(apiError.response?.data, null, 2));
        console.error('Error details:', apiError.message);
        throw apiError; // Re-throw to be caught by the outer catch block
      }
    } catch (error) {
      console.error('Error adding meal:', error);
      setError('Failed to add meal. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div className="bg-gray-50 min-h-screen pb-20">
      {/* Header */}
      <header className="flex justify-between items-center p-4">
        <button
          onClick={() => navigate('/')}
          className="flex items-center text-gray-600"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </button>
        <h1 className="text-xl font-bold">Add Meal</h1>
        <div className="w-10"></div> {/* Spacer for alignment */}
      </header>

      {/* Content */}
      <div className={`${isMobile ? 'px-4' : 'px-8 py-4 max-w-2xl mx-auto'}`}>
        <div className="bg-white p-6 rounded-xl shadow-sm">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Image preview if available */}
            {formData.image_url && (
              <div className="mb-4">
                <div className="relative w-full h-48 rounded-xl overflow-hidden">
                  <img
                    src={formData.image_url}
                    alt="Food preview"
                    className="w-full h-full object-cover"
                  />
                  <button
                    type="button"
                    onClick={() => setFormData({...formData, image_url: ''})}
                    className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full"
                    aria-label="Remove image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Meal Name
              </label>
              <input
                id="name"
                name="name"
                type="text"
                required
                value={formData.name}
                onChange={handleChange}
                className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                placeholder="e.g., Grilled Vegetables"
              />
            </div>

            <div>
              <label htmlFor="calories" className="block text-sm font-medium text-gray-700 mb-1">
                Calories
              </label>
              <input
                id="calories"
                name="calories"
                type="number"
                required
                value={formData.calories}
                onChange={handleChange}
                className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                placeholder="e.g., 350"
              />
            </div>

            {/* Main Macronutrients */}
            <div className="grid grid-cols-3 gap-4">
              <div>
                <label htmlFor="protein_g" className="block text-sm font-medium text-gray-700 mb-1">
                  Protein (g)
                </label>
                <input
                  id="protein_g"
                  name="protein_g"
                  type="number"
                  value={formData.protein_g}
                  onChange={handleChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                  placeholder="e.g., 25"
                />
              </div>

              <div>
                <label htmlFor="carbs_g" className="block text-sm font-medium text-gray-700 mb-1">
                  Carbs (g)
                </label>
                <input
                  id="carbs_g"
                  name="carbs_g"
                  type="number"
                  value={formData.carbs_g}
                  onChange={handleChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                  placeholder="e.g., 30"
                />
              </div>

              <div>
                <label htmlFor="fats_g" className="block text-sm font-medium text-gray-700 mb-1">
                  Fats (g)
                </label>
                <input
                  id="fats_g"
                  name="fats_g"
                  type="number"
                  value={formData.fats_g}
                  onChange={handleChange}
                  className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                  placeholder="e.g., 15"
                />
              </div>
            </div>

            {/* Additional Nutritional Information */}
            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-700 mb-4">Additional Nutritional Information</h3>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="fiber_g" className="block text-sm font-medium text-gray-700 mb-1">
                    Fiber (g)
                  </label>
                  <input
                    id="fiber_g"
                    name="fiber_g"
                    type="number"
                    value={formData.fiber_g}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 5"
                  />
                </div>

                <div>
                  <label htmlFor="sugar_g" className="block text-sm font-medium text-gray-700 mb-1">
                    Sugar (g)
                  </label>
                  <input
                    id="sugar_g"
                    name="sugar_g"
                    type="number"
                    value={formData.sugar_g}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 10"
                  />
                </div>

                <div>
                  <label htmlFor="sodium_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Sodium (mg)
                  </label>
                  <input
                    id="sodium_mg"
                    name="sodium_mg"
                    type="number"
                    value={formData.sodium_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 500"
                  />
                </div>

                <div>
                  <label htmlFor="cholesterol_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Cholesterol (mg)
                  </label>
                  <input
                    id="cholesterol_mg"
                    name="cholesterol_mg"
                    type="number"
                    value={formData.cholesterol_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 50"
                  />
                </div>

                <div>
                  <label htmlFor="saturated_fat_g" className="block text-sm font-medium text-gray-700 mb-1">
                    Saturated Fat (g)
                  </label>
                  <input
                    id="saturated_fat_g"
                    name="saturated_fat_g"
                    type="number"
                    value={formData.saturated_fat_g}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 5"
                  />
                </div>

                <div>
                  <label htmlFor="trans_fat_g" className="block text-sm font-medium text-gray-700 mb-1">
                    Trans Fat (g)
                  </label>
                  <input
                    id="trans_fat_g"
                    name="trans_fat_g"
                    type="number"
                    value={formData.trans_fat_g}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 0"
                  />
                </div>
              </div>
            </div>

            {/* Vitamins and Minerals */}
            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-700 mb-4">Vitamins and Minerals</h3>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="vitamin_c_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Vitamin C (mg)
                  </label>
                  <input
                    id="vitamin_c_mg"
                    name="vitamin_c_mg"
                    type="number"
                    value={formData.vitamin_c_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 30"
                  />
                </div>

                <div>
                  <label htmlFor="calcium_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Calcium (mg)
                  </label>
                  <input
                    id="calcium_mg"
                    name="calcium_mg"
                    type="number"
                    value={formData.calcium_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 200"
                  />
                </div>

                <div>
                  <label htmlFor="iron_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Iron (mg)
                  </label>
                  <input
                    id="iron_mg"
                    name="iron_mg"
                    type="number"
                    value={formData.iron_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 2"
                  />
                </div>

                <div>
                  <label htmlFor="potassium_mg" className="block text-sm font-medium text-gray-700 mb-1">
                    Potassium (mg)
                  </label>
                  <input
                    id="potassium_mg"
                    name="potassium_mg"
                    type="number"
                    value={formData.potassium_mg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 400"
                  />
                </div>

                <div>
                  <label htmlFor="vitamin_d_mcg" className="block text-sm font-medium text-gray-700 mb-1">
                    Vitamin D (mcg)
                  </label>
                  <input
                    id="vitamin_d_mcg"
                    name="vitamin_d_mcg"
                    type="number"
                    value={formData.vitamin_d_mcg}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 5"
                  />
                </div>

                <div>
                  <label htmlFor="serving_size" className="block text-sm font-medium text-gray-700 mb-1">
                    Serving Size
                  </label>
                  <input
                    id="serving_size"
                    name="serving_size"
                    type="number"
                    value={formData.serving_size}
                    onChange={handleChange}
                    className="appearance-none block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black"
                    placeholder="e.g., 1"
                  />
                </div>
              </div>
            </div>

            {/* Food items section - only show if items exist */}
            {formData.items && formData.items.length > 0 && (
              <div className="mt-4">
                <h3 className="text-md font-medium text-gray-700 mb-2">Detected Food Items:</h3>
                <div className="bg-gray-50 rounded-xl p-4">
                  <ul className="divide-y divide-gray-200">
                    {formData.items.map((item, index) => (
                      <li key={index} className="py-3 flex justify-between">
                        <div className="text-sm">
                          <p className="font-medium text-gray-800">{item.name}</p>
                          <p className="text-gray-500">{item.portion}</p>
                        </div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.calories} cal
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className={`w-full py-4 px-6 font-bold rounded-xl transition-all duration-300 bg-black text-white hover:bg-opacity-90 ${
                  loading ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {loading ? 'Adding...' : 'Add Meal'}
              </button>
            </div>
          </form>
        </div>
      </div>

      {/* Scanner button - only on mobile */}
      {isMobile && (
        <div className="fixed bottom-24 right-6 z-40">
          <button
            onClick={() => setIsScannerOpen(true)}
            className="bg-black text-white w-14 h-14 rounded-full flex items-center justify-center shadow-lg"
            aria-label="Scan food"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
      )}

      {/* Bottom Navigation */}
      <BottomNavigation onAddClick={openAddOptions} />

      {/* Food Scanner Modal */}
      <FoodScanner
        isOpen={isScannerOpen}
        onClose={() => setIsScannerOpen(false)}
      />
    </div>
  );
};

export default AddMeal;
