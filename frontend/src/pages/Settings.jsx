import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { useIsMobile } from '../hooks/useIsMobile';
import { useTheme } from '../context/ThemeContext';
import { useGlobalLoading } from '../hooks/useGlobalLoading';
import BottomNavigation from '../components/ui/BottomNavigation';
import ApiConfigurationSettings from '../components/settings/ApiConfigurationSettings';
import { fetchConfig } from '../services/configService';
import {
  getHeightUnitLabel,
  getWeightUnitLabel,
  convertWeight,
  getHeightInputValue,
  getWeightDisplayValue
} from '../utils/unitConversion';
import { calculateDailyCalorieGoal } from '../utils/weightCalculations';

const Settings = () => {
  const { logout } = useAuth();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const globalLoading = useGlobalLoading();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [profile, setProfile] = useState({
    email: '',
    gender: '',
    dob: '',
    height_cm: '',
    weight_kg: '',
    goal_type: '',
    target_weight: '',
    activity_level: '',
    height_unit: 'cm',
    weight_unit: 'kg'
  });
  const [config, setConfig] = useState(null);

  // Track if weight was modified by user
  const [weightModified, setWeightModified] = useState(false);

  // Helper functions to get display values
  const getDisplayHeight = () => {
    if (!profile.height_cm) return '';
    return getHeightInputValue(profile.height_cm, profile.height_unit);
  };

  const getDisplayWeight = () => {
    if (!profile.weight_kg) return '';
    return getWeightDisplayValue(profile.weight_kg, profile.weight_unit);
  };

  const getDisplayTargetWeight = () => {
    if (!profile.target_weight) return '';
    return getWeightDisplayValue(profile.target_weight, profile.weight_unit);
  };

  // Function to recalculate and update calorie goal when weight changes
  const updateCalorieGoal = async (newWeightKg) => {
    try {
      // Only recalculate if we have all required data
      if (!profile.gender || !profile.height_cm || !profile.dob || !profile.activity_level || !profile.goal_type) {
        console.log('Missing profile data for calorie calculation, skipping update');
        return;
      }

      // Calculate new daily calorie goal
      const newCalorieGoal = calculateDailyCalorieGoal(
        profile.gender,
        profile.height_cm,
        newWeightKg,
        profile.dob,
        profile.activity_level,
        profile.goal_type,
        0.5 // Default goal rate, could be made configurable
      );

      console.log('Updating calorie goal from weight change:', {
        oldWeight: profile.weight_kg,
        newWeight: newWeightKg,
        newCalorieGoal
      });

      // Update the current goal with new calorie target
      const token = localStorage.getItem('token');
      await axios.put('/api/goals/current', {
        targetChange: 0, // Placeholder, could be calculated based on target weight
        pace: 0.5, // Default pace
        dailyCalorieGoal: newCalorieGoal
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      console.log('Calorie goal updated successfully to:', newCalorieGoal);
    } catch (error) {
      console.error('Error updating calorie goal:', error);
      // Don't fail the weight update if calorie goal update fails
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/users/profile', {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        // Format date for input field
        let formattedProfile = { ...response.data };
        if (formattedProfile.dob) {
          formattedProfile.dob = new Date(formattedProfile.dob).toISOString().split('T')[0];
        }

        // Convert database field names to match our form field names
        formattedProfile = {
          ...formattedProfile,
          height_cm: formattedProfile.heightCm !== null ? formattedProfile.heightCm : '',
          weight_kg: formattedProfile.weightKg !== null ? formattedProfile.weightKg : '',
          goal_type: formattedProfile.goalType || '',
          target_weight: formattedProfile.targetWeight !== null ? formattedProfile.targetWeight : '',
          activity_level: formattedProfile.activityLevel || '',
          height_unit: formattedProfile.heightUnit || 'cm',
          weight_unit: formattedProfile.weightUnit || 'kg'
        };

        setProfile(formattedProfile);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError('Failed to load profile data');
        setLoading(false);
      }
    };

    const loadConfig = async () => {
      try {
        const appConfig = await fetchConfig();
        setConfig(appConfig);
      } catch (error) {
        console.error('Error fetching config:', error);
      }
    };

    fetchData();
    loadConfig();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle unit changes - no conversion needed since we always store in cm/kg
    if (name === 'height_unit' || name === 'weight_unit') {
      setProfile({ ...profile, [name]: value });
    } else if (name === 'height_display') {
      // Handle height input - convert to cm for storage
      if (value === '') {
        setProfile({ ...profile, height_cm: '' });
      } else {
        const numValue = parseFloat(value);
        if (profile.height_unit === 'ft') {
          // For feet input, we need to handle this differently
          // For now, treat as decimal feet and convert to cm
          const cmValue = numValue * 30.48; // 1 ft = 30.48 cm
          setProfile({ ...profile, height_cm: Math.round(cmValue * 10) / 10 });
        } else {
          // Direct cm input
          setProfile({ ...profile, height_cm: numValue });
        }
      }
    } else if (name === 'weight_display') {
      // Handle weight input - convert to kg for storage
      setWeightModified(true); // Mark weight as modified
      if (value === '') {
        setProfile({ ...profile, weight_kg: '' });
      } else {
        const numValue = parseFloat(value);
        if (profile.weight_unit === 'lbs') {
          // Convert lbs to kg for storage
          const kgValue = convertWeight(numValue, 'lbs', 'kg');
          setProfile({ ...profile, weight_kg: kgValue });
        } else {
          // Direct kg input
          setProfile({ ...profile, weight_kg: numValue });
        }
      }
    } else if (name === 'target_weight_display') {
      // Handle target weight input - convert to kg for storage
      if (value === '') {
        setProfile({ ...profile, target_weight: '' });
      } else {
        const numValue = parseFloat(value);
        if (profile.weight_unit === 'lbs') {
          // Convert lbs to kg for storage
          const kgValue = convertWeight(numValue, 'lbs', 'kg');
          setProfile({ ...profile, target_weight: kgValue });
        } else {
          // Direct kg input
          setProfile({ ...profile, target_weight: numValue });
        }
      }
    } else if (['height_cm', 'weight_kg', 'target_weight'].includes(name)) {
      // Direct storage field updates (fallback)
      setProfile({ ...profile, [name]: value === '' ? '' : parseFloat(value) });
    } else {
      setProfile({ ...profile, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    globalLoading.updatingProfile();
    setError(null);
    setSuccess(null);

    try {
      // Convert field names to match the API expectations
      const profileData = {
        gender: profile.gender,
        dob: profile.dob,
        heightCm: profile.height_cm ? parseFloat(profile.height_cm) : null,
        weightKg: profile.weight_kg ? parseFloat(profile.weight_kg) : null,
        goalType: profile.goal_type || null,
        targetWeight: profile.target_weight ? parseFloat(profile.target_weight) : null,
        activityLevel: profile.activity_level || null,
        heightUnit: profile.height_unit,
        weightUnit: profile.weight_unit
      };

      const token = localStorage.getItem('token');

      // First, get the current profile to compare weights
      const currentProfileResponse = await axios.get('/api/users/profile', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      const currentWeight = currentProfileResponse.data.weightKg;
      const newWeight = profileData.weightKg;

      // Update the profile
      await axios.put('/api/users/profile', profileData, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      // If weight was modified by user and is not null, create a weight log entry
      // This ensures the Profile page will show the updated weight
      if (weightModified && newWeight !== null && newWeight !== currentWeight) {
        try {
          await axios.post('/api/weight', {
            weightKg: newWeight,
            timestamp: new Date().toISOString()
          }, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          // Recalculate and update calorie goal based on new weight
          await updateCalorieGoal(newWeight);
        } catch (weightLogError) {
          console.error('Error creating weight log:', weightLogError);
          // Don't fail the entire operation if weight logging fails
        }
      }

      setSuccess('Profile settings updated successfully');
      setWeightModified(false); // Reset weight modification flag
      setSaving(false);
      globalLoading.stop.profile();

      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Failed to update profile settings');
      setSaving(false);
      globalLoading.stop.profile();

      // Auto-hide error message after 5 seconds
      setTimeout(() => {
        setError(null);
      }, 5000);
    }
  };



  // Loading is now handled by global loading indicator

  return (
    <div className={`min-h-screen ${isMobile ? 'pb-24' : 'pb-20'}`}>
      {/* Header */}
      <header className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-800">
        <button
          onClick={() => navigate('/profile')}
          className="flex items-center text-blue-600 dark:text-blue-400 font-medium hover:opacity-70 transition-opacity"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Back
        </button>
        <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Settings</h1>
        <div className="w-16"></div> {/* Spacer for alignment */}
      </header>

      {/* Content */}
      <div className="px-4 py-6 space-y-8">
        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-100 dark:bg-red-900/30 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-xl">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-100 dark:bg-green-900/30 border border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 px-4 py-3 rounded-xl">
            {success}
          </div>
        )}

        {/* iPhone-style Settings */}
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Section */}
          <div className="space-y-3">
            <h2 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide px-4">
              Personal Information
            </h2>
            <div className="bg-white dark:bg-[#242426] rounded-xl overflow-hidden">
              {/* Gender */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Gender</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <select
                    id="gender"
                    name="gender"
                    value={profile.gender || ''}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[80px]"
                  >
                    <option value="">Select</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              {/* Date of Birth */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Date of Birth</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <input
                    type="date"
                    id="dob"
                    name="dob"
                    value={profile.dob ? profile.dob.split('T')[0] : ''}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none text-right min-w-[120px]"
                  />
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              {/* Height */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Height</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <input
                    type="number"
                    id="height_display"
                    name="height_display"
                    value={getDisplayHeight()}
                    onChange={handleChange}
                    placeholder="0"
                    min={0}
                    max={profile.height_unit === 'ft' ? 10 : 300}
                    step={profile.height_unit === 'ft' ? 0.1 : 1}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none w-16 text-right"
                  />
                  <span className="text-gray-500 dark:text-gray-400 text-sm min-w-[20px]">{getHeightUnitLabel(profile.height_unit)}</span>
                </div>
              </div>

              {/* Weight */}
              <div className="flex items-center justify-between px-6 py-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Weight</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <input
                    type="number"
                    id="weight_display"
                    name="weight_display"
                    value={getDisplayWeight()}
                    onChange={handleChange}
                    placeholder="0"
                    min={0}
                    max={profile.weight_unit === 'lbs' ? 1100 : 500}
                    step={0.1}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none w-16 text-right"
                  />
                  <span className="text-gray-500 dark:text-gray-400 text-sm min-w-[20px]">{getWeightUnitLabel(profile.weight_unit)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Units Section */}
          <div className="space-y-3">
            <h2 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide px-4">
              Units
            </h2>
            <div className="bg-white dark:bg-[#242426] rounded-xl overflow-hidden">
              {/* Height Unit */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Height Unit</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <select
                    id="height_unit"
                    name="height_unit"
                    value={profile.height_unit || 'cm'}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[100px]"
                  >
                    <option value="cm">Centimeters</option>
                    <option value="ft">Feet & Inches</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              {/* Weight Unit */}
              <div className="flex items-center justify-between px-6 py-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Weight Unit</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <select
                    id="weight_unit"
                    name="weight_unit"
                    value={profile.weight_unit || 'kg'}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[100px]"
                  >
                    <option value="kg">Kilograms</option>
                    <option value="lbs">Pounds</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Fitness Goals Section */}
          <div className="space-y-3">
            <h2 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide px-4">
              Fitness Goals
            </h2>
            <div className="bg-white dark:bg-[#242426] rounded-xl overflow-hidden">
              {/* Goal Type */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Goal Type</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <select
                    id="goal_type"
                    name="goal_type"
                    value={profile.goal_type || ''}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[120px]"
                  >
                    <option value="">Select</option>
                    <option value="lose">Lose Weight</option>
                    <option value="maintain">Maintain Weight</option>
                    <option value="gain">Gain Weight</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>

              {/* Target Weight */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Target Weight</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <input
                    type="number"
                    id="target_weight_display"
                    name="target_weight_display"
                    value={getDisplayTargetWeight()}
                    onChange={handleChange}
                    placeholder="0"
                    min={0}
                    max={profile.weight_unit === 'lbs' ? 1100 : 500}
                    step={0.1}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none w-16 text-right"
                  />
                  <span className="text-gray-500 dark:text-gray-400 text-sm min-w-[20px]">{getWeightUnitLabel(profile.weight_unit)}</span>
                </div>
              </div>

              {/* Activity Level */}
              <div className="flex items-center justify-between px-6 py-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Activity Level</span>
                </div>
                <div className="flex items-center space-x-2 min-w-0">
                  <select
                    id="activity_level"
                    name="activity_level"
                    value={profile.activity_level || ''}
                    onChange={handleChange}
                    className="bg-transparent text-gray-900 dark:text-white focus:outline-none appearance-none text-right pr-2 min-w-[100px]"
                  >
                    <option value="">Select</option>
                    <option value="sedentary">Sedentary</option>
                    <option value="light">Light</option>
                    <option value="moderate">Moderate</option>
                    <option value="active">Active</option>
                    <option value="very_active">Very Active</option>
                  </select>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* API Configuration Section */}
          <ApiConfigurationSettings />

          {/* App Settings Section */}
          <div className="space-y-3">
            <h2 className="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide px-4">
              App Settings
            </h2>
            <div className="bg-white dark:bg-[#242426] rounded-xl overflow-hidden">
              {/* Dark Mode Toggle */}
              <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-800 dark:bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0">
                    {isDarkMode ? (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                      </svg>
                    )}
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Dark Mode</span>
                </div>
                <button
                  type="button"
                  onClick={toggleDarkMode}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    isDarkMode ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      isDarkMode ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* API Dashboard - Only show if Ollama is available and URLs are available */}
              {config?.ollamaAvailable && config?.baseUrl && config?.ollamaGatewayPort && (
                <a
                  href={`${config.baseUrl}:${config.ollamaGatewayPort}/dashboard`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-between px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-b border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                      </svg>
                    </div>
                    <div>
                      <span className="text-gray-900 dark:text-white font-medium">API Dashboard</span>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Manage Ollama API keys</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </a>
              )}

              {/* Install App */}
              <Link
                to="/pwa-info"
                className="flex items-center justify-between px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <span className="text-gray-900 dark:text-white font-medium">Install App</span>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Bottom Action Buttons */}
          <div className="flex flex-col space-y-4 px-4 pt-8 pb-8">
            {/* Save Changes Button */}
            <button
              type="submit"
              disabled={saving}
              className="w-full py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-semibold rounded-2xl transition-colors shadow-lg"
            >
              {saving ? 'Saving Changes...' : 'Save Changes'}
            </button>

            {/* Logout Button */}
            <button
              type="button"
              onClick={logout}
              className="w-full py-4 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-2xl transition-colors shadow-lg"
            >
              Logout
            </button>
          </div>
        </form>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
};

export default Settings;
