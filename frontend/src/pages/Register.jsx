import { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import SplashLayout from '../components/SplashLayout';
import { registerImage } from '../assets/images/imageUrls';
import { checkEmailExists, isValidEmailFormat, debounce } from '../utils/emailValidation';

const Register = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Email validation states
  const [emailError, setEmailError] = useState('');
  const [emailValidating, setEmailValidating] = useState(false);
  const [emailExists, setEmailExists] = useState(false);

  const { register } = useAuth();
  const navigate = useNavigate();

  // Debounced email validation function
  const debouncedEmailValidation = useCallback(
    debounce(async (emailValue) => {
      if (!emailValue || !isValidEmailFormat(emailValue)) {
        setEmailValidating(false);
        return;
      }

      setEmailValidating(true);
      setEmailError('');

      try {
        const result = await checkEmailExists(emailValue);

        if (result.error) {
          setEmailError(result.error);
          setEmailExists(false);
        } else if (result.exists) {
          setEmailError('This email is already registered');
          setEmailExists(true);
        } else {
          setEmailError('');
          setEmailExists(false);
        }
      } catch (error) {
        setEmailError('Unable to validate email. Please try again.');
        setEmailExists(false);
      } finally {
        setEmailValidating(false);
      }
    }, 500),
    []
  );

  // Handle email input change
  const handleEmailChange = (e) => {
    const emailValue = e.target.value;
    setEmail(emailValue);

    // Clear previous validation states
    setEmailError('');
    setEmailExists(false);

    // Clear general error if it was related to email
    if (error && (error.includes('email') || error.includes('Email'))) {
      setError('');
    }
  };

  // Handle email field blur
  const handleEmailBlur = () => {
    if (email && isValidEmailFormat(email)) {
      debouncedEmailValidation(email);
    } else if (email && !isValidEmailFormat(email)) {
      setEmailError('Please enter a valid email address');
      setEmailExists(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !password || !confirmPassword) {
      setError('Please fill in all fields');
      return;
    }

    if (!isValidEmailFormat(email)) {
      setError('Please enter a valid email address');
      return;
    }

    if (emailExists) {
      setError('This email is already registered. Please use a different email or sign in instead.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters long');
      return;
    }

    try {
      setError('');
      setLoading(true);

      const result = await register({ email, password });

      if (result.success) {
        navigate('/onboarding');
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError('Failed to create an account. Please try again.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SplashLayout
      title="Create your account"
      subtitle={
        <p>
          Or{' '}
          <Link to="/login" className="font-medium text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300">
            sign in to your existing account
          </Link>
        </p>
      }
      imageSrc={registerImage}
      imageAlt="Fresh healthy ingredients"
      imagePosition="left"
    >
      <form className="space-y-6" onSubmit={handleSubmit}>
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-md">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Email address
            </label>
            <div className="relative">
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={handleEmailChange}
                onBlur={handleEmailBlur}
                className={`appearance-none block w-full px-4 py-3 border rounded-2xl shadow-sm placeholder-gray-400 bg-white text-black focus:outline-none focus:ring-black focus:border-black ${
                  emailError ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter your email"
              />
              {emailValidating && (
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                </div>
              )}
            </div>
            {emailError && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                {emailError}
                {emailExists && (
                  <span className="block mt-1">
                    Already have an account?{' '}
                    <Link to="/login" className="font-medium text-red-600 dark:text-red-400 hover:text-red-500 dark:hover:text-red-300 underline">
                      Sign in instead
                    </Link>
                  </span>
                )}
              </p>
            )}
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={emailExists}
              className={`appearance-none block w-full px-4 py-3 border border-gray-300 rounded-2xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black ${
                emailExists
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : 'bg-white text-black'
              }`}
              placeholder="Create a password"
            />
          </div>
          <div>
            <label htmlFor="confirm-password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Confirm Password
            </label>
            <input
              id="confirm-password"
              name="confirm-password"
              type="password"
              autoComplete="new-password"
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              disabled={emailExists}
              className={`appearance-none block w-full px-4 py-3 border border-gray-300 rounded-2xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-black focus:border-black ${
                emailExists
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : 'bg-white text-black'
              }`}
              placeholder="Confirm your password"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            disabled={loading || emailExists || emailValidating}
            className={`w-full py-4 px-6 font-bold rounded-2xl transition-all duration-300 bg-black text-white hover:bg-opacity-90 ${
              loading || emailExists || emailValidating ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? 'Creating account...' : emailExists ? 'Email already registered' : 'Create account'}
          </button>
        </div>
      </form>
    </SplashLayout>
  );
};

export default Register;
