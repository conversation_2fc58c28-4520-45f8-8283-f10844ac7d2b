import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { useIsMobile } from '../hooks/useIsMobile';
import { useGlobalLoading } from '../hooks/useGlobalLoading';
import { cn } from '../lib/utils';
import BottomNavigation from '../components/ui/BottomNavigation';
import NutritionBarChart from '../components/ui/NutritionBarChart';
import WeightProgressChart from '../components/ui/WeightProgressChart';
import WeightInputModal from '../components/modals/WeightInputModal';
import TargetWeightInputModal from '../components/modals/TargetWeightInputModal';
import { useAddOptionsContext } from '../context/AddOptionsContext';
import {
  getWeightDisplayValue,
  getWeightUnitLabel,
  formatWeight
} from '../utils/unitConversion';
import { calculateDailyCalorieGoal } from '../utils/weightCalculations';

const Profile = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const globalLoading = useGlobalLoading();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [activeTab, setActiveTab] = useState('thisWeek');
  const [weightPeriod, setWeightPeriod] = useState('30days');
  const [showWeightModal, setShowWeightModal] = useState(false);
  const [updatingWeight, setUpdatingWeight] = useState(false);
  const [showTargetWeightModal, setShowTargetWeightModal] = useState(false);
  const [updatingTargetWeight, setUpdatingTargetWeight] = useState(false);
  const { openAddOptions } = useAddOptionsContext();
  const [profile, setProfile] = useState({
    email: '',
    gender: '',
    dob: '',
    height_cm: '',
    weight_kg: '',
    goal_type: '',
    target_weight: '',
    activity_level: '',
    height_unit: 'cm',
    weight_unit: 'kg'
  });

  // State for weight history
  const [weightHistory, setWeightHistory] = useState([]);
  const [filteredWeightHistory, setFilteredWeightHistory] = useState([]);

  // State for calorie target
  const [calorieTarget, setCalorieTarget] = useState(2500);



  // State for nutrition data
  const [nutritionData, setNutritionData] = useState({
    thisWeek: {
      totalCalories: 0,
      dailyAvg: 0,
      dailyData: [0, 0, 0, 0, 0, 0, 0],
      // Add macronutrient data arrays for each day
      proteinData: [0, 0, 0, 0, 0, 0, 0],
      carbsData: [0, 0, 0, 0, 0, 0, 0],
      fatsData: [0, 0, 0, 0, 0, 0, 0],
      // Calorie contribution from each macronutrient
      proteinCalories: [0, 0, 0, 0, 0, 0, 0],
      carbsCalories: [0, 0, 0, 0, 0, 0, 0],
      fatsCalories: [0, 0, 0, 0, 0, 0, 0]
    },
    lastWeek: {
      totalCalories: 0,
      dailyAvg: 0,
      dailyData: [0, 0, 0, 0, 0, 0, 0],
      proteinData: [0, 0, 0, 0, 0, 0, 0],
      carbsData: [0, 0, 0, 0, 0, 0, 0],
      fatsData: [0, 0, 0, 0, 0, 0, 0],
      proteinCalories: [0, 0, 0, 0, 0, 0, 0],
      carbsCalories: [0, 0, 0, 0, 0, 0, 0],
      fatsCalories: [0, 0, 0, 0, 0, 0, 0]
    },
    twoWeeksAgo: {
      totalCalories: 0,
      dailyAvg: 0,
      dailyData: [0, 0, 0, 0, 0, 0, 0],
      proteinData: [0, 0, 0, 0, 0, 0, 0],
      carbsData: [0, 0, 0, 0, 0, 0, 0],
      fatsData: [0, 0, 0, 0, 0, 0, 0],
      proteinCalories: [0, 0, 0, 0, 0, 0, 0],
      carbsCalories: [0, 0, 0, 0, 0, 0, 0],
      fatsCalories: [0, 0, 0, 0, 0, 0, 0]
    },
    threeWeeksAgo: {
      totalCalories: 0,
      dailyAvg: 0,
      dailyData: [0, 0, 0, 0, 0, 0, 0],
      proteinData: [0, 0, 0, 0, 0, 0, 0],
      carbsData: [0, 0, 0, 0, 0, 0, 0],
      fatsData: [0, 0, 0, 0, 0, 0, 0],
      proteinCalories: [0, 0, 0, 0, 0, 0, 0],
      carbsCalories: [0, 0, 0, 0, 0, 0, 0],
      fatsCalories: [0, 0, 0, 0, 0, 0, 0]
    }
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        globalLoading.fetchingData();
        console.log('Fetching user profile data...');

        // Fetch user profile
        const profileResponse = await axios.get('/api/users/profile');
        console.log('Profile data received:', profileResponse.data);

        // Format date for input field
        let formattedProfile = { ...profileResponse.data };
        if (formattedProfile.dob) {
          formattedProfile.dob = new Date(formattedProfile.dob).toISOString().split('T')[0];
        }

        // Fetch user's current goal to get calorie target
        try {
          const goalResponse = await axios.get('/api/goals/current');
          if (goalResponse.data && goalResponse.data.dailyCalorieGoal) {
            console.log('Calorie target from API:', goalResponse.data.dailyCalorieGoal);
            setCalorieTarget(goalResponse.data.dailyCalorieGoal);
          }
        } catch (goalError) {
          console.error('Error fetching calorie target:', goalError);
          // Keep default calorie target if there's an error
        }

        // Convert database field names to match our form field names
        formattedProfile = {
          ...formattedProfile,
          height_cm: formattedProfile.heightCm !== null ? formattedProfile.heightCm : '',
          weight_kg: formattedProfile.weightKg !== null ? formattedProfile.weightKg : '',
          goal_type: formattedProfile.goalType || '',
          target_weight: formattedProfile.targetWeight !== null ? formattedProfile.targetWeight : '',
          activity_level: formattedProfile.activityLevel || '',
          height_unit: formattedProfile.heightUnit || 'cm',
          weight_unit: formattedProfile.weightUnit || 'kg'
        };

        console.log('Height from API:', formattedProfile.heightCm);
        console.log('Weight from API:', formattedProfile.weightKg);
        console.log('Target weight from API:', formattedProfile.targetWeight);

        // Fetch current weight (latest by timestamp) and update profile
        try {
          // Get current weight based on latest timestamp
          const currentWeightResponse = await axios.get('/api/weight/current');
          if (currentWeightResponse.data) {
            // Update profile with the accurate current weight
            formattedProfile.weightKg = currentWeightResponse.data.weightKg;
            formattedProfile.weight_kg = currentWeightResponse.data.weightKg;
          }
        } catch (currentWeightError) {
          console.error('Error fetching current weight:', currentWeightError);
          // If no current weight found, that's okay - user might not have logged weight yet
        }

        console.log('Formatted profile data:', formattedProfile);
        setProfile(formattedProfile);

        try {
          // Get historical weight data
          const weightResponse = await axios.get('/api/weight/historical?limit=100');
          const formattedWeightHistory = weightResponse.data.map(log => ({
            date: new Date(log.timestamp).toISOString().split('T')[0],
            weight: log.weightKg,
            timestamp: new Date(log.timestamp)
          }));
          setWeightHistory(formattedWeightHistory);
        } catch (weightError) {
          console.error('Error fetching weight history:', weightError);
        }

        // Fetch nutrition data for the last 4 weeks
        try {
          // Calculate date ranges for the last 4 weeks
          const today = new Date();
          const thisWeekStart = new Date(today);
          thisWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)

          const lastWeekStart = new Date(thisWeekStart);
          lastWeekStart.setDate(thisWeekStart.getDate() - 7);

          const twoWeeksAgoStart = new Date(lastWeekStart);
          twoWeeksAgoStart.setDate(lastWeekStart.getDate() - 7);

          const threeWeeksAgoStart = new Date(twoWeeksAgoStart);
          threeWeeksAgoStart.setDate(twoWeeksAgoStart.getDate() - 7);

          const threeWeeksAgoEnd = new Date(twoWeeksAgoStart);
          threeWeeksAgoEnd.setDate(threeWeeksAgoEnd.getDate() - 1);

          const twoWeeksAgoEnd = new Date(lastWeekStart);
          twoWeeksAgoEnd.setDate(twoWeeksAgoEnd.getDate() - 1);

          const lastWeekEnd = new Date(thisWeekStart);
          lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);

          const thisWeekEnd = new Date(today);

          // Format dates for API requests (YYYY-MM-DD format)
          const formatDateForAPI = (date) => date.toISOString().split('T')[0];

          // Fetch meals for each week
          const [thisWeekMeals, lastWeekMeals, twoWeeksAgoMeals, threeWeeksAgoMeals] = await Promise.all([
            axios.get(`/api/meals?start_date=${formatDateForAPI(thisWeekStart)}&end_date=${formatDateForAPI(thisWeekEnd)}`),
            axios.get(`/api/meals?start_date=${formatDateForAPI(lastWeekStart)}&end_date=${formatDateForAPI(lastWeekEnd)}`),
            axios.get(`/api/meals?start_date=${formatDateForAPI(twoWeeksAgoStart)}&end_date=${formatDateForAPI(twoWeeksAgoEnd)}`),
            axios.get(`/api/meals?start_date=${formatDateForAPI(threeWeeksAgoStart)}&end_date=${formatDateForAPI(threeWeeksAgoEnd)}`)
          ]);

          // Process nutrition data for each week
          const processWeekData = (meals) => {
            const dailyData = [0, 0, 0, 0, 0, 0, 0]; // Initialize with zeros for each day
            const proteinData = [0, 0, 0, 0, 0, 0, 0];
            const carbsData = [0, 0, 0, 0, 0, 0, 0];
            const fatsData = [0, 0, 0, 0, 0, 0, 0];
            const proteinCalories = [0, 0, 0, 0, 0, 0, 0];
            const carbsCalories = [0, 0, 0, 0, 0, 0, 0];
            const fatsCalories = [0, 0, 0, 0, 0, 0, 0];
            let totalCalories = 0;

            // Process each meal
            meals.data.forEach(meal => {
              const mealDate = new Date(meal.timestamp);
              const dayOfWeek = mealDate.getDay(); // 0 = Sunday, 6 = Saturday

              // Add calories to daily total
              const mealCalories = meal.calories || 0;
              dailyData[dayOfWeek] += mealCalories;
              totalCalories += mealCalories;

              // Extract macronutrient data (handle both naming conventions)
              const proteinGrams = parseFloat(meal.proteinG || meal.protein_g || 0);
              const carbsGrams = parseFloat(meal.carbsG || meal.carbs_g || 0);
              const fatsGrams = parseFloat(meal.fatsG || meal.fats_g || 0);

              // Add macronutrient grams to daily totals
              proteinData[dayOfWeek] += proteinGrams;
              carbsData[dayOfWeek] += carbsGrams;
              fatsData[dayOfWeek] += fatsGrams;

              // Calculate calorie contribution from each macronutrient
              // Protein: 4 calories per gram
              // Carbs: 4 calories per gram
              // Fats: 9 calories per gram
              proteinCalories[dayOfWeek] += proteinGrams * 4;
              carbsCalories[dayOfWeek] += carbsGrams * 4;
              fatsCalories[dayOfWeek] += fatsGrams * 9;

              // Log for debugging
              console.log(`Day ${dayOfWeek}: Added meal with ${mealCalories} calories, ${proteinGrams}g protein (${proteinGrams * 4} cal), ${carbsGrams}g carbs (${carbsGrams * 4} cal), ${fatsGrams}g fat (${fatsGrams * 9} cal)`);
            });

            // If we have calorie data but no macro data for any day, estimate macros
            for (let i = 0; i < 7; i++) {
              if (dailyData[i] > 0 && (proteinCalories[i] + carbsCalories[i] + fatsCalories[i]) === 0) {
                console.log(`Day ${i}: No macro data found for ${dailyData[i]} calories. Using default proportions.`);

                // Use standard proportions: 30% protein, 40% carbs, 30% fats
                proteinCalories[i] = Math.round(dailyData[i] * 0.3);
                carbsCalories[i] = Math.round(dailyData[i] * 0.4);
                fatsCalories[i] = Math.round(dailyData[i] * 0.3);

                // Calculate grams from calories
                proteinData[i] = Math.round(proteinCalories[i] / 4);
                carbsData[i] = Math.round(carbsCalories[i] / 4);
                fatsData[i] = Math.round(fatsCalories[i] / 9);

                console.log(`Day ${i}: Estimated macros - Protein: ${proteinCalories[i]} cal, Carbs: ${carbsCalories[i]} cal, Fats: ${fatsCalories[i]} cal, Total: ${proteinCalories[i] + carbsCalories[i] + fatsCalories[i]} cal vs ${dailyData[i]} total`);
              } else if (dailyData[i] > 0) {
                // Log the actual macros for days that have data
                console.log(`Day ${i}: Actual macros - Protein: ${proteinCalories[i]} cal, Carbs: ${carbsCalories[i]} cal, Fats: ${fatsCalories[i]} cal, Total: ${proteinCalories[i] + carbsCalories[i] + fatsCalories[i]} cal vs ${dailyData[i]} total`);
              }
            }

            // No sample data will be added if no real data exists
            if (totalCalories === 0) {
              console.log('No nutrition data found for this week');
            }

            // Calculate the number of days with data
            const daysWithData = dailyData.filter(calories => calories > 0).length;

            return {
              totalCalories,
              // Calculate daily average based on days with actual data, not the whole week
              dailyAvg: daysWithData > 0 ? Math.round(totalCalories / daysWithData) : 0,
              dailyData,
              proteinData,
              carbsData,
              fatsData,
              proteinCalories,
              carbsCalories,
              fatsCalories,
              daysWithData // Add this to track how many days had data
            };
          };

          // Process nutrition data for each week
          const processedData = {
            thisWeek: processWeekData(thisWeekMeals),
            lastWeek: processWeekData(lastWeekMeals),
            twoWeeksAgo: processWeekData(twoWeeksAgoMeals),
            threeWeeksAgo: processWeekData(threeWeeksAgoMeals)
          };

          // Log the processed data for debugging
          console.log('Processed nutrition data:', processedData);

          setNutritionData(processedData);
        } catch (nutritionError) {
          console.error('Error fetching nutrition data:', nutritionError);
        }

        setLoading(false);
        globalLoading.stop.data();
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError('Failed to load profile data');
        setLoading(false);
        globalLoading.stop.data();
      }
    };

    fetchData();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Convert numeric values
    if (['height_cm', 'weight_kg', 'target_weight'].includes(name)) {
      setProfile({ ...profile, [name]: value === '' ? '' : parseFloat(value) });
    } else {
      setProfile({ ...profile, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      globalLoading.updatingProfile();
      setError(null);
      setSuccess(null);

      // Convert field names to match the API expectations
      const profileData = {
        gender: profile.gender,
        dob: profile.dob,
        heightCm: profile.height_cm ? parseFloat(profile.height_cm) : null,
        weightKg: profile.weightKg ? parseFloat(profile.weightKg) : null,
        goalType: profile.goal_type || null,
        targetWeight: profile.target_weight ? parseFloat(profile.target_weight) : null,
        activityLevel: profile.activity_level || null
      };

      console.log('Sending updated profile data to backend:', profileData);

      // Update user profile
      await axios.put('/api/users/profile', profileData);

      // Log weight if it has changed
      const lastWeight = weightHistory.length > 0 ? weightHistory[weightHistory.length - 1].weight : null;
      if (profile.weightKg && (!lastWeight || parseFloat(profile.weightKg) !== lastWeight)) {
        try {
          await axios.post('/api/weight', {
            weightKg: parseFloat(profile.weightKg),
            timestamp: new Date().toISOString()
          });

          // Update weight history
          const newWeightLog = {
            date: new Date().toISOString().split('T')[0],
            weight: parseFloat(profile.weightKg),
            timestamp: new Date()
          };
          setWeightHistory(prev => [...prev, newWeightLog]);
        } catch (weightError) {
          console.error('Error logging weight:', weightError);
        }
      }

      setSuccess('Profile updated successfully');
      setSaving(false);
      globalLoading.stop.profile();
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Failed to update profile');
      setSaving(false);
      globalLoading.stop.profile();
    }
  };

  const handleLogout = () => {
    logout();
  };

  // Calculate BMI
  const calculateBMI = () => {
    if (!profile.height_cm || !profile.weightKg ||
        isNaN(parseFloat(profile.height_cm)) ||
        isNaN(parseFloat(profile.weightKg)) ||
        parseFloat(profile.height_cm) <= 0) {
      return null;
    }

    const heightInMeters = parseFloat(profile.height_cm) / 100;
    const bmi = parseFloat(profile.weightKg) / (heightInMeters * heightInMeters);

    // Check if the result is a valid number
    if (isNaN(bmi) || !isFinite(bmi)) {
      return null;
    }

    return bmi.toFixed(1);
  };

  // Get BMI category
  const getBMICategory = (bmi) => {
    if (!bmi) return null;

    if (bmi < 18.5) return { category: 'Underweight', color: 'underweight' };
    if (bmi < 25) return { category: 'Normal', color: 'normal' };
    if (bmi < 30) return { category: 'Overweight', color: 'overweight' };
    return { category: 'Obese', color: 'obese' };
  };

  // Calculate BMI position on the scale (0-100%)
  const calculateBMIPosition = (bmi) => {
    if (!bmi) return 0;

    // BMI scale typically goes from 15 to 40
    const minBMI = 15;
    const maxBMI = 40;
    const range = maxBMI - minBMI;

    // Calculate position as percentage
    let position = ((parseFloat(bmi) - minBMI) / range) * 100;

    // Constrain between 0 and 100
    position = Math.max(0, Math.min(100, position));

    return position;
  };

  // Calculate weight progress percentage based on chronological progression
  const calculateWeightProgress = () => {
    if (!profile.weightKg || !profile.target_weight) return null;

    const currentWeight = parseFloat(profile.weightKg);
    const targetWeight = parseFloat(profile.target_weight);

    // Determine the true starting weight for progress calculation
    // Priority: 1) startingWeightKg from signup, 2) earliest weight log entry, 3) current weight
    let startWeight;
    if (profile.startingWeightKg) {
      startWeight = parseFloat(profile.startingWeightKg);
    } else if (weightHistory.length > 0) {
      // weightHistory is already sorted chronologically (ascending by timestamp)
      // so weightHistory[0] is the earliest entry
      startWeight = weightHistory[0].weight;
    } else {
      startWeight = currentWeight;
    }

    // If target equals current weight, goal achieved
    if (Math.abs(targetWeight - currentWeight) < 0.1) {
      return 100;
    }

    // Calculate progress based on the actual direction (starting weight vs target weight)
    // This is more reliable than using the goalType field which might be incorrect
    let progress;

    if (targetWeight < startWeight) {
      // Weight loss goal: target is lower than starting weight
      const totalToLose = startWeight - targetWeight;
      const lostSoFar = startWeight - currentWeight;

      if (totalToLose <= 0) return 100;
      progress = (lostSoFar / totalToLose) * 100;

      console.log('Weight loss calculation:', {
        startWeight,
        currentWeight,
        targetWeight,
        totalToLose,
        lostSoFar,
        progress: Math.round(progress)
      });
    } else if (targetWeight > startWeight) {
      // Weight gain goal: target is higher than starting weight
      const totalToGain = targetWeight - startWeight;
      const gainedSoFar = currentWeight - startWeight;

      if (totalToGain <= 0) return 100;
      progress = (gainedSoFar / totalToGain) * 100;

      console.log('Weight gain calculation:', {
        startWeight,
        currentWeight,
        targetWeight,
        totalToGain,
        gainedSoFar,
        progress: Math.round(progress)
      });
    } else {
      // Target equals starting weight (maintenance goal)
      console.log('Maintenance goal: target equals starting weight');
      return 100;
    }

    // Return progress clamped between 0 and 100
    // This ensures that even if someone temporarily reached their goal
    // but then moved away from it, the progress reflects their current position
    return Math.min(100, Math.max(0, Math.round(progress)));
  };

  // Handle tab selection for nutrition data
  const handleTabSelect = (tab) => {
    setActiveTab(tab);
  };

  // Handle weight period selection
  const handleWeightPeriodSelect = (period) => {
    setWeightPeriod(period);
  };

  // Filter weight history based on selected period
  const filterWeightHistory = (period, history) => {
    if (!history || history.length === 0) return [];

    const now = new Date();
    let startDate;

    switch (period) {
      case '30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '3months':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '6months':
        startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
        break;
      case '1year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      case 'alltime':
        return history;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return history.filter(entry => entry.timestamp >= startDate);
  };

  // Update filtered weight history when period or history changes
  useEffect(() => {
    const filtered = filterWeightHistory(weightPeriod, weightHistory);
    setFilteredWeightHistory(filtered);
  }, [weightPeriod, weightHistory]);

  // Function to recalculate and update calorie goal when weight changes
  const updateCalorieGoal = async (newWeightKg) => {
    try {
      // Only recalculate if we have all required data
      if (!profile.gender || !profile.height_cm || !profile.dob || !profile.activity_level || !profile.goal_type) {
        console.log('Missing profile data for calorie calculation, skipping update');
        return;
      }

      // Calculate new daily calorie goal
      const newCalorieGoal = calculateDailyCalorieGoal(
        profile.gender,
        profile.height_cm,
        newWeightKg,
        profile.dob,
        profile.activity_level,
        profile.goal_type,
        0.5 // Default goal rate, could be made configurable
      );

      console.log('Updating calorie goal from weight change:', {
        oldWeight: profile.weightKg,
        newWeight: newWeightKg,
        newCalorieGoal
      });

      // Update the current goal with new calorie target
      await axios.put('/api/goals/current', {
        targetChange: 0, // Placeholder, could be calculated based on target weight
        pace: 0.5, // Default pace
        dailyCalorieGoal: newCalorieGoal
      });

      // Update local calorie target state
      setCalorieTarget(newCalorieGoal);

      console.log('Calorie goal updated successfully to:', newCalorieGoal);
    } catch (error) {
      console.error('Error updating calorie goal:', error);
      // Don't fail the weight update if calorie goal update fails
    }
  };

  // Handle weight update
  const handleWeightUpdate = async (newWeightKg) => {
    try {
      setUpdatingWeight(true);
      setError(null);

      // Log the new weight (this also updates the user's profile weight in the backend)
      const response = await axios.post('/api/weight', {
        weightKg: newWeightKg,
        timestamp: new Date().toISOString()
      });

      // Update local state (the backend already updated the user profile)
      setProfile(prev => ({ ...prev, weightKg: newWeightKg }));

      // Add to weight history
      const newWeightLog = {
        date: new Date().toISOString().split('T')[0],
        weight: newWeightKg,
        timestamp: new Date()
      };
      setWeightHistory(prev => [...prev, newWeightLog]);

      // Recalculate and update calorie goal based on new weight
      await updateCalorieGoal(newWeightKg);

      setShowWeightModal(false);
      setSuccess('Weight updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error updating weight:', error);
      setError('Failed to update weight');
    } finally {
      setUpdatingWeight(false);
    }
  };

  // Handle target weight update
  const handleTargetWeightUpdate = async (newTargetWeight) => {
    try {
      setUpdatingTargetWeight(true);
      setError(null);

      // Update the user's profile with the new target weight
      // Since there can only be one target weight per user, we update the user profile directly
      const response = await axios.put('/api/users/profile', {
        targetWeight: newTargetWeight
      });

      // Update local state
      setProfile(prev => ({
        ...prev,
        target_weight: newTargetWeight,
        targetWeight: newTargetWeight
      }));

      setShowTargetWeightModal(false);
      setSuccess('Target weight updated successfully');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error('Error updating target weight:', error);
      setError('Failed to update target weight');
    } finally {
      setUpdatingTargetWeight(false);
    }
  };

  const bmi = calculateBMI();
  const bmiCategory = getBMICategory(bmi);
  const bmiPosition = calculateBMIPosition(bmi);
  const weightProgress = calculateWeightProgress();

  // Get user's first name from email
  const getFirstName = () => {
    if (!profile.email) return '';
    return profile.email.split('@')[0].split('.')[0];
  };

  // Format activity level for display
  const formatActivityLevel = (level) => {
    if (!level) return '';

    const activityMap = {
      'sedentary': 'Sedentary',
      'light': 'Light Activity',
      'moderate': 'Moderate Activity',
      'active': 'Active',
      'very_active': 'Very Active'
    };

    return activityMap[level] || level.charAt(0).toUpperCase() + level.slice(1);
  };

  // Calculate age from date of birth
  const calculateAge = () => {
    if (!profile.dob) return null;

    const birthDate = new Date(profile.dob);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();

    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Loading is now handled by global loading indicator

  return (
    <div className={`min-h-screen ${isMobile ? 'pb-24' : 'pb-20'}`}>
      {/* Header - Empty for clean look */}
      <header className="p-4">
      </header>

      {/* Content */}
      <div className={`${isMobile ? 'px-4 pt-4' : 'px-8 py-4 max-w-7xl mx-auto'}`}>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-4">
            {error}
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-xl mb-4">
            {success}
          </div>
        )}

        {/* Desktop Layout - Two Columns */}
        {!isMobile ? (
          <div className="grid grid-cols-12 gap-6">
            {/* Left Column */}
            <div className="col-span-6 space-y-6">
              {/* Target Weight */}
              <div className="bg-white rounded-2xl shadow-sm mb-4">
                <div className="flex justify-between items-center p-4">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                      <span className="text-white text-lg">🏆</span>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Target Weight</p>
                      <div className="flex items-center">
                        <span className="text-xl font-bold">
                          {profile.target_weight ? getWeightDisplayValue(profile.target_weight, profile.weight_unit) : '185'}
                        </span>
                        <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowTargetWeightModal(true)}
                    className="px-4 py-2 rounded-xl border border-gray-300 text-sm hover:bg-gray-50"
                  >
                    Update
                  </button>
                </div>
              </div>

              {/* Current Weight */}
              <div className="bg-white rounded-2xl shadow-sm mb-4">
                <div className="flex justify-between items-center p-4">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-3">
                      <span className="text-white text-lg">⚖️</span>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Current Weight</p>
                      <div className="flex items-center">
                        <span className="text-xl font-bold">
                          {profile.weightKg ? getWeightDisplayValue(profile.weightKg, profile.weight_unit) : '193'}
                        </span>
                        <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={() => setShowWeightModal(true)}
                    className="px-4 py-2 rounded-xl bg-black text-white hover:bg-gray-800"
                  >
                    Update
                  </button>
                </div>
              </div>

              {/* Reminder */}
              <div className="bg-purple-50 p-4 rounded-2xl mb-6">
                <p className="text-purple-800 text-sm">
                  Remember to update this at least once a week so we can adjust your plan to hit your goal
                </p>
              </div>

              {/* BMI Section */}
              <h2 className="text-lg font-medium mb-3">Your BMI</h2>
              <div className="bg-white p-6 rounded-2xl shadow-sm">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <p className="text-gray-500 mr-2">Your Weight is</p>
                    <span className={`bg-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-100 text-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-600 rounded px-2 py-0.5 text-sm font-medium`}>
                      {bmiCategory?.category || 'Normal'}
                    </span>
                  </div>
                  <button
                    onClick={() => navigate('/bmi')}
                    className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors cursor-pointer text-sm font-medium"
                    title="Learn more about BMI"
                  >
                    ?
                  </button>
                </div>

                <div className="flex items-center">
                  <span className="text-5xl font-bold">{bmi || '26'}</span>
                  <span className="text-gray-500 ml-2">BMI</span>
                </div>

                {/* BMI Scale with granular bars - exact match to design */}
                <div className="mt-6">
                  <div className="relative">
                    {/* BMI Scale with evenly spaced bars */}
                    <div className="h-7 w-full flex">
                      {/* All bars in a single container with proper spacing */}
                      <div className="w-full flex items-center justify-between px-0.5">
                        {/* Generate all bars at once with appropriate colors */}
                        {Array.from({ length: 32 }).map((_, i) => {
                          const currentBMIPosition = bmiPosition !== null && bmiPosition !== undefined ? bmiPosition : 55;

                          // Find the closest bar to the BMI position
                          let isClosestBar = false;
                          if (bmi && bmiPosition !== null && bmiPosition !== undefined) {
                            // Calculate which bar index should be highlighted based on BMI position
                            const targetBarIndex = Math.round((currentBMIPosition / 100) * 31);
                            isClosestBar = i === targetBarIndex;
                          }

                          // Determine color based on position
                          let bgColor;
                          if (isClosestBar) {
                            bgColor = 'bg-black'; // Black for the indicator bar
                          } else if (i < 8) {
                            bgColor = 'bg-blue-400'; // Underweight - blue
                          } else if (i < 16) {
                            bgColor = 'bg-green-500'; // Normal - green
                          } else if (i < 24) {
                            bgColor = 'bg-yellow-400'; // Overweight - yellow
                          } else {
                            bgColor = 'bg-red-500'; // Obese - red
                          }

                          return (
                            <div
                              key={`bmi-bar-${i}`}
                              className={`h-full w-1.5 ${bgColor} rounded-t-md rounded-b-md relative`}
                            >
                              {/* Add downward-pointing arrow at the top of the black indicator bar */}
                              {isClosestBar && (
                                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 text-black" style={{ fontSize: '10px' }}>
                                  ▼
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Category labels with proper spacing */}
                  <div className="flex text-xs text-gray-500 mt-3">
                    <div className="w-1/4 text-left">Underweight</div>
                    <div className="w-1/4 text-center">Normal</div>
                    <div className="w-1/4 text-center">Overweight</div>
                    <div className="w-1/4 text-right">Obese</div>
                  </div>
                </div>
              </div>

              {/* Goal Progress Section */}
              <div className="bg-white p-6 rounded-2xl shadow-sm">
                <h2 className="text-lg font-medium mb-4">Goal Progress</h2>

                {/* Current Weight Display */}
                <div className="flex justify-between items-center mb-4">
                  <div>
                    <p className="text-sm text-gray-500">Current Weight</p>
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold">
                        {profile.weightKg ? getWeightDisplayValue(profile.weightKg, profile.weight_unit) : '--'}
                      </span>
                      <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                    </div>
                  </div>
                  {profile.target_weight && (
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Target Weight</p>
                      <div className="flex items-baseline">
                        <span className="text-xl font-semibold text-green-600">
                          {getWeightDisplayValue(profile.target_weight, profile.weight_unit)}
                        </span>
                        <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Progress Summary */}
                {profile.target_weight && profile.weightKg && (
                  <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-sm text-gray-600">Progress</p>
                        <p className="text-lg font-semibold">
                          {weightProgress !== null ? weightProgress : 0}% Complete
                          {weightProgress === 0 && profile.startingWeightKg && profile.target_weight && (
                            <span className="text-xs text-orange-600 block mt-1">
                              {(() => {
                                const currentWeight = parseFloat(profile.weightKg);
                                const startingWeight = parseFloat(profile.startingWeightKg);
                                const targetWeight = parseFloat(profile.target_weight);
                                const isWeightLossGoal = targetWeight < startingWeight;

                                if (isWeightLossGoal) {
                                  // Weight loss goal
                                  if (currentWeight === startingWeight) return 'At starting weight';
                                  if (currentWeight > startingWeight) return 'Above starting weight';
                                  return 'Lost weight but not enough';
                                } else {
                                  // Weight gain goal
                                  if (currentWeight === startingWeight) return 'At starting weight';
                                  if (currentWeight < startingWeight) return 'Below starting weight';
                                  return 'Gained weight but not enough';
                                }
                              })()}
                            </span>
                          )}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">
                          {filteredWeightHistory.length > 0 ? `${filteredWeightHistory.length} entries` : 'No data yet'}
                        </p>
                        <p className="text-sm text-gray-500">
                          {weightPeriod === '30days' ? 'Last 30 days' :
                           weightPeriod === '3months' ? 'Last 3 months' :
                           weightPeriod === '6months' ? 'Last 6 months' :
                           weightPeriod === '1year' ? 'Last year' : 'All time'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex border-b pb-2 mb-6">
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${weightPeriod === '30days' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleWeightPeriodSelect('30days')}
                  >
                    30 Days
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${weightPeriod === '3months' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleWeightPeriodSelect('3months')}
                  >
                    3 Months
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${weightPeriod === '6months' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleWeightPeriodSelect('6months')}
                  >
                    6 Months
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${weightPeriod === '1year' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleWeightPeriodSelect('1year')}
                  >
                    1 Year
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${weightPeriod === 'alltime' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleWeightPeriodSelect('alltime')}
                  >
                    All Time
                  </button>
                </div>

                <WeightProgressChart
                  weightData={filteredWeightHistory}
                  targetWeight={profile.target_weight}
                  weightUnit={profile.weight_unit}
                  className="w-full"
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="col-span-6">
              {/* Nutrition Section */}
              <div className="bg-white p-6 rounded-2xl shadow-sm mb-6">
                <h2 className="text-lg font-medium mb-3">Nutrition</h2>

                <div className="flex border-b pb-2">
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${activeTab === 'thisWeek' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleTabSelect('thisWeek')}
                  >
                    This week
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${activeTab === 'lastWeek' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleTabSelect('lastWeek')}
                  >
                    Last week
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${activeTab === 'twoWeeksAgo' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleTabSelect('twoWeeksAgo')}
                  >
                    2 wks. ago
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-sm ${activeTab === 'threeWeeksAgo' ? 'bg-black text-white rounded-xl' : ''}`}
                    onClick={() => handleTabSelect('threeWeeksAgo')}
                  >
                    3 wks. ago
                  </button>
                </div>

                <div className="flex justify-between mt-6 mb-8">
                  <div className="text-center">
                    <p className="text-2xl font-bold">{nutritionData[activeTab].totalCalories.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">Total calories</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold">{nutritionData[activeTab].dailyAvg.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">
                      Daily avg.
                      {nutritionData[activeTab].daysWithData > 0 && (
                        <span className="text-xs ml-1">
                          ({nutritionData[activeTab].daysWithData} day{nutritionData[activeTab].daysWithData !== 1 ? 's' : ''})
                        </span>
                      )}
                    </p>
                  </div>
                </div>

                {/* Nutrition Bar Chart */}
                <div className="mt-6 mb-4">
                  <NutritionBarChart
                    data={nutritionData[activeTab].dailyData}
                    proteinCalories={nutritionData[activeTab].proteinCalories}
                    carbsCalories={nutritionData[activeTab].carbsCalories}
                    fatsCalories={nutritionData[activeTab].fatsCalories}
                    calorieTarget={calorieTarget}
                    labels={['S', 'M', 'T', 'W', 'T', 'F', 'S']}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Settings Button */}
              <div className="bg-white rounded-2xl shadow-sm p-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-medium">Account Settings</h2>
                  <button
                    onClick={() => navigate('/settings')}
                    className="px-4 py-2 bg-black text-white rounded-2xl hover:bg-opacity-90"
                  >
                    Manage Settings
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Mobile Layout
          <div className="space-y-4">
            {/* Target Weight */}
            <div className="bg-white rounded-2xl shadow-sm mb-4">
              <div className="flex justify-between items-center p-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-white text-lg">🏆</span>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm">Target Weight</p>
                    <div className="flex items-center">
                      <span className="text-xl font-bold">
                        {profile.target_weight ? getWeightDisplayValue(profile.target_weight, profile.weight_unit) : '185'}
                      </span>
                      <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => setShowTargetWeightModal(true)}
                  className="px-3 py-1 rounded-xl border border-gray-300 text-sm hover:bg-gray-50"
                >
                  Update
                </button>
              </div>
            </div>

            {/* Current Weight */}
            <div className="bg-white rounded-2xl shadow-sm mb-4">
              <div className="flex justify-between items-center p-4">
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-3">
                    <span className="text-white text-lg">⚖️</span>
                  </div>
                  <div>
                    <p className="text-gray-500 text-sm">Current Weight</p>
                    <div className="flex items-center">
                      <span className="text-xl font-bold">
                        {profile.weightKg ? getWeightDisplayValue(profile.weightKg, profile.weight_unit) : '193'}
                      </span>
                      <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => setShowWeightModal(true)}
                  className="px-3 py-1 rounded-xl bg-black text-white hover:bg-gray-800"
                >
                  Update
                </button>
              </div>
            </div>

            {/* Reminder */}
            <div className="bg-purple-50 p-4 rounded-2xl mb-4">
              <p className="text-purple-800 text-sm">
                Remember to update this at least once a week so we can adjust your plan to hit your goal
              </p>
            </div>

            {/* BMI Section */}
            <h2 className="text-lg font-medium mb-3">Your BMI</h2>
            <div className="bg-white p-4 rounded-2xl shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <p className="text-gray-500 mr-2">Your Weight is</p>
                  <span className={`bg-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-100 text-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-600 rounded px-2 py-0.5 text-sm font-medium`}>
                    {bmiCategory?.category || 'Normal'}
                  </span>
                </div>
                <button
                  onClick={() => navigate('/bmi')}
                  className="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center text-gray-600 hover:text-gray-800 transition-colors cursor-pointer text-sm font-medium"
                  title="Learn more about BMI"
                >
                  ?
                </button>
              </div>

              <div className="flex items-center">
                <span className="text-5xl font-bold">{bmi || '26'}</span>
                <span className="text-gray-500 ml-2">BMI</span>
              </div>

              {/* BMI Scale with granular bars - exact match to design */}
              <div className="mt-6">
                <div className="relative">
                  {/* BMI Scale with evenly spaced bars */}
                  <div className="h-7 w-full flex">
                    {/* All bars in a single container with proper spacing */}
                    <div className="w-full flex items-center justify-between px-0.5">
                      {/* Generate all bars at once with appropriate colors */}
                      {Array.from({ length: 32 }).map((_, i) => {
                        const currentBMIPosition = bmiPosition !== null && bmiPosition !== undefined ? bmiPosition : 55;

                        // Find the closest bar to the BMI position
                        let isClosestBar = false;
                        if (bmi && bmiPosition !== null && bmiPosition !== undefined) {
                          // Calculate which bar index should be highlighted based on BMI position
                          const targetBarIndex = Math.round((currentBMIPosition / 100) * 31);
                          isClosestBar = i === targetBarIndex;
                        }

                        // Determine color based on position
                        let bgColor;
                        if (isClosestBar) {
                          bgColor = 'bg-black'; // Black for the indicator bar
                        } else if (i < 8) {
                          bgColor = 'bg-blue-400'; // Underweight - blue
                        } else if (i < 16) {
                          bgColor = 'bg-green-500'; // Normal - green
                        } else if (i < 24) {
                          bgColor = 'bg-yellow-400'; // Overweight - yellow
                        } else {
                          bgColor = 'bg-red-500'; // Obese - red
                        }

                        return (
                          <div
                            key={`bmi-bar-m-${i}`}
                            className={`h-full w-1.5 ${bgColor} rounded-t-md rounded-b-md relative`}
                          >
                            {/* Add downward-pointing arrow at the top of the black indicator bar */}
                            {isClosestBar && (
                              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 text-black" style={{ fontSize: '10px' }}>
                                ▼
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Category labels with proper spacing */}
                <div className="flex text-xs text-gray-500 mt-3">
                  <div className="w-1/4 text-left">Underweight</div>
                  <div className="w-1/4 text-center">Normal</div>
                  <div className="w-1/4 text-center">Overweight</div>
                  <div className="w-1/4 text-right">Obese</div>
                </div>
              </div>
            </div>

            {/* Goal Progress Section */}
            <div className="bg-white p-4 rounded-2xl shadow-sm mb-4">
              <h2 className="text-lg font-medium mb-4">Goal Progress</h2>

              {/* Current Weight Display */}
              <div className="flex justify-between items-center mb-4">
                <div>
                  <p className="text-sm text-gray-500">Current Weight</p>
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold">
                      {profile.weightKg ? getWeightDisplayValue(profile.weightKg, profile.weight_unit) : '--'}
                    </span>
                    <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                  </div>
                </div>
                {profile.target_weight && (
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Target</p>
                    <div className="flex items-baseline">
                      <span className="text-lg font-semibold text-green-600">
                        {getWeightDisplayValue(profile.target_weight, profile.weight_unit)}
                      </span>
                      <span className="ml-1 text-gray-500">{getWeightUnitLabel(profile.weight_unit)}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Progress Summary */}
              {profile.target_weight && profile.weightKg && (
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-xs text-gray-600">Progress</p>
                      <p className="text-sm font-semibold">
                        {weightProgress !== null ? weightProgress : 0}% Complete
                        {weightProgress === 0 && profile.startingWeightKg && profile.target_weight && (
                          <span className="text-xs text-orange-600 block mt-1">
                            {(() => {
                              const currentWeight = parseFloat(profile.weightKg);
                              const startingWeight = parseFloat(profile.startingWeightKg);
                              const targetWeight = parseFloat(profile.target_weight);
                              const isWeightLossGoal = targetWeight < startingWeight;

                              if (isWeightLossGoal) {
                                // Weight loss goal
                                if (currentWeight === startingWeight) return 'At starting weight';
                                if (currentWeight > startingWeight) return 'Above starting weight';
                                return 'Lost weight but not enough';
                              } else {
                                // Weight gain goal
                                if (currentWeight === startingWeight) return 'At starting weight';
                                if (currentWeight < startingWeight) return 'Below starting weight';
                                return 'Gained weight but not enough';
                              }
                            })()}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-600">
                        {filteredWeightHistory.length > 0 ? `${filteredWeightHistory.length} entries` : 'No data yet'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {weightPeriod === '30days' ? 'Last 30 days' :
                         weightPeriod === '3months' ? 'Last 3 months' :
                         weightPeriod === '6months' ? 'Last 6 months' :
                         weightPeriod === '1year' ? 'Last year' : 'All time'}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex border-b pb-2 mb-4">
                <button
                  className={`flex-1 py-1 px-1 text-xs ${weightPeriod === '30days' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleWeightPeriodSelect('30days')}
                >
                  30D
                </button>
                <button
                  className={`flex-1 py-1 px-1 text-xs ${weightPeriod === '3months' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleWeightPeriodSelect('3months')}
                >
                  3M
                </button>
                <button
                  className={`flex-1 py-1 px-1 text-xs ${weightPeriod === '6months' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleWeightPeriodSelect('6months')}
                >
                  6M
                </button>
                <button
                  className={`flex-1 py-1 px-1 text-xs ${weightPeriod === '1year' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleWeightPeriodSelect('1year')}
                >
                  1Y
                </button>
                <button
                  className={`flex-1 py-1 px-1 text-xs ${weightPeriod === 'alltime' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleWeightPeriodSelect('alltime')}
                >
                  All
                </button>
              </div>

              <WeightProgressChart
                weightData={filteredWeightHistory}
                targetWeight={profile.target_weight}
                weightUnit={profile.weight_unit}
                className="w-full"
              />
            </div>

            {/* Nutrition Section */}
            <div className="bg-white p-4 rounded-2xl shadow-sm">
              <h2 className="text-lg font-medium mb-3">Nutrition</h2>

              <div className="flex border-b pb-2">
                <button
                  className={`flex-1 py-1 px-2 text-sm ${activeTab === 'thisWeek' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleTabSelect('thisWeek')}
                >
                  This week
                </button>
                <button
                  className={`flex-1 py-1 px-2 text-sm ${activeTab === 'lastWeek' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleTabSelect('lastWeek')}
                >
                  Last week
                </button>
                <button
                  className={`flex-1 py-1 px-2 text-sm ${activeTab === 'twoWeeksAgo' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleTabSelect('twoWeeksAgo')}
                >
                  2 wks. ago
                </button>
                <button
                  className={`flex-1 py-1 px-2 text-sm ${activeTab === 'threeWeeksAgo' ? 'bg-black text-white rounded-xl' : ''}`}
                  onClick={() => handleTabSelect('threeWeeksAgo')}
                >
                  3 wks. ago
                </button>
              </div>

              <div className="flex justify-between mt-4">
                <div className="text-center">
                  <p className="text-2xl font-bold">{nutritionData[activeTab].totalCalories.toLocaleString()}</p>
                  <p className="text-sm text-gray-500">Total calories</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold">{nutritionData[activeTab].dailyAvg.toLocaleString()}</p>
                  <p className="text-sm text-gray-500">
                    Daily avg.
                    {nutritionData[activeTab].daysWithData > 0 && (
                      <span className="text-xs ml-1">
                        ({nutritionData[activeTab].daysWithData} day{nutritionData[activeTab].daysWithData !== 1 ? 's' : ''})
                      </span>
                    )}
                  </p>
                </div>
              </div>

              {/* Nutrition Bar Chart */}
              <div className="mt-4 mb-4">
                <NutritionBarChart
                  data={nutritionData[activeTab].dailyData}
                  proteinCalories={nutritionData[activeTab].proteinCalories}
                  carbsCalories={nutritionData[activeTab].carbsCalories}
                  fatsCalories={nutritionData[activeTab].fatsCalories}
                  calorieTarget={calorieTarget}
                  labels={['S', 'M', 'T', 'W', 'T', 'F', 'S']}
                  className="w-full"
                />
              </div>
            </div>

            {/* Settings Button */}
            <div className="bg-white rounded-2xl shadow-sm p-4">
              <div className="flex flex-col space-y-4">
                <h2 className="text-lg font-medium">Account Settings</h2>
                <button
                  onClick={() => navigate('/settings')}
                  className="w-full py-4 px-6 font-bold rounded-2xl transition-all duration-300 bg-black text-white hover:bg-opacity-90"
                >
                  Manage Settings
                </button>
                <button
                  type="button"
                  onClick={handleLogout}
                  className="w-full py-4 px-6 font-bold rounded-2xl transition-all duration-300 border border-gray-300 text-gray-700 hover:bg-gray-50"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation onAddClick={openAddOptions} />

      {/* Weight Input Modal */}
      <WeightInputModal
        isOpen={showWeightModal}
        onClose={() => setShowWeightModal(false)}
        onSave={handleWeightUpdate}
        currentWeight={profile?.weightKg}
        weightUnit={profile?.weightUnit || 'kg'}
      />

      {/* Target Weight Input Modal */}
      <TargetWeightInputModal
        isOpen={showTargetWeightModal}
        onClose={() => setShowTargetWeightModal(false)}
        onSave={handleTargetWeightUpdate}
        currentTargetWeight={profile?.target_weight}
        weightUnit={profile?.weight_unit || 'kg'}
      />

    </div>
  );
};

export default Profile;
