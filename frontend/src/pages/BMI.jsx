import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { useIsMobile } from '../hooks/useIsMobile';

const BMI = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [profile, setProfile] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await axios.get('/api/users/profile');
        setProfile(response.data);
      } catch (error) {
        console.error('Error fetching profile:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Calculate BMI
  const calculateBMI = () => {
    // Check both camelCase and snake_case field names for compatibility
    const height = profile.heightCm || profile.height_cm;
    const weight = profile.weightKg || profile.weight_kg;

    if (!height || !weight ||
        isNaN(parseFloat(height)) ||
        isNaN(parseFloat(weight)) ||
        parseFloat(height) <= 0) {
      return null;
    }

    const heightInMeters = parseFloat(height) / 100;
    const bmi = parseFloat(weight) / (heightInMeters * heightInMeters);

    if (isNaN(bmi) || !isFinite(bmi)) {
      return null;
    }

    return bmi.toFixed(1);
  };

  // Get BMI category
  const getBMICategory = (bmi) => {
    if (!bmi) return null;

    if (bmi < 18.5) return { category: 'Underweight', color: 'underweight' };
    if (bmi < 25) return { category: 'Normal', color: 'normal' };
    if (bmi < 30) return { category: 'Overweight', color: 'overweight' };
    return { category: 'Obese', color: 'obese' };
  };

  // Calculate BMI position on the scale (0-100%)
  const calculateBMIPosition = (bmi) => {
    if (!bmi) return 0;

    const minBMI = 15;
    const maxBMI = 40;
    const range = maxBMI - minBMI;

    let position = ((parseFloat(bmi) - minBMI) / range) * 100;
    position = Math.max(0, Math.min(100, position));

    return position;
  };

  const bmi = calculateBMI();
  const bmiCategory = getBMICategory(bmi);
  const bmiPosition = calculateBMIPosition(bmi);

  // Debug logging
  console.log('BMI Debug:', { bmi, bmiPosition, profile });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mx-auto mb-4"></div>
          <p className="text-gray-600">Loading BMI information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center">
            <button
              onClick={() => navigate('/profile')}
              className="mr-4 p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <h1 className="text-2xl font-bold">BMI Information</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Current BMI Card */}
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Your Current BMI</h2>
          
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <p className="text-gray-500 mr-2">Your Weight is</p>
              <span className={`bg-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-100 text-${bmiCategory?.color === 'normal' ? 'green' : bmiCategory?.color === 'underweight' ? 'blue' : bmiCategory?.color === 'overweight' ? 'yellow' : 'red'}-600 rounded px-3 py-1 text-sm font-medium`}>
                {bmiCategory?.category || 'Normal'}
              </span>
            </div>
          </div>

          <div className="flex items-center mb-6">
            <span className="text-6xl font-bold">{bmi || '26'}</span>
            <span className="text-gray-500 ml-3 text-lg">BMI</span>
          </div>

          {/* BMI Scale */}
          <div className="mt-6">
            <div className="relative">
              <div className="h-8 w-full flex">
                <div className="w-full flex items-center justify-between px-0.5">
                  {Array.from({ length: 32 }).map((_, i) => {
                    const currentBMIPosition = bmiPosition !== null && bmiPosition !== undefined ? bmiPosition : 55;

                    // Find the closest bar to the BMI position
                    let isClosestBar = false;
                    if (bmi && bmiPosition !== null && bmiPosition !== undefined) {
                      // Calculate which bar index should be highlighted based on BMI position
                      const targetBarIndex = Math.round((currentBMIPosition / 100) * 31);
                      isClosestBar = i === targetBarIndex;
                    }

                    let bgColor;
                    if (isClosestBar) {
                      bgColor = 'bg-black';
                    } else if (i < 8) {
                      bgColor = 'bg-blue-400';
                    } else if (i < 16) {
                      bgColor = 'bg-green-500';
                    } else if (i < 24) {
                      bgColor = 'bg-yellow-400';
                    } else {
                      bgColor = 'bg-red-500';
                    }

                    return (
                      <div
                        key={`bmi-bar-${i}`}
                        className={`h-full w-2 ${bgColor} rounded-t-md rounded-b-md relative`}
                      >
                        {isClosestBar && (
                          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 text-black text-xs">
                            ▼
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Category labels */}
            <div className="flex text-sm text-gray-500 mt-4">
              <div className="w-1/4 text-left">Underweight</div>
              <div className="w-1/4 text-center">Normal</div>
              <div className="w-1/4 text-center">Overweight</div>
              <div className="w-1/4 text-right">Obese</div>
            </div>

            {/* BMI ranges */}
            <div className="flex text-xs text-gray-400 mt-2">
              <div className="w-1/4 text-left">&lt; 18.5</div>
              <div className="w-1/4 text-center">18.5 - 24.9</div>
              <div className="w-1/4 text-center">25.0 - 29.9</div>
              <div className="w-1/4 text-right">≥ 30.0</div>
            </div>
          </div>
        </div>

        {/* What is BMI Section */}
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">What is BMI?</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              Body Mass Index (BMI) is a person's weight in kilograms divided by the square of height in meters. 
              BMI is an inexpensive and easy screening method for weight category—underweight, healthy weight, overweight, and obesity.
            </p>
            <p>
              BMI does not measure body fat directly, but BMI is moderately correlated with more direct measures of body fat. 
              Furthermore, BMI appears to be as strongly correlated with various metabolic and disease outcome as are these more direct measures of body fatness.
            </p>
          </div>
        </div>

        {/* Why BMI Matters Section */}
        <div className="bg-white rounded-2xl shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Why BMI Matters</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              BMI can be a screening tool, but it is not a diagnostic tool. For example, a person may have a high BMI. 
              However, to determine if excess weight is a health risk, a healthcare provider would need to perform further assessments.
            </p>
            <p>
              These assessments might include skinfold thickness measurements, evaluations of diet, physical activity, 
              family history, and other appropriate health screenings.
            </p>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Important Note:</h3>
              <p className="text-blue-800 text-sm">
                BMI is just one factor in assessing health. It doesn't account for muscle mass, bone density, 
                overall body composition, and racial and sex differences. Always consult with healthcare professionals 
                for a comprehensive health assessment.
              </p>
            </div>
          </div>
        </div>

        {/* Disclaimer Section */}
        <div className="bg-yellow-50 rounded-2xl p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-900">Disclaimer</h2>
          <div className="space-y-3 text-yellow-800 text-sm">
            <p>
              <strong>This BMI calculator is for informational purposes only and should not be used as a substitute for professional medical advice.</strong>
            </p>
            <p>
              BMI calculations may not be accurate for athletes, pregnant women, elderly individuals, or people with certain medical conditions. 
              The results should be interpreted in conjunction with other health assessments.
            </p>
            <p>
              Always consult with a qualified healthcare provider before making any decisions about your health, diet, or exercise routine.
            </p>
          </div>
        </div>

        {/* Source Section */}
        <div className="bg-white rounded-2xl shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Source</h2>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-700 mb-2">
                Information provided by the Centers for Disease Control and Prevention (CDC)
              </p>
              <p className="text-sm text-gray-500">
                For more detailed information about BMI, visit the official CDC website.
              </p>
            </div>
            <a
              href="https://www.cdc.gov/bmi/index.html"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Visit CDC
              <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BMI;
