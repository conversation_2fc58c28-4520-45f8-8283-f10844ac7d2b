import { useState, useEffect } from 'react';
import axios from 'axios';
import { getRandomMealImage } from '../assets/images/food/foodImageUrls';
import { useIsMobile } from '../hooks/useIsMobile';
import { useGlobalLoading } from '../hooks/useGlobalLoading';
import { useAuth } from '../hooks/useAuth';
import { useApiConfig } from '../context/ApiConfigContext';
import { format, isSameDay } from 'date-fns';
import { formatLocalDateToISO, logDateInfo } from '../utils/dateUtils';

// UI components
import CircularProgress from '../components/ui/CircularProgress';
import MacroCard from '../components/ui/MacroCard';
import MealItem from '../components/ui/MealItem';
import CalendarWithTodo from '../components/ui/CalendarStrip';
import CaloriesLeftCard from '../components/ui/CalorieProgressCard';
import BottomNavigation from '../components/ui/BottomNavigation';
import FoodScanner from '../components/scanner/FoodScanner';
import AnalyzingFoodItem from '../components/ui/AnalyzingFoodItem';
import OllamaAnalyzingFoodItem from '../components/ui/OllamaAnalyzingFoodItem';
import ApiSetupModal from '../components/modals/ApiSetupModal';
import { useAddOptionsContext } from '../context/AddOptionsContext';


import { ProteinIcon, CarbsIcon, FatsIcon } from '../assets/icons/DashboardIcons';

const Dashboard = () => {
  const isMobile = useIsMobile();
  const globalLoading = useGlobalLoading();
  const { config: apiConfig, loading: apiLoading, needsReauth } = useApiConfig();
  const { logout } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [currentGoal, setCurrentGoal] = useState(null);
  const [todaysMeals, setTodaysMeals] = useState([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedDateMeals, setSelectedDateMeals] = useState([]);
  const [selectedDateLoading, setSelectedDateLoading] = useState(false);
  const [previousDaysData, setPreviousDaysData] = useState([]);
  const [expandedMealId, setExpandedMealId] = useState(null);
  const [analyzingFoodItems, setAnalyzingFoodItems] = useState([]);
  const [streak, setStreak] = useState(0);
  const [streakLoading, setStreakLoading] = useState(true);
  const [showApiSetupModal, setShowApiSetupModal] = useState(false);
  const { openAddOptions } = useAddOptionsContext();

  // Handle meal card expansion (only for mobile)
  const handleMealExpand = (mealId) => {
    // Only handle expansion for mobile view
    if (!isMobile) return;

    if (expandedMealId === mealId) {
      // If clicking the already expanded card, collapse it
      setExpandedMealId(null);
    } else {
      // Otherwise, expand the clicked card
      setExpandedMealId(mealId);

      // Scroll to the expanded card
      setTimeout(() => {
        const element = document.getElementById(`meal-${mealId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  };

  // Handle analyzing food items from scanner
  useEffect(() => {
    // Load existing analyzing items from localStorage on mount
    try {
      const existingAnalyzingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');

      // Filter out items with invalid blob URLs and ensure all items have required properties
      const validItems = Array.isArray(existingAnalyzingItems) ? existingAnalyzingItems.filter(item => {
        // Check if item has required properties
        if (!item || typeof item !== 'object' || !item.id || !item.name) {
          console.warn('Removing invalid analyzing item:', item);
          return false;
        }

        // Check if blob URL is still valid
        if (item.image_url && item.image_url.startsWith('blob:')) {
          // For blob URLs, we'll keep them but they might fail to load
          // The image error handler will show a fallback
          return true;
        }

        return true;
      }) : [];

      // Update localStorage if we filtered out any items
      if (validItems.length !== existingAnalyzingItems.length) {
        localStorage.setItem('analyzingFoodItems', JSON.stringify(validItems));
      }

      setAnalyzingFoodItems(validItems);
    } catch (error) {
      console.error('Error loading analyzing food items:', error);
      setAnalyzingFoodItems([]);
      // Clear corrupted localStorage
      localStorage.removeItem('analyzingFoodItems');
    }

    // Listen for new analyzing food items
    const handleNewAnalyzingFood = (event) => {
      const newItem = event.detail;
      if (newItem && newItem.id && newItem.name) {
        setAnalyzingFoodItems(prev => Array.isArray(prev) ? [...prev, newItem] : [newItem]);
      } else {
        console.warn('Invalid analyzing food item received:', newItem);
      }
    };

    // Listen for completed food analysis
    const handleFoodAnalysisComplete = async (event) => {
      const { analyzingId, analyzedFood } = event.detail;

      console.log('===== FOOD ANALYSIS COMPLETE EVENT =====');
      console.log('Analyzing ID:', analyzingId);
      console.log('Analyzed Food:', JSON.stringify(analyzedFood, null, 2));
      console.log('Event detail:', JSON.stringify(event.detail, null, 2));
      console.log('Current analyzing items:', analyzingFoodItems);
      console.log('Current selected date:', selectedDate);
      console.log('Is today?', isSameDay(selectedDate, new Date()));



      // Validate that we have valid analyzed food data
      if (!analyzedFood || !analyzedFood.name || analyzedFood.name === 'Analyzing food...' || !analyzedFood.calories) {
        console.error('Invalid analyzed food data received:', analyzedFood);

        // Remove the analyzing item and show error
        setAnalyzingFoodItems(prev => Array.isArray(prev) ? prev.filter(item => item && item.id !== analyzingId) : []);
        const existingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
        const updatedItems = existingItems.filter(item => item.id !== analyzingId);
        localStorage.setItem('analyzingFoodItems', JSON.stringify(updatedItems));

        // Don't save invalid data as a meal
        console.log('Analysis failed - not saving meal with invalid data');
        return;
      }

      // Remove the analyzing item
      setAnalyzingFoodItems(prev => Array.isArray(prev) ? prev.filter(item => item && item.id !== analyzingId) : []);

      // Remove from localStorage
      const existingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
      const updatedItems = existingItems.filter(item => item.id !== analyzingId);
      localStorage.setItem('analyzingFoodItems', JSON.stringify(updatedItems));

      // Add the analyzed food as a meal only if data is valid
      try {

        const mealData = {
          name: analyzedFood.name,
          calories: analyzedFood.calories,
          protein_g: analyzedFood.protein_g,
          carbs_g: analyzedFood.carbs_g,
          fats_g: analyzedFood.fats_g,
          fiber_g: analyzedFood.fiber_g,
          sugar_g: analyzedFood.sugar_g,
          sodium_mg: analyzedFood.sodium_mg,
          cholesterol_mg: analyzedFood.cholesterol_mg,
          saturated_fat_g: analyzedFood.saturated_fat_g,
          trans_fat_g: analyzedFood.trans_fat_g,
          vitamin_c_mg: analyzedFood.vitamin_c_mg,
          calcium_mg: analyzedFood.calcium_mg,
          iron_mg: analyzedFood.iron_mg,
          potassium_mg: analyzedFood.potassium_mg,
          vitamin_d_mcg: analyzedFood.vitamin_d_mcg,
          serving_size: analyzedFood.serving_size,
          items: analyzedFood.items || [],
          timestamp: new Date().toISOString(),
          image_url: analyzedFood.image_url,
          is_server_image: analyzedFood.is_server_image
        };

        console.log('===== DASHBOARD MEAL DATA DEBUG =====');
        console.log('Analyzed food items:', analyzedFood.items);
        console.log('Meal data items:', mealData.items);

        const response = await axios.post('/api/meals', mealData);
        const newMeal = response.data;

        // Add image_url and items if not present
        if (!newMeal.image_url && analyzedFood.image_url) {
          newMeal.image_url = analyzedFood.image_url;
        }
        if (!newMeal.items && analyzedFood.items) {
          newMeal.items = analyzedFood.items;
        }

        console.log('===== NEW MEAL WITH ITEMS =====');
        console.log('New meal items:', newMeal.items);

        // Update today's meals if we're viewing today
        const today = new Date();
        if (isSameDay(today, new Date())) {
          setTodaysMeals(prev => [newMeal, ...prev]);
        }

        // Update selected date meals if the meal is for the currently selected date
        const mealDate = new Date(newMeal.timestamp);
        if (isSameDay(selectedDate, mealDate)) {
          setSelectedDateMeals(prev => [newMeal, ...prev]);
        }

        console.log('Successfully added analyzed meal:', newMeal);
      } catch (error) {
        console.error('Error adding analyzed meal:', error);
      }
    };

    // Listen for analysis errors
    const handleFoodAnalysisError = (event) => {
      const { analyzingId, error } = event.detail;
      console.error('Food analysis failed:', error);

      // Remove the analyzing item
      setAnalyzingFoodItems(prev => Array.isArray(prev) ? prev.filter(item => item && item.id !== analyzingId) : []);

      // Remove from localStorage
      const existingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
      const updatedItems = existingItems.filter(item => item.id !== analyzingId);
      localStorage.setItem('analyzingFoodItems', JSON.stringify(updatedItems));

      // Show error message to user
      alert(`Food analysis failed: ${error}. Please try again with a clearer image.`);
    };

    window.addEventListener('newAnalyzingFood', handleNewAnalyzingFood);
    window.addEventListener('foodAnalysisComplete', handleFoodAnalysisComplete);
    window.addEventListener('foodAnalysisError', handleFoodAnalysisError);

    // Set up a periodic check for stuck analyzing items
    const checkStuckItems = setInterval(() => {
      const currentItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
      const now = Date.now();

      currentItems.forEach(item => {
        if (item && item.timestamp) {
          const itemAge = now - new Date(item.timestamp).getTime();
          // If item is older than 15 minutes, consider it stuck
          if (itemAge > 900000) {
            console.warn('Found stuck analyzing item:', item.id, 'Age:', itemAge / 1000 / 60, 'minutes');
            // Dispatch error event to clean it up
            window.dispatchEvent(new CustomEvent('foodAnalysisError', {
              detail: {
                analyzingId: item.id,
                error: 'Analysis appears to be stuck. Cleaning up...'
              }
            }));
          }
        }
      });
    }, 60000); // Check every minute

    return () => {
      window.removeEventListener('newAnalyzingFood', handleNewAnalyzingFood);
      window.removeEventListener('foodAnalysisComplete', handleFoodAnalysisComplete);
      window.removeEventListener('foodAnalysisError', handleFoodAnalysisError);
      clearInterval(checkStuckItems);
    };
  }, []); // Remove selectedDate dependency to prevent event listener re-registration

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        globalLoading.fetchingData();

        // Log current date information for debugging
        const now = new Date();
        logDateInfo(now, 'Dashboard Initialization');

        // Get today's date in local timezone and format it properly
        const localToday = formatLocalDateToISO(now).split('T')[0];
        console.log('Using local today date for API calls:', localToday);

        // Fetch user profile
        const profileResponse = await axios.get('/api/users/profile');
        setUserProfile(profileResponse.data);

        // Fetch current goal
        const goalResponse = await axios.get('/api/goals/current');
        console.log('Goal data received:', goalResponse.data);
        setCurrentGoal(goalResponse.data);

        // Fetch today's meals using the local date
        const mealsResponse = await axios.get(`/api/meals?start_date=${localToday}&end_date=${localToday}`);

        // Add image URLs to meals that don't have them
        const mealsWithImages = mealsResponse.data.map(meal => {
          console.log('Processing meal:', meal.id, 'Image URL:', meal.imageUrl);

          // Check for imageUrl (from backend) or image_url (frontend property)
          if (!meal.imageUrl && !meal.image_url) {
            // Determine meal type based on time
            const hour = new Date(meal.timestamp).getHours();
            let mealType = 'snack';

            if (hour >= 5 && hour < 10) mealType = 'breakfast';
            else if (hour >= 10 && hour < 15) mealType = 'lunch';
            else if (hour >= 15 && hour < 21) mealType = 'dinner';

            // Get random image for meal type
            const randomImage = getRandomMealImage(mealType);
            console.log('Using random image for meal:', meal.id, 'Image:', randomImage);

            return {
              ...meal,
              // Use a random image since no image URL is available
              image_url: randomImage
            };
          }

          // If the meal has imageUrl from backend but no image_url for frontend, add it
          if (meal.imageUrl && !meal.image_url) {
            console.log('Using backend imageUrl for meal:', meal.id, 'Image:', meal.imageUrl);

            return {
              ...meal,
              image_url: meal.imageUrl
            };
          }

          return meal;
        });

        // Log meals for debugging
        console.log('Meals with images:', mealsWithImages.map(meal => ({
          id: meal.id,
          name: meal.name,
          imageUrl: meal.imageUrl,
          image_url: meal.image_url
        })));

        setTodaysMeals(mealsWithImages);
        // Also set selected date meals to today's meals initially
        setSelectedDateMeals(mealsWithImages);

        // Fetch previous 7 days data for calendar
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        // Format the date using our utility to ensure correct local date
        const startDate = formatLocalDateToISO(sevenDaysAgo).split('T')[0];

        console.log('Fetching previous days data from', startDate, 'to', localToday);

        try {
          const previousDaysResponse = await axios.get(`/api/meals/summary?start_date=${startDate}&end_date=${localToday}`);
          // Add hasData property to each day's data
          const processedData = previousDaysResponse.data.map(day => ({
            ...day,
            hasData: day.totalCalories > 0
          }));
          console.log('Processed previous days data:', processedData);
          setPreviousDaysData(processedData);
        } catch (error) {
          console.error('Error fetching previous days data:', error);
          // If API fails, just set an empty array
          setPreviousDaysData([]);
        }

        setLoading(false);
        globalLoading.stop.data();
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError('Failed to load dashboard data');
        setLoading(false);
        globalLoading.stop.data();
      }
    };

    const fetchStreak = async () => {
      try {
        setStreakLoading(true);
        const response = await axios.get('/api/users/streak');
        setStreak(response.data.streak);
      } catch (error) {
        console.error('Error fetching streak:', error);
        setStreak(0); // Default to 0 if error
      } finally {
        setStreakLoading(false);
      }
    };

    fetchDashboardData();
    fetchStreak();
  }, []);



  // Check for API configuration after loading
  useEffect(() => {
    if (!apiLoading && !loading && (!apiConfig.isConfigured || needsReauth)) {
      // Show API setup modal for new users or users who need to re-authenticate
      setShowApiSetupModal(true);
    }
  }, [apiLoading, loading, apiConfig.isConfigured, needsReauth]);

  // Calculate total calories consumed today
  const calculateTotalCalories = () => {
    return todaysMeals.reduce((total, meal) => total + meal.calories, 0);
  };

  // Calculate remaining calories
  const calculateRemainingCalories = () => {
    if (!currentGoal || typeof currentGoal.dailyCalorieGoal !== 'number') return 0;
    return currentGoal.dailyCalorieGoal - calculateTotalCalories();
  };

  // Calculate macronutrient totals for today
  const calculateMacros = () => {
    return {
      protein: todaysMeals.reduce((total, meal) => {
        // Handle both naming conventions (proteinG from backend, protein_g from frontend)
        const proteinValue = parseFloat(meal.proteinG || meal.protein_g) || 0;
        return total + proteinValue;
      }, 0),
      carbs: todaysMeals.reduce((total, meal) => {
        // Handle both naming conventions (carbsG from backend, carbs_g from frontend)
        const carbsValue = parseFloat(meal.carbsG || meal.carbs_g) || 0;
        return total + carbsValue;
      }, 0),
      fat: todaysMeals.reduce((total, meal) => {
        // Handle both naming conventions (fatsG from backend, fats_g from frontend)
        const fatValue = parseFloat(meal.fatsG || meal.fats_g) || 0;
        return total + fatValue;
      }, 0),
    };
  };

  // Calculate macronutrient percentages for the progress circle
  const calculateMacroPercentages = () => {
    const macros = calculateMacros();
    const totalMacroCalories =
      (macros.protein * 4) + // 4 calories per gram of protein
      (macros.carbs * 4) +   // 4 calories per gram of carbs
      (macros.fat * 9);      // 9 calories per gram of fat

    if (totalMacroCalories === 0) {
      return {
        protein: 33,
        carbs: 34,
        fat: 33
      };
    }

    return {
      protein: Math.round((macros.protein * 4 / totalMacroCalories) * 100),
      carbs: Math.round((macros.carbs * 4 / totalMacroCalories) * 100),
      fat: Math.round((macros.fat * 9 / totalMacroCalories) * 100)
    };
  };

  // Calculate macronutrient percentages for selected date
  const calculateSelectedDateMacroPercentages = () => {
    const macros = calculateSelectedDateMacros();
    const totalMacroCalories =
      (macros.protein * 4) + // 4 calories per gram of protein
      (macros.carbs * 4) +   // 4 calories per gram of carbs
      (macros.fat * 9);      // 9 calories per gram of fat

    if (totalMacroCalories === 0) {
      return {
        protein: 33,
        carbs: 34,
        fat: 33
      };
    }

    return {
      protein: Math.round((macros.protein * 4 / totalMacroCalories) * 100),
      carbs: Math.round((macros.carbs * 4 / totalMacroCalories) * 100),
      fat: Math.round((macros.fat * 9 / totalMacroCalories) * 100)
    };
  };

  // Calculate total calories for selected date
  const calculateSelectedDateTotalCalories = () => {
    return selectedDateMeals.reduce((total, meal) => total + meal.calories, 0);
  };

  // Calculate remaining calories for selected date
  const calculateSelectedDateRemainingCalories = () => {
    if (!currentGoal || typeof currentGoal.dailyCalorieGoal !== 'number') return 0;
    return currentGoal.dailyCalorieGoal - calculateSelectedDateTotalCalories();
  };

  // Calculate macronutrient totals for selected date
  const calculateSelectedDateMacros = () => {
    return {
      protein: selectedDateMeals.reduce((total, meal) => {
        // Handle both naming conventions (proteinG from backend, protein_g from frontend)
        const proteinValue = parseFloat(meal.proteinG || meal.protein_g) || 0;
        return total + proteinValue;
      }, 0),
      carbs: selectedDateMeals.reduce((total, meal) => {
        // Handle both naming conventions (carbsG from backend, carbs_g from frontend)
        const carbsValue = parseFloat(meal.carbsG || meal.carbs_g) || 0;
        return total + carbsValue;
      }, 0),
      fat: selectedDateMeals.reduce((total, meal) => {
        // Handle both naming conventions (fatsG from backend, fats_g from frontend)
        const fatValue = parseFloat(meal.fatsG || meal.fats_g) || 0;
        return total + fatValue;
      }, 0),
    };
  };

  // This function is used in the MacroCard components to calculate remaining macros

  // We'll rely on the API to provide accurate data for all dates
  // No need to manually update the previousDaysData

  // Handle adding a new meal
  const handleAddMeal = () => {
    // Use the global add options modal
    openAddOptions();
  };

  // Handle deleting a meal
  const handleDeleteMeal = async (mealId) => {
    if (!window.confirm('Are you sure you want to delete this meal?')) {
      return;
    }

    try {
      // Call API to delete the meal
      await axios.delete(`/api/meals/${mealId}`);

      // Update the UI by removing the deleted meal from selected date meals
      setSelectedDateMeals(selectedDateMeals.filter(meal => meal.id !== mealId));

      // Also update today's meals if the selected date is today
      if (isSameDay(selectedDate, new Date())) {
        setTodaysMeals(todaysMeals.filter(meal => meal.id !== mealId));
      }

      // Reset expanded meal if the deleted meal was expanded
      if (expandedMealId === mealId) {
        setExpandedMealId(null);
      }

      // Update the calendar data to reflect the deletion
      // This would require refetching the summary data, but for simplicity we'll skip it for now
    } catch (error) {
      console.error('Error deleting meal:', error);
      alert('Failed to delete meal. Please try again.');
    }
  };

  // Handle canceling analyzing food item
  const handleCancelAnalyzing = (analyzingId) => {
    // Remove the analyzing item
    setAnalyzingFoodItems(prev => Array.isArray(prev) ? prev.filter(item => item && item.id !== analyzingId) : []);

    // Remove from localStorage
    const existingItems = JSON.parse(localStorage.getItem('analyzingFoodItems') || '[]');
    const updatedItems = existingItems.filter(item => item.id !== analyzingId);
    localStorage.setItem('analyzingFoodItems', JSON.stringify(updatedItems));
  };

  // Handle date selection
  const handleDateSelect = async (date) => {
    setSelectedDate(date);
    // Reset expanded meal when changing dates
    setExpandedMealId(null);

    // Check if selected date is today, if so, use today's meals
    const today = new Date();
    if (isSameDay(date, today)) {
      // Use the current todaysMeals state
      setSelectedDateMeals(todaysMeals);

      // Optionally, we could refresh today's data here to ensure it's up-to-date
      try {
        // Use our utility to ensure correct local date
        const localFormattedToday = formatLocalDateToISO(today).split('T')[0];
        console.log('Refreshing today\'s data with date:', localFormattedToday);
        const mealsResponse = await axios.get(`/api/meals?date=${localFormattedToday}`);

        // Process meals with images
        const mealsWithImages = mealsResponse.data.map(meal => {
          if (!meal.imageUrl && !meal.image_url) {
            const hour = new Date(meal.timestamp).getHours();
            let mealType = 'snack';
            if (hour >= 5 && hour < 10) mealType = 'breakfast';
            else if (hour >= 10 && hour < 15) mealType = 'lunch';
            else if (hour >= 15 && hour < 21) mealType = 'dinner';
            return { ...meal, image_url: getRandomMealImage(mealType) };
          }
          if (meal.imageUrl && !meal.image_url) {
            return { ...meal, image_url: meal.imageUrl };
          }
          return meal;
        });

        // Update both today's meals and selected date meals
        setTodaysMeals(mealsWithImages);
        setSelectedDateMeals(mealsWithImages);
      } catch (error) {
        console.error('Error refreshing today\'s meals:', error);
      }
      return;
    }

    // Fetch meals for the selected date
    try {
      setSelectedDateLoading(true);
      // Use our utility to ensure correct local date
      const localFormattedDate = formatLocalDateToISO(date).split('T')[0];
      console.log('Fetching meals for selected date:', localFormattedDate);
      const mealsResponse = await axios.get(`/api/meals?date=${localFormattedDate}`);

      // Add image URLs to meals that don't have them
      const mealsWithImages = mealsResponse.data.map(meal => {
        // Check for imageUrl (from backend) or image_url (frontend property)
        if (!meal.imageUrl && !meal.image_url) {
          // Determine meal type based on time
          const hour = new Date(meal.timestamp).getHours();
          let mealType = 'snack';

          if (hour >= 5 && hour < 10) mealType = 'breakfast';
          else if (hour >= 10 && hour < 15) mealType = 'lunch';
          else if (hour >= 15 && hour < 21) mealType = 'dinner';

          // Get random image for meal type
          const randomImage = getRandomMealImage(mealType);

          return {
            ...meal,
            // Use a random image since no image URL is available
            image_url: randomImage
          };
        }

        // If the meal has imageUrl from backend but no image_url for frontend, add it
        if (meal.imageUrl && !meal.image_url) {
          return {
            ...meal,
            image_url: meal.imageUrl
          };
        }

        return meal;
      });

      setSelectedDateMeals(mealsWithImages);
    } catch (error) {
      console.error('Error fetching meals for selected date:', error);
      setSelectedDateMeals([]);
    } finally {
      setSelectedDateLoading(false);
    }
  };

  // Main loading is now handled by global loading indicator

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl">
          <h3 className="text-lg font-medium mb-2">Error</h3>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const macros = calculateMacros();
  const macroPercentages = calculateMacroPercentages();
  // Calculate remaining macros for display in the nutrition breakdown
  const calorieGoal = currentGoal?.dailyCalorieGoal || 2000; // Default to 2000 if not set
  const caloriesRemaining = calculateRemainingCalories();
  const caloriesConsumed = calculateTotalCalories();
  const caloriePercentage = Math.min(100, Math.round((caloriesConsumed / calorieGoal) * 100));



  return (
    <div className={`min-h-screen ${isMobile ? 'pb-24' : 'pb-20'}`}>
      {/* Header */}
      <header className="flex justify-between items-center p-4">
        {/* Left side - Streak */}
        <div className="bg-orange-100 text-orange-500 px-3 py-1 rounded-full flex items-center text-sm">
          <span className="mr-1">🔥</span>
          <span className="font-medium">
            {streakLoading ? 'Loading...' : `Day ${streak}`}
          </span>
        </div>

        {/* Right side - Status and Settings */}
        <div className="flex items-center gap-2">
          <div className="bg-highlight-orange text-white px-3 py-1 rounded-full text-sm">
            {userProfile?.goalType === 'lose' ? 'Losing' : userProfile?.goalType === 'gain' ? 'Gaining' : 'Maintaining'}
          </div>
          {!isMobile && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          )}
        </div>
      </header>

      {/* Content */}
      <div className={`${isMobile ? 'px-4' : 'px-4 py-4 max-w-7xl mx-auto'}`}>
        {/* Main Layout */}
        <div>
          {/* Main Content */}
          <div>
            {/* Calendar with Daily Summary Card */}
            <div className="mb-6 w-full">
              <CalendarWithTodo
                onDateSelect={handleDateSelect}
                previousDaysData={previousDaysData}
                calorieGoal={calorieGoal}
                consumedCalories={calculateSelectedDateTotalCalories()}
                remainingCalories={calculateSelectedDateRemainingCalories()}
                selectedDate={selectedDate}
                className="w-full"
              />
            </div>

            {/* Calories Left Card */}
            <div className="mb-6 w-full">
              <CaloriesLeftCard
                selectedDate={selectedDate}
                calorieGoal={calorieGoal}
                consumedCalories={calculateSelectedDateTotalCalories()}
                remainingCalories={calculateSelectedDateRemainingCalories()}
                macroPercentages={calculateSelectedDateMacroPercentages()}
                className="w-full"
              />
            </div>

            {/* Macros Cards */}
            <div className="grid grid-cols-3 gap-3 mb-6 w-full">
              <MacroCard
                type="protein"
                value={calculateSelectedDateMacros().protein}
                total={Math.round(calorieGoal * 0.3 / 4)} // 30% of calories from protein
                className="w-full"
              />
              <MacroCard
                type="carbs"
                value={calculateSelectedDateMacros().carbs}
                total={Math.round(calorieGoal * 0.4 / 4)} // 40% of calories from carbs
                className="w-full"
              />
              <MacroCard
                type="fat"
                value={calculateSelectedDateMacros().fat}
                total={Math.round(calorieGoal * 0.3 / 9)} // 30% of calories from fat
                className="w-full"
              />
            </div>



            {/* Recently Logged */}
            <div className="mb-5">
              <h2 className="text-xl font-medium text-gray-700 mb-5">
                {isSameDay(selectedDate, new Date())
                  ? "Recently logged"
                  : `Meals on ${format(selectedDate, 'MMM d')}`}
              </h2>
              {selectedDateMeals.length === 0 && analyzingFoodItems.length === 0 ? (
                <div className="text-center py-8 bg-white rounded-2xl shadow-sm text-gray-500">
                  <p>No meals logged on {format(selectedDate, 'MMMM d, yyyy')}</p>
                  {isSameDay(selectedDate, new Date()) && (
                    <>
                      <p className="text-sm mt-2">Start tracking your food intake by adding a meal</p>
                      <button
                        onClick={handleAddMeal}
                        className="mt-4 px-4 py-2 bg-black text-white rounded-xl hover:bg-opacity-90"
                      >
                        Add Your First Meal
                      </button>
                    </>
                  )}
                </div>
              ) : (
                <div className={`grid gap-6 mx-auto ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-1 lg:grid-cols-2'} w-full relative`}>
                  {/* Show analyzing food items first (only for today) */}
                  {isSameDay(selectedDate, new Date()) && analyzingFoodItems && Array.isArray(analyzingFoodItems) && analyzingFoodItems.map((item) => {
                    // Check if this item was analyzed using Ollama
                    const isOllamaAnalysis = item.apiProvider === 'ollama' || (apiConfig && apiConfig.provider === 'ollama');

                    return (
                      <div key={item.id} className="relative" id={`analyzing-${item.id}`}>
                        {isOllamaAnalysis ? (
                          <OllamaAnalyzingFoodItem
                            food={item}
                            requestId={item.id}
                            isMobile={isMobile}
                            onCancel={() => handleCancelAnalyzing(item.id)}
                          />
                        ) : (
                          <AnalyzingFoodItem
                            item={item}
                            isMobile={isMobile}
                            onCancel={() => handleCancelAnalyzing(item.id)}
                          />
                        )}
                      </div>
                    );
                  })}

                  {/* Show regular meals */}
                  {selectedDateMeals && Array.isArray(selectedDateMeals) && selectedDateMeals.map((meal) => (
                    <div key={meal.id} className="relative" id={`meal-${meal.id}`}>
                      <MealItem
                        name={meal.name}
                        calories={meal.calories}
                        time={format(new Date(meal.timestamp), 'h:mm aa')}
                        macros={{
                          protein: Math.round(meal.proteinG || meal.protein_g || 0),
                          carbs: Math.round(meal.carbsG || meal.carbs_g || 0),
                          fat: Math.round(meal.fatsG || meal.fats_g || 0)
                        }}
                        nutrition={{
                          fiber_g: meal.fiberG || meal.fiber_g || null,
                          sugar_g: meal.sugarG || meal.sugar_g || null,
                          sodium_mg: meal.sodiumMg || meal.sodium_mg || null,
                          cholesterol_mg: meal.cholesterolMg || meal.cholesterol_mg || null,
                          saturated_fat_g: meal.saturatedFatG || meal.saturated_fat_g || null,
                          trans_fat_g: meal.transFatG || meal.trans_fat_g || null,
                          vitamin_c_mg: meal.vitaminCMg || meal.vitamin_c_mg || null,
                          calcium_mg: meal.calciumMg || meal.calcium_mg || null,
                          iron_mg: meal.ironMg || meal.iron_mg || null,
                          potassium_mg: meal.potassiumMg || meal.potassium_mg || null,
                          vitamin_d_mcg: meal.vitaminDMcg || meal.vitamin_d_mcg || null
                        }}
                        image={meal.imageUrl || meal.image_url}
                        items={meal.items || []}
                        onDelete={() => handleDeleteMeal(meal.id)}
                        expanded={expandedMealId === meal.id}
                        onExpand={() => handleMealExpand(meal.id)}
                        isMobile={isMobile}
                        className="w-full h-full"
                      />
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation onAddClick={openAddOptions} />

      {/* API Setup Modal */}
      <ApiSetupModal
        isOpen={showApiSetupModal}
        onClose={() => setShowApiSetupModal(false)}
        isRequired={true}
      />

    </div>
  );
};

export default Dashboard;
