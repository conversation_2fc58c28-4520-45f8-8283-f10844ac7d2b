import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { isAppInstalled } from '../utils/registerSW';

/**
 * PWA Information Page
 * Provides information about the PWA features and installation instructions
 */
const PwaInfo = () => {
  const [isInstalled, setIsInstalled] = useState(false);
  const [isIOS, setIsIOS] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Check if app is installed as PWA
    setIsInstalled(isAppInstalled());

    // Check device type
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    setIsIOS(/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream);
    setIsAndroid(/android/i.test(userAgent));

    // Check online status
    setIsOnline(navigator.onLine);

    // Add event listeners for online/offline events
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Calcounta Progressive Web App
          </h1>
          <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
            Learn about our PWA features and how to install the app on your device
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          {/* Current Status */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Current Status
            </h2>
            <ul className="space-y-2">
              <li className="flex items-center">
                <span className={`inline-block w-3 h-3 rounded-full mr-2 ${isOnline ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span className="text-gray-700 dark:text-gray-300">
                  {isOnline ? 'Online' : 'Offline'}
                </span>
              </li>
              <li className="flex items-center">
                <span className={`inline-block w-3 h-3 rounded-full mr-2 ${isInstalled ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
                <span className="text-gray-700 dark:text-gray-300">
                  {isInstalled ? 'Installed as PWA' : 'Not installed as PWA'}
                </span>
              </li>
            </ul>
          </div>

          {/* PWA Features */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              PWA Features
            </h2>
            <ul className="space-y-3 text-gray-700 dark:text-gray-300">
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Offline Access:</strong> Continue using Calcounta even without an internet connection. Your data is stored locally and syncs when you're back online.</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Fast Loading:</strong> Faster startup and navigation after installation. The app loads instantly without waiting for network requests.</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Home Screen Access:</strong> Launch directly from your device's home screen like a native app, with a full-screen experience and no browser UI.</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Background Sync:</strong> Data entered while offline will automatically sync when you're back online, ensuring you never lose your entries.</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Automatic Updates:</strong> The app automatically updates in the background, ensuring you always have the latest features and fixes.</span>
              </li>
              <li className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span><strong>Reduced Data Usage:</strong> After installation, the app only downloads new or changed content, saving your data plan.</span>
              </li>
            </ul>
          </div>

          {/* Installation Instructions */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Installation Instructions
            </h2>

            {isIOS && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                  </svg>
                  iOS Installation
                </h3>
                <ol className="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                  <li>Open Calcounta in <strong>Safari</strong> (other browsers are not supported)</li>
                  <li>Tap the Share icon <span className="inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                    </svg>
                  </span> at the bottom of the screen</li>
                  <li>Scroll down and tap <strong>"Add to Home Screen"</strong></li>
                  <li>Tap <strong>"Add"</strong> in the top right corner</li>
                  <li>Find the app icon on your home screen and tap to launch</li>
                </ol>
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Note:</strong> On iOS, you must use Safari. Chrome, Firefox, and other browsers on iOS do not support PWA installation.
                  </p>
                </div>
              </div>
            )}

            {isAndroid && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                  </svg>
                  Android Installation
                </h3>
                <ol className="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                  <li>Open Calcounta in <strong>Chrome</strong> or another supported browser</li>
                  <li>Tap the menu icon (three dots) in the top right</li>
                  <li>Tap <strong>"Install app"</strong> or <strong>"Add to Home screen"</strong></li>
                  <li>Follow the on-screen instructions to complete installation</li>
                  <li>Find the app icon on your home screen and tap to launch</li>
                </ol>
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Tip:</strong> If you don't see the install option, try refreshing the page or visiting it a few more times.
                  </p>
                </div>
              </div>
            )}

            {!isIOS && !isAndroid && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                  <svg className="h-5 w-5 mr-2 text-primary-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z" />
                  </svg>
                  Desktop Installation
                </h3>
                <ol className="list-decimal pl-5 space-y-2 text-gray-700 dark:text-gray-300">
                  <li>Open Calcounta in <strong>Chrome</strong>, <strong>Edge</strong>, or another supported browser</li>
                  <li>Look for the install icon <span className="inline-block">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 inline" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </span> in the address bar</li>
                  <li>Click <strong>"Install"</strong> when prompted</li>
                  <li>The app will open in its own window</li>
                </ol>
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Tip:</strong> You can also install by clicking the menu (three dots) in Chrome or Edge and selecting "Install Calcounta..."
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Offline Usage */}
          <div className="p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              Using Calcounta Offline
            </h2>

            <div className="space-y-4 text-gray-700 dark:text-gray-300">
              <p>
                Once installed, Calcounta works even when you're offline or have a poor internet connection. Here's what you can do:
              </p>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">When Offline:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>View your previously loaded meals and nutrition data</li>
                  <li>Add new meals (they'll sync when you're back online)</li>
                  <li>Track your daily calorie intake</li>
                  <li>View your progress charts (based on cached data)</li>
                </ul>
              </div>

              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">Limitations:</h3>
                <ul className="list-disc pl-5 space-y-1">
                  <li>AI-powered food recognition requires an internet connection</li>
                  <li>You won't see new data from other devices until you're back online</li>
                  <li>Some advanced features may be limited</li>
                </ul>
              </div>

              <p className="text-sm italic">
                The app will automatically sync your data when your connection is restored.
              </p>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <Link
            to="/"
            className="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Return to Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PwaInfo;
