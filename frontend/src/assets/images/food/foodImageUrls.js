/**
 * Food image URLs for the Calcounta application
 * 
 * Note: These are placeholder URLs. Replace them with actual stock images
 * of healthy food, meal preparation, or nutrition-related imagery.
 * 
 * Recommended sources for free stock images:
 * - Unsplash (https://unsplash.com)
 * - Pexels (https://pexels.com)
 * - Pixabay (https://pixabay.com)
 */

// Breakfast images
export const breakfastImages = [
  "https://images.unsplash.com/photo-1533089860892-a9b9ac6cd6a4?q=80&w=2070&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1525351484163-7529414344d8?q=80&w=2080&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1494390248081-4e521a5940db?q=80&w=2006&auto=format&fit=crop"
];

// Lunch images
export const lunchImages = [
  "https://images.unsplash.com/photo-**********-ba9599a7e63c?q=80&w=2080&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?q=80&w=2070&auto=format&fit=crop",
  "https://images.unsplash.com/photo-**********-affa22d38842?q=80&w=1977&auto=format&fit=crop"
];

// Dinner images
export const dinnerImages = [
  "https://images.unsplash.com/photo-1559847844-5315695dadae?q=80&w=2058&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1574484284002-952d92456975?q=80&w=1974&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=2070&auto=format&fit=crop"
];

// Snack images
export const snackImages = [
  "https://images.unsplash.com/photo-1505253758473-96b7015fcd40?q=80&w=2000&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?q=80&w=2070&auto=format&fit=crop",
  "https://images.unsplash.com/photo-1599599810769-bcde5a160d32?q=80&w=1974&auto=format&fit=crop"
];

// Dashboard hero image
export const dashboardHeroImage = "https://images.unsplash.com/photo-1490818387583-1baba5e638af?q=80&w=1932&auto=format&fit=crop";

/**
 * Get a random image URL for a specific meal type
 * 
 * @param {string} mealType - Type of meal (breakfast, lunch, dinner, snack)
 * @returns {string} - URL of a random image for the specified meal type
 */
export const getRandomMealImage = (mealType) => {
  let images;
  
  switch (mealType) {
    case 'breakfast':
      images = breakfastImages;
      break;
    case 'lunch':
      images = lunchImages;
      break;
    case 'dinner':
      images = dinnerImages;
      break;
    default:
      images = snackImages;
      break;
  }
  
  const randomIndex = Math.floor(Math.random() * images.length);
  return images[randomIndex];
};
