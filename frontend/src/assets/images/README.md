# Image Assets for Calcounta

This directory contains image assets used throughout the Calcounta application.

## Stock Images

The application uses stock images for the login, registration, and onboarding screens. The image URLs are defined in `imageUrls.js`.

### Replacing Stock Images

To replace the placeholder images with your own:

1. Download high-quality stock images of food, healthy meals, or nutrition-related imagery
2. Place the images in this directory
3. Update the `imageUrls.js` file to point to your local images instead of the placeholder URLs

Example:
```js
// Before
export const loginImage = "https://images.unsplash.com/photo-1490818387583-1baba5e638af?q=80&w=1932&auto=format&fit=crop";

// After
export const loginImage = "/src/assets/images/login-food.jpg";
```

### Recommended Image Characteristics

- **Resolution**: At least 1920x1080 pixels for good quality on all devices
- **Aspect Ratio**: 3:2 or 4:3 works well for the splash screens
- **Content**: Food imagery that aligns with healthy eating and nutrition
- **Style**: Clean, well-lit images with good contrast against the overlay text

### Free Stock Image Resources

- [Unsplash](https://unsplash.com/s/photos/healthy-food)
- [Pexels](https://www.pexels.com/search/healthy%20food/)
- [Pixabay](https://pixabay.com/images/search/healthy%20food/)

Make sure to use images that are licensed for commercial use if this is a commercial project.
