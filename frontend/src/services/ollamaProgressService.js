/**
 * Ollama Progress Service
 * Handles real-time progress tracking for Ollama analysis requests
 */

class OllamaProgressService {
  constructor() {
    this.progressCallbacks = new Map();
    this.activeRequests = new Map();
  }

  /**
   * Start tracking progress for an analysis request
   * @param {string} requestId - Unique identifier for the request
   * @param {Function} onProgress - Callback function for progress updates
   * @param {Function} onComplete - Callback function for completion
   * @param {Function} onError - Callback function for errors
   */
  startTracking(requestId, { onProgress, onComplete, onError }) {
    this.progressCallbacks.set(requestId, {
      onProgress,
      onComplete,
      onError
    });

    // Initialize progress state
    this.activeRequests.set(requestId, {
      stage: 'initializing',
      progress: 0,
      message: 'Initializing analysis...',
      startTime: Date.now()
    });

    // Start progress simulation until we get real progress data
    this.simulateInitialProgress(requestId);
  }

  /**
   * Update progress for a specific request
   * @param {string} requestId - Request identifier
   * @param {Object} progressData - Progress information
   */
  updateProgress(requestId, progressData) {
    const callbacks = this.progressCallbacks.get(requestId);
    if (!callbacks) return;

    const requestState = this.activeRequests.get(requestId);
    if (!requestState) return;

    // Update request state
    const updatedState = {
      ...requestState,
      ...progressData,
      lastUpdate: Date.now()
    };
    this.activeRequests.set(requestId, updatedState);

    // Call progress callback
    if (callbacks.onProgress) {
      callbacks.onProgress(updatedState);
    }
  }

  /**
   * Mark request as complete
   * @param {string} requestId - Request identifier
   * @param {Object} result - Analysis result
   */
  completeRequest(requestId, result) {
    const callbacks = this.progressCallbacks.get(requestId);
    if (callbacks && callbacks.onComplete) {
      callbacks.onComplete(result);
    }
    this.cleanup(requestId);
  }

  /**
   * Mark request as failed
   * @param {string} requestId - Request identifier
   * @param {Error} error - Error object
   */
  failRequest(requestId, error) {
    const callbacks = this.progressCallbacks.get(requestId);
    if (callbacks && callbacks.onError) {
      callbacks.onError(error);
    }
    this.cleanup(requestId);
  }

  /**
   * Cancel a request
   * @param {string} requestId - Request identifier
   */
  cancelRequest(requestId) {
    this.cleanup(requestId);
  }

  /**
   * Clean up request tracking
   * @param {string} requestId - Request identifier
   */
  cleanup(requestId) {
    this.progressCallbacks.delete(requestId);
    this.activeRequests.delete(requestId);
  }

  /**
   * Simulate initial progress while waiting for real data
   * @param {string} requestId - Request identifier
   */
  simulateInitialProgress(requestId) {
    const progressStages = [
      { stage: 'uploading', progress: 10, message: 'Uploading image...' },
      { stage: 'processing', progress: 25, message: 'Processing image...' },
      { stage: 'analyzing', progress: 40, message: 'Starting analysis...' }
    ];

    let stageIndex = 0;
    const interval = setInterval(() => {
      if (!this.activeRequests.has(requestId) || stageIndex >= progressStages.length) {
        clearInterval(interval);
        return;
      }

      const stage = progressStages[stageIndex];
      this.updateProgress(requestId, stage);
      stageIndex++;
    }, 1000);

    // Store interval for cleanup
    const requestState = this.activeRequests.get(requestId);
    if (requestState) {
      requestState.progressInterval = interval;
    }
  }

  /**
   * Parse Ollama progress from streaming response
   * @param {string} chunk - Response chunk from Ollama
   * @returns {Object|null} - Parsed progress data or null
   */
  parseOllamaProgress(chunk) {
    try {
      // Ollama sends progress in JSON format like:
      // {"status":"pulling manifest"}
      // {"status":"downloading","completed":1024,"total":2048}
      // {"status":"model load progress","completed":0.75}
      
      const data = JSON.parse(chunk);
      
      if (data.status) {
        let progress = 0;
        let message = data.status;
        let stage = 'processing';

        // Handle different status types
        switch (data.status) {
          case 'pulling manifest':
            progress = 5;
            message = 'Loading model manifest...';
            stage = 'loading';
            break;
          case 'downloading':
            if (data.total && data.completed) {
              progress = Math.min(95, (data.completed / data.total) * 50 + 5);
              message = `Downloading model... ${Math.round(progress)}%`;
              stage = 'downloading';
            }
            break;
          case 'model load progress':
            if (data.completed !== undefined) {
              progress = Math.min(95, data.completed * 90 + 5);
              message = `Loading model... ${Math.round(progress)}%`;
              stage = 'loading';
            }
            break;
          case 'generating':
            progress = 95;
            message = 'Analyzing image...';
            stage = 'analyzing';
            break;
          default:
            progress = 50;
            message = data.status;
            stage = 'processing';
        }

        return { stage, progress, message };
      }
    } catch (e) {
      // Not JSON or invalid format, ignore
    }
    
    return null;
  }

  /**
   * Get current progress for a request
   * @param {string} requestId - Request identifier
   * @returns {Object|null} - Current progress state
   */
  getProgress(requestId) {
    return this.activeRequests.get(requestId) || null;
  }

  /**
   * Check if a request is being tracked
   * @param {string} requestId - Request identifier
   * @returns {boolean} - Whether request is being tracked
   */
  isTracking(requestId) {
    return this.activeRequests.has(requestId);
  }
}

// Create singleton instance
const ollamaProgressService = new OllamaProgressService();

export default ollamaProgressService;
