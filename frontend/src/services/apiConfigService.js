import axios from 'axios';
import { fetchConfig } from './configService';

// Available AI providers and their models
export const AI_PROVIDERS = {
  openai: {
    name: 'OpenAI',
    authType: 'apiKey',
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', supportsVision: true },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', supportsVision: true },
      { id: 'gpt-4.1', name: 'GPT-4.1', supportsVision: true },
      { id: 'gpt-4.1-mini', name: 'GPT-4.1 Mini', supportsVision: true },
      { id: 'gpt-4.1-nano', name: 'GPT-4.1 Nano', supportsVision: true },
      { id: 'gpt-4.5', name: 'GPT-4.5', supportsVision: true },
      { id: 'o1', name: 'o1', supportsVision: true },
      { id: 'o1-pro', name: 'o1 Pro', supportsVision: true },
      { id: 'o3', name: 'o3', supportsVision: true },
      { id: 'o4-mini', name: 'o4 Mini', supportsVision: true },
    ]
  },
  gemini: {
    name: 'Google Gemini',
    authType: 'apiKey',
    models: [
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', supportsVision: true },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', supportsVision: true },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', supportsVision: true },
    ]
  },
  ollama: {
    name: 'Ollama',
    authType: 'clientCredentials',
    dashboardUrl: '/ollama-dashboard', // Relative URL to the Ollama dashboard
    models: [
      // Models will be dynamically loaded from Ollama API
      { id: 'llama3.2-vision', name: 'Llama 3.2 Vision', supportsVision: true },
      { id: 'llava', name: 'LLaVA', supportsVision: true },
      { id: 'gemma3:4b-it-qat', name: 'Gemma 3 4B IT QAT', supportsVision: true },
    ]
  }
};

// Get available providers based on Ollama availability
export const getAvailableProviders = async () => {
  const config = await fetchConfig();
  const providers = { ...AI_PROVIDERS };

  // Remove Ollama if not available
  if (!config.ollamaAvailable) {
    delete providers.ollama;
  }

  return providers;
};

// Get models that support vision for a provider
export const getVisionModels = (provider) => {
  if (!AI_PROVIDERS[provider]) return [];
  return AI_PROVIDERS[provider].models.filter(model => model.supportsVision);
};

// Get authentication type for a provider
export const getAuthType = (provider) => {
  if (!AI_PROVIDERS[provider]) return 'apiKey';
  return AI_PROVIDERS[provider].authType || 'apiKey';
};

// Get dashboard URL for a provider (if applicable)
export const getDashboardUrl = async (provider) => {
  if (provider !== 'ollama') return null;

  const config = await fetchConfig();
  if (!config.ollamaAvailable) return null;

  // Construct the full dashboard URL
  const baseUrl = config.baseUrl || window.location.origin;
  const port = config.ollamaGatewayPort || '85';
  return `${baseUrl}:${port}`;
};

// Fetch available Ollama models dynamically
export const fetchOllamaModels = async (clientId, clientSecret) => {
  try {
    const config = await fetchConfig();
    if (!config.ollamaAvailable) {
      return { success: false, error: 'Ollama services are not available' };
    }

    const baseUrl = config.baseUrl || window.location.origin;
    const port = config.ollamaGatewayPort || '85';
    const ollamaUrl = `${baseUrl}:${port}`;

    const response = await fetch(`${ollamaUrl}/ai/api/tags`, {
      method: 'GET',
      headers: {
        'X-Client-ID': clientId,
        'X-Client-Secret': clientSecret,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.error || `HTTP ${response.status}: ${response.statusText}`
      };
    }

    const data = await response.json();
    const models = data.models || [];

    // Transform Ollama models to our format
    const transformedModels = models.map(model => ({
      id: model.name,
      name: model.name,
      supportsVision: true, // Assume all models support vision for now
      size: model.size,
      modified_at: model.modified_at
    }));

    return { success: true, models: transformedModels };
  } catch (error) {
    console.error('Error fetching Ollama models:', error);
    return { success: false, error: error.message };
  }
};

// API endpoints for backend communication
const API_ENDPOINTS = {
  STORE_API_KEY: '/api/users/api-key',
  GET_API_KEY: '/api/users/api-key',
  DELETE_API_KEY: '/api/users/api-key'
};

// Save API configuration to backend
export const saveApiConfig = async (provider, model, credentials) => {
  try {
    const payload = {
      provider,
      model,
      ...credentials // This will include either { apiKey } or { clientId, clientSecret }
    };

    const response = await axios.post(API_ENDPOINTS.STORE_API_KEY, payload);

    return response.data.success;
  } catch (error) {
    console.error('Error saving API configuration:', error);
    return false;
  }
};

// Load API configuration from backend
export const loadApiConfig = async () => {
  try {
    const response = await axios.get(API_ENDPOINTS.GET_API_KEY);
    return {
      provider: response.data.provider,
      model: response.data.model,
      apiKey: response.data.apiKey,
      clientId: response.data.clientId,
      clientSecret: response.data.clientSecret,
      isConfigured: response.data.isConfigured
    };
  } catch (error) {
    if (error.response?.status === 404) {
      // No API configuration found
      return null;
    }
    console.error('Error loading API configuration:', error);
    return null;
  }
};

// Clear API configuration
export const clearApiConfig = async () => {
  try {
    await axios.delete(API_ENDPOINTS.DELETE_API_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing API configuration:', error);
    return false;
  }
};

// Check if API is configured
export const isApiConfigured = async () => {
  try {
    const config = await loadApiConfig();
    return config !== null && config.isConfigured;
  } catch (error) {
    return false;
  }
};

// Validate OpenAI API key
export const validateOpenAIKey = async (apiKey) => {
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return { valid: true, error: null };
    } else if (response.status === 401) {
      return { valid: false, error: 'Invalid API key' };
    } else {
      return { valid: false, error: 'Unable to validate API key' };
    }
  } catch (error) {
    console.error('Error validating OpenAI API key:', error);
    return { valid: false, error: 'Network error during validation' };
  }
};

// Validate Gemini API key
export const validateGeminiKey = async (apiKey) => {
  try {
    // Use a simple request to validate the Gemini API key
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${apiKey}`);

    if (response.ok) {
      return { valid: true, error: null };
    } else if (response.status === 400 || response.status === 403) {
      return { valid: false, error: 'Invalid API key' };
    } else {
      return { valid: false, error: 'Unable to validate API key' };
    }
  } catch (error) {
    console.error('Error validating Gemini API key:', error);
    return { valid: false, error: 'Network error during validation' };
  }
};

// Validate Ollama client credentials
export const validateOllamaCredentials = async (clientId, clientSecret) => {
  try {
    const config = await fetchConfig();
    if (!config.ollamaAvailable) {
      return { valid: false, error: 'Ollama services are not available' };
    }

    const baseUrl = config.baseUrl || window.location.origin;
    const port = config.ollamaGatewayPort || '85';
    const ollamaUrl = `${baseUrl}:${port}`;

    // Test the credentials by making a request to the Ollama API
    const response = await fetch(`${ollamaUrl}/ai/api/tags`, {
      method: 'GET',
      headers: {
        'X-Client-ID': clientId,
        'X-Client-Secret': clientSecret,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      return { valid: true, error: null };
    } else if (response.status === 401 || response.status === 403) {
      return { valid: false, error: 'Invalid client credentials' };
    } else {
      return { valid: false, error: 'Unable to validate credentials' };
    }
  } catch (error) {
    console.error('Error validating Ollama credentials:', error);
    return { valid: false, error: 'Network error during validation' };
  }
};

// Validate API credentials based on provider
export const validateApiCredentials = async (provider, credentials) => {
  const authType = getAuthType(provider);

  if (authType === 'apiKey') {
    const { apiKey } = credentials;
    if (!apiKey || apiKey.trim() === '') {
      return { valid: false, error: 'API key is required' };
    }

    switch (provider) {
      case 'openai':
        return await validateOpenAIKey(apiKey);
      case 'gemini':
        return await validateGeminiKey(apiKey);
      default:
        return { valid: false, error: 'Unsupported provider' };
    }
  } else if (authType === 'clientCredentials') {
    const { clientId, clientSecret } = credentials;
    if (!clientId || clientId.trim() === '') {
      return { valid: false, error: 'Client ID is required' };
    }
    if (!clientSecret || clientSecret.trim() === '') {
      return { valid: false, error: 'Client Secret is required' };
    }

    switch (provider) {
      case 'ollama':
        return await validateOllamaCredentials(clientId, clientSecret);
      default:
        return { valid: false, error: 'Unsupported provider' };
    }
  }

  return { valid: false, error: 'Invalid authentication type' };
};

// Legacy function for backward compatibility
export const validateApiKey = async (provider, apiKey) => {
  return await validateApiCredentials(provider, { apiKey });
};

// Get current API configuration for making requests
export const getCurrentApiConfig = () => {
  const config = loadApiConfig();
  if (!config) {
    throw new Error('API not configured. Please configure your API settings.');
  }
  return config;
};
