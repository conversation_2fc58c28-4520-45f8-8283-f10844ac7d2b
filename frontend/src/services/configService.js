import axios from 'axios';

/**
 * Configuration service for frontend
 * Fetches configuration from backend
 */

let configCache = null;
let configPromise = null;

/**
 * Fetch configuration from backend
 * @returns {Promise<Object>} Configuration object
 */
export const fetchConfig = async () => {
  // Return cached config if available
  if (configCache) {
    return configCache;
  }

  // Return existing promise if already fetching
  if (configPromise) {
    return configPromise;
  }

  // Create new promise to fetch config
  configPromise = axios.get('/api/config')
    .then(response => {
      configCache = response.data;
      configPromise = null; // Clear promise after successful fetch
      return configCache;
    })
    .catch(error => {
      console.error('Error fetching configuration:', error);
      configPromise = null; // Clear promise on error

      // Return minimal fallback configuration on error
      // In production, this should rarely happen as the backend should always be available
      const fallbackConfig = {
        ollamaAvailable: false,
        ollamaGatewayPort: null,
        baseUrl: null,
        port: null
      };

      configCache = fallbackConfig;
      return fallbackConfig;
    });

  return configPromise;
};

/**
 * Get cached configuration (synchronous)
 * Returns null if config hasn't been fetched yet
 * @returns {Object|null} Configuration object or null
 */
export const getCachedConfig = () => {
  return configCache;
};

/**
 * Check if Ollama is available
 * @returns {Promise<boolean>} True if Ollama services are available
 */
export const isOllamaAvailable = async () => {
  const config = await fetchConfig();
  return config.ollamaAvailable;
};

/**
 * Get Ollama gateway port
 * @returns {Promise<string>} Ollama gateway port
 */
export const getOllamaGatewayPort = async () => {
  const config = await fetchConfig();
  return config.ollamaGatewayPort;
};

/**
 * Get base URL
 * @returns {Promise<string>} Base URL
 */
export const getBaseUrl = async () => {
  const config = await fetchConfig();
  return config.baseUrl;
};

/**
 * Get main application URL
 * @returns {Promise<string|null>} Main application URL or null if not available
 */
export const getMainAppUrl = async () => {
  const config = await fetchConfig();
  if (!config.baseUrl || !config.port) {
    return null;
  }
  return `${config.baseUrl}:${config.port}`;
};

/**
 * Get Ollama dashboard URL
 * @returns {Promise<string|null>} Ollama dashboard URL or null if not available
 */
export const getOllamaDashboardUrl = async () => {
  const config = await fetchConfig();
  if (!config.baseUrl || !config.ollamaGatewayPort) {
    return null;
  }
  return `${config.baseUrl}:${config.ollamaGatewayPort}/dashboard`;
};

/**
 * Clear configuration cache
 * Forces next fetch to get fresh data from backend
 */
export const clearConfigCache = () => {
  configCache = null;
  configPromise = null;
};
