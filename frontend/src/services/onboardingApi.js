import api from './api';

/**
 * Onboarding API service for managing user onboarding progress
 */
class OnboardingAPI {
  /**
   * Get user's onboarding status and progress
   * @returns {Promise<Object>} Onboarding status data
   */
  async getStatus() {
    try {
      const response = await api.get('/onboarding/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching onboarding status:', error);
      throw error;
    }
  }

  /**
   * Mark onboarding as started
   * @returns {Promise<Object>} Updated onboarding data
   */
  async start() {
    try {
      const response = await api.post('/onboarding/start');
      return response.data;
    } catch (error) {
      console.error('Error starting onboarding:', error);
      throw error;
    }
  }

  /**
   * Save onboarding progress
   * @param {number} step - Current step number (0-15)
   * @param {Object} data - Onboarding form data
   * @returns {Promise<Object>} Updated onboarding data
   */
  async saveProgress(step, data) {
    try {
      const response = await api.put('/onboarding/progress', {
        step,
        data
      });
      return response.data;
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
      throw error;
    }
  }

  /**
   * Mark onboarding as completed
   * @param {Object} finalData - Final onboarding data
   * @returns {Promise<Object>} Completion response
   */
  async complete(finalData) {
    try {
      const response = await api.post('/onboarding/complete', {
        data: finalData
      });
      return response.data;
    } catch (error) {
      console.error('Error completing onboarding:', error);
      throw error;
    }
  }

  /**
   * Reset onboarding progress (for development/testing)
   * @returns {Promise<Object>} Reset response
   */
  async reset() {
    try {
      const response = await api.delete('/onboarding/reset');
      return response.data;
    } catch (error) {
      console.error('Error resetting onboarding:', error);
      throw error;
    }
  }

  /**
   * Mark onboarding as completed via user profile endpoint
   * @returns {Promise<Object>} Completion response
   */
  async markCompleted() {
    try {
      const response = await api.post('/users/complete-onboarding');
      return response.data;
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
      throw error;
    }
  }
}

// Export singleton instance
const onboardingAPI = new OnboardingAPI();
export default onboardingAPI;
