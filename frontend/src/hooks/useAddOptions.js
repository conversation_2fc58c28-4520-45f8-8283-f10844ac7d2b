import { useState } from 'react';
import { useIsMobile } from './useIsMobile';

/**
 * Custom hook for managing add options modal and food scanner
 * Provides consistent add functionality across different pages
 */
export const useAddOptions = () => {
  const [showAddOptionsModal, setShowAddOptionsModal] = useState(false);
  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const isMobile = useIsMobile();

  const openAddOptions = () => {
    setShowAddOptionsModal(true);
  };

  const closeAddOptions = () => {
    setShowAddOptionsModal(false);
  };

  const handleAddMeal = () => {
    setShowAddOptionsModal(false);
    if (isMobile) {
      setIsScannerOpen(true);
    } else {
      // Navigate to add meal page on desktop
      window.location.href = '/add-meal';
    }
  };

  const closeFoodScanner = () => {
    setIsScannerOpen(false);
  };

  return {
    showAddOptionsModal,
    isScannerOpen,
    openAddOptions,
    closeAddOptions,
    handleAddMeal,
    closeFoodScanner,
  };
};
