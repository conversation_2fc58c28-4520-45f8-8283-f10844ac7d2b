import { useState, useEffect, useCallback } from 'react';

/**
 * Hook for managing sequential typewriter animations
 * Useful for coordinating multiple typewriter effects in sequence
 */
export const useTypewriterSequence = (totalSteps = 1) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [isSequenceComplete, setIsSequenceComplete] = useState(false);

  const markStepComplete = useCallback((stepIndex) => {
    setCompletedSteps(prev => {
      const newSet = new Set(prev);
      newSet.add(stepIndex);
      return newSet;
    });
  }, []);

  const startNextStep = useCallback(() => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep, totalSteps]);

  const resetSequence = useCallback(() => {
    setCurrentStep(0);
    setCompletedSteps(new Set());
    setIsSequenceComplete(false);
  }, []);

  // Check if sequence is complete
  useEffect(() => {
    if (completedSteps.size === totalSteps && !isSequenceComplete) {
      setIsSequenceComplete(true);
    }
  }, [completedSteps.size, totalSteps, isSequenceComplete]);

  const shouldStartStep = useCallback((stepIndex) => {
    return stepIndex <= currentStep;
  }, [currentStep]);

  const isStepComplete = useCallback((stepIndex) => {
    return completedSteps.has(stepIndex);
  }, [completedSteps]);

  return {
    currentStep,
    shouldStartStep,
    isStepComplete,
    markStepComplete,
    startNextStep,
    resetSequence,
    isSequenceComplete
  };
};

/**
 * Hook for managing typewriter animations with delays and coordination
 */
export const useTypewriterController = () => {
  const [activeAnimations, setActiveAnimations] = useState(new Map());
  const [globalDelay, setGlobalDelay] = useState(0);

  const registerAnimation = useCallback((id, config = {}) => {
    setActiveAnimations(prev => {
      const newMap = new Map(prev);
      newMap.set(id, {
        isActive: false,
        isComplete: false,
        startDelay: config.startDelay || 0,
        ...config
      });
      return newMap;
    });
  }, []);

  const startAnimation = useCallback((id, delay = 0) => {
    setTimeout(() => {
      setActiveAnimations(prev => {
        const newMap = new Map(prev);
        const animation = newMap.get(id);
        if (animation) {
          newMap.set(id, { ...animation, isActive: true });
        }
        return newMap;
      });
    }, delay + globalDelay);
  }, [globalDelay]);

  const markComplete = useCallback((id) => {
    setActiveAnimations(prev => {
      const newMap = new Map(prev);
      const animation = newMap.get(id);
      if (animation) {
        newMap.set(id, { ...animation, isComplete: true });
      }
      return newMap;
    });
  }, []);

  const startSequentialAnimations = useCallback((ids, delayBetween = 500) => {
    ids.forEach((id, index) => {
      startAnimation(id, index * delayBetween);
    });
  }, [startAnimation]);

  const resetAll = useCallback(() => {
    setActiveAnimations(new Map());
    setGlobalDelay(0);
  }, []);

  const getAnimationState = useCallback((id) => {
    return activeAnimations.get(id) || { isActive: false, isComplete: false };
  }, [activeAnimations]);

  const setGlobalStartDelay = useCallback((delay) => {
    setGlobalDelay(delay);
  }, []);

  return {
    registerAnimation,
    startAnimation,
    markComplete,
    startSequentialAnimations,
    resetAll,
    getAnimationState,
    setGlobalStartDelay,
    activeAnimations
  };
};

/**
 * Hook for step counter typewriter animation
 */
export const useStepCounterTypewriter = (currentStep, totalSteps, delay = 1000) => {
  const [showStepText, setShowStepText] = useState(false);
  const [stepText, setStepText] = useState('');

  useEffect(() => {
    const timer = setTimeout(() => {
      setStepText(`Step ${currentStep + 1} of ${totalSteps}`);
      setShowStepText(true);
    }, delay);

    return () => {
      clearTimeout(timer);
      setShowStepText(false);
    };
  }, [currentStep, totalSteps, delay]);

  return { showStepText, stepText };
};

/**
 * Hook for card content typewriter animations
 */
export const useCardTypewriter = (cards, startDelay = 500) => {
  const [visibleCards, setVisibleCards] = useState(0);
  const [activeCardContent, setActiveCardContent] = useState(new Map());

  useEffect(() => {
    if (cards.length === 0) return;

    const timer = setTimeout(() => {
      setVisibleCards(cards.length);
    }, startDelay);

    return () => clearTimeout(timer);
  }, [cards.length, startDelay]);

  const startCardContentAnimation = useCallback((cardIndex, contentType, delay = 0) => {
    setTimeout(() => {
      setActiveCardContent(prev => {
        const newMap = new Map(prev);
        const key = `${cardIndex}-${contentType}`;
        newMap.set(key, true);
        return newMap;
      });
    }, delay);
  }, []);

  const shouldShowCard = useCallback((cardIndex) => {
    return cardIndex < visibleCards;
  }, [visibleCards]);

  const shouldAnimateContent = useCallback((cardIndex, contentType) => {
    const key = `${cardIndex}-${contentType}`;
    return activeCardContent.get(key) || false;
  }, [activeCardContent]);

  return {
    shouldShowCard,
    shouldAnimateContent,
    startCardContentAnimation,
    visibleCards
  };
};
