import { useState, useEffect, useRef } from 'react';

/**
 * Custom hook for managing onboarding animations
 * Provides consistent animation timing and state management
 */
export const useOnboardingAnimations = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  const [animationPhase, setAnimationPhase] = useState('entering');
  const timeoutRef = useRef(null);

  // Initialize animations when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
      setAnimationPhase('visible');
    }, 50); // Small delay to ensure smooth entrance

    return () => clearTimeout(timer);
  }, []);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  /**
   * Trigger exit animation before navigation
   * @param {Function} callback - Function to call after exit animation
   * @param {number} delay - Delay before calling callback (default: 300ms)
   */
  const triggerExit = (callback, delay = 300) => {
    setIsExiting(true);
    setAnimationPhase('exiting');
    
    timeoutRef.current = setTimeout(() => {
      if (callback) callback();
    }, delay);
  };

  /**
   * Get animation classes for page transitions
   * @param {string} direction - 'forward' or 'backward'
   * @returns {string} - CSS classes for animation
   */
  const getPageTransitionClasses = (direction = 'forward') => {
    if (isExiting) {
      return direction === 'forward' 
        ? 'animate-slideOutLeft' 
        : 'animate-slideOutRight';
    }
    
    if (isVisible) {
      return direction === 'forward' 
        ? 'animate-slideInRight' 
        : 'animate-slideInLeft';
    }
    
    return 'opacity-0';
  };

  /**
   * Get staggered animation classes for components
   * @param {number} index - Component index for stagger delay
   * @param {number} baseDelay - Base delay in ms (default: 100)
   * @returns {string} - CSS classes for staggered animation
   */
  const getStaggeredClasses = (index, baseDelay = 100) => {
    if (!isVisible) return 'opacity-0';
    
    const delayClass = index === 0 ? '' : `-delay-${Math.min(index * baseDelay, 400)}`;
    return `animate-staggerFadeIn${delayClass}`;
  };

  /**
   * Get text animation classes
   * @param {string} type - 'title', 'subtitle', or 'typewriter'
   * @param {number} delay - Additional delay in ms
   * @returns {string} - CSS classes for text animation
   */
  const getTextClasses = (type = 'title', delay = 0) => {
    if (!isVisible) return 'opacity-0';
    
    switch (type) {
      case 'title':
        return delay > 0 ? `animate-fadeInUp-delay-${delay}` : 'animate-fadeInUp';
      case 'subtitle':
        return delay > 0 ? `animate-fadeInUp-delay-${delay}` : 'animate-fadeInUp-delay-100';
      case 'typewriter':
        return 'animate-typewriter';
      default:
        return 'animate-fadeInUp';
    }
  };

  /**
   * Get button animation classes
   * @param {boolean} isPressed - Whether button is being pressed
   * @param {boolean} isHovered - Whether button is being hovered
   * @returns {string} - CSS classes for button animation
   */
  const getButtonClasses = (isPressed = false, isHovered = false) => {
    let classes = 'transition-all duration-200 ease-out';
    
    if (isPressed) {
      classes += ' animate-buttonPress';
    } else if (isHovered) {
      classes += ' animate-buttonHover';
    }
    
    return classes;
  };

  /**
   * Get progress bar animation classes
   * @param {number} progress - Progress percentage (0-100)
   * @returns {object} - Style object with CSS custom properties
   */
  const getProgressStyles = (progress) => ({
    '--progress-width': `${progress}%`,
    animationDelay: isVisible ? '0.2s' : '0s'
  });

  return {
    isVisible,
    isExiting,
    animationPhase,
    triggerExit,
    getPageTransitionClasses,
    getStaggeredClasses,
    getTextClasses,
    getButtonClasses,
    getProgressStyles
  };
};

/**
 * Hook for managing form field animations
 * @param {number} fieldCount - Total number of fields
 * @returns {object} - Animation utilities for form fields
 */
export const useFormAnimations = (fieldCount = 1) => {
  const [visibleFields, setVisibleFields] = useState(0);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisibleFields(fieldCount);
    }, 200);

    return () => clearTimeout(timer);
  }, [fieldCount]);

  const getFieldClasses = (index) => {
    if (index >= visibleFields) return 'opacity-0 translate-y-4';
    return `animate-staggerFadeIn-delay-${Math.min(index * 100, 400)}`;
  };

  return { getFieldClasses, visibleFields };
};

/**
 * Hook for managing validation animations
 * @returns {object} - Animation utilities for validation states
 */
export const useValidationAnimations = () => {
  const [validationState, setValidationState] = useState(null); // 'success', 'error', null

  const triggerValidation = (isValid) => {
    setValidationState(isValid ? 'success' : 'error');
    
    // Reset after animation
    setTimeout(() => {
      setValidationState(null);
    }, 600);
  };

  const getValidationClasses = () => {
    switch (validationState) {
      case 'success':
        return 'animate-bounceIn border-green-500';
      case 'error':
        return 'animate-pulse border-red-500';
      default:
        return '';
    }
  };

  return { triggerValidation, getValidationClasses, validationState };
};
