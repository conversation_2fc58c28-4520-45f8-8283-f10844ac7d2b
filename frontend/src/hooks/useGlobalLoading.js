import { useLoading } from '../context/LoadingContext';

/**
 * Custom hook for managing global loading states
 * Provides convenient methods for common loading scenarios
 */
export const useGlobalLoading = () => {
  const { setLoading, getLoadingState, clearAllLoading } = useLoading();

  // Common loading scenarios with predefined messages
  const loadingScenarios = {
    // Authentication related
    authenticating: () => setLoading('auth', true, 'Authenticating...'),
    signingIn: () => setLoading('auth', true, 'Signing in...'),
    signingOut: () => setLoading('auth', true, 'Signing out...'),
    
    // Data fetching
    fetchingData: () => setLoading('data', true, 'Loading data...'),
    savingData: () => setLoading('data', true, 'Saving...'),
    deletingData: () => setLoading('data', true, 'Deleting...'),
    
    // Food related
    analyzingFood: () => setLoading('food', true, 'Analyzing food...'),
    scanningFood: () => setLoading('food', true, 'Scanning food...'),
    addingMeal: () => setLoading('meal', true, 'Adding meal...'),
    
    // Profile related
    updatingProfile: () => setLoading('profile', true, 'Updating profile...'),
    uploadingImage: () => setLoading('upload', true, 'Uploading image...'),
    
    // General
    processing: () => setLoading('general', true, 'Processing...'),
    loading: () => setLoading('general', true, 'Loading...'),
  };

  // Stop loading for specific scenarios
  const stopLoading = {
    auth: () => setLoading('auth', false),
    data: () => setLoading('data', false),
    food: () => setLoading('food', false),
    meal: () => setLoading('meal', false),
    profile: () => setLoading('profile', false),
    upload: () => setLoading('upload', false),
    general: () => setLoading('general', false),
  };

  // Custom loading with message
  const startLoading = (key, message = 'Loading...') => {
    setLoading(key, true, message);
  };

  const stopLoadingKey = (key) => {
    setLoading(key, false);
  };

  return {
    // Predefined scenarios
    ...loadingScenarios,
    
    // Stop loading methods
    stop: stopLoading,
    
    // Custom methods
    startLoading,
    stopLoading: stopLoadingKey,
    clearAll: clearAllLoading,
    
    // State checkers
    isLoading: getLoadingState,
  };
};
