{"name": "calcounta-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-pwa-assets": "node scripts/generate-pwa-assets.js"}, "dependencies": {"@tensorflow/tfjs": "^4.17.0", "@tensorflow-models/mobilenet": "^2.1.1", "axios": "^1.4.0", "chart.js": "^4.3.0", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-icons": "^4.10.1", "react-router-dom": "^6.14.1", "tailwindcss": "^3.3.2"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.14", "eslint": "^8.44.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "^8.4.25", "sharp": "^0.32.1", "vite": "^4.4.0", "vite-plugin-pwa": "^0.16.4", "workbox-window": "^7.0.0"}}