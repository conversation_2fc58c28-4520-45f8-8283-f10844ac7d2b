/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        secondary: {
          50: '#f5f3ff',
          100: '#ede9fe',
          200: '#ddd6fe',
          300: '#c4b5fd',
          400: '#a78bfa',
          500: '#8b5cf6',
          600: '#7c3aed',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95',
          950: '#2e1065',
        },
        // macOS/iOS Design System Colors
        'ios-blue': '#007AFF',
        'ios-blue-light': '#5AC8FA',
        'ios-blue-dark': '#0051D5',
        'ios-gray': '#8E8E93',
        'ios-gray-light': '#C7C7CC',
        'ios-gray-2': '#AEAEB2',
        'ios-gray-3': '#C7C7CC',
        'ios-gray-4': '#D1D1D6',
        'ios-gray-5': '#E5E5EA',
        'ios-gray-6': '#F2F2F7',
        'ios-separator': 'rgba(60, 60, 67, 0.36)',
        'ios-separator-opaque': '#C6C6C8',
        'ios-label': '#000000',
        'ios-label-secondary': 'rgba(60, 60, 67, 0.6)',
        'ios-label-tertiary': 'rgba(60, 60, 67, 0.3)',
        'ios-label-quaternary': 'rgba(60, 60, 67, 0.18)',
        'ios-system-background': '#FFFFFF',
        'ios-secondary-background': '#F2F2F7',
        'ios-tertiary-background': '#FFFFFF',
        'ios-grouped-background': '#F2F2F7',
        'ios-secondary-grouped-background': '#FFFFFF',
        'ios-tertiary-grouped-background': '#F2F2F7',
        // Loveable design system colors
        'protein-blue': '#3b82f6',
        'carbs-green': '#10b981',
        'fat-yellow': '#fbbf24',
        'highlight-orange': '#fb923c',
        'highlight-purple': '#a855f7',
        'highlight-green': '#22c55e',
        'underweight': '#3b82f6',
        'normal': '#22c55e',
        'overweight': '#fbbf24',
        'obese': '#ef4444',
      },
      fontFamily: {
        sans: [
          '-apple-system',
          'BlinkMacSystemFont',
          'SF Pro Display',
          'SF Pro Text',
          'Helvetica Neue',
          'Helvetica',
          'Arial',
          'sans-serif'
        ],
        'sf-pro': [
          'SF Pro Display',
          'SF Pro Text',
          '-apple-system',
          'BlinkMacSystemFont',
          'sans-serif'
        ],
      },
      animation: {
        // Legacy animations
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'fadeIn': 'fadeIn 0.3s ease-in-out',
        'pulse': 'pulse 2s infinite ease-in-out',

        // Onboarding animations
        'slide-in-right': 'slideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'slide-in-left': 'slideInLeft 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'slide-out-left': 'slideOutLeft 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19)',
        'slide-out-right': 'slideOutRight 0.3s cubic-bezier(0.55, 0.055, 0.675, 0.19)',
        'fade-in-up': 'fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'fade-in-down': 'fadeInDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'stagger-fade-in': 'staggerFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'bounce-in': 'bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'slide-up': 'slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'button-press': 'buttonPress 0.15s ease-in-out',
        'button-hover': 'buttonHover 0.2s ease-out',
        'progress-fill': 'progressFill 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'typewriter': 'typewriter 2s steps(40, end), blinkCursor 0.75s step-end infinite',
      },
      keyframes: {
        // Legacy keyframes
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        pulse: {
          '0%': { transform: 'scale(1)', opacity: '1' },
          '50%': { transform: 'scale(1.05)', opacity: '0.8' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },

        // Onboarding keyframes
        slideInRight: {
          '0%': { opacity: '0', transform: 'translateX(30px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        slideInLeft: {
          '0%': { opacity: '0', transform: 'translateX(-30px)' },
          '100%': { opacity: '1', transform: 'translateX(0)' },
        },
        slideOutLeft: {
          '0%': { opacity: '1', transform: 'translateX(0)' },
          '100%': { opacity: '0', transform: 'translateX(-30px)' },
        },
        slideOutRight: {
          '0%': { opacity: '1', transform: 'translateX(0)' },
          '100%': { opacity: '0', transform: 'translateX(30px)' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        fadeInDown: {
          '0%': { opacity: '0', transform: 'translateY(-20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        staggerFadeIn: {
          '0%': { opacity: '0', transform: 'translateY(15px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        bounceIn: {
          '0%': { opacity: '0', transform: 'scale(0.3)' },
          '50%': { opacity: '1', transform: 'scale(1.05)' },
          '70%': { transform: 'scale(0.9)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        buttonPress: {
          '0%': { transform: 'scale(1)' },
          '50%': { transform: 'scale(0.98)' },
          '100%': { transform: 'scale(1)' },
        },
        buttonHover: {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(-2px)' },
        },
        progressFill: {
          '0%': { width: '0%' },
          '100%': { width: 'var(--progress-width)' },
        },
        typewriter: {
          '0%': { width: '0' },
          '100%': { width: '100%' },
        },
        blinkCursor: {
          '0%, 100%': { borderColor: 'transparent' },
          '50%': { borderColor: 'currentColor' },
        },
      },
      borderRadius: {
        lg: '0.5rem',  // 8px
        md: '0.375rem', // 6px
        sm: '0.25rem',  // 4px
      },
    },
  },
  plugins: [],
}
