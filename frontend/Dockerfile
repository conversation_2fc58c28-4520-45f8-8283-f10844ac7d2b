# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Create public/icons directory for PWA assets
RUN mkdir -p public/icons

# Install Sharp for PWA asset generation
RUN npm install --no-save sharp

# Generate PWA icons
RUN if [ -f "scripts/generate-pwa-assets.js" ]; then \
      node scripts/generate-pwa-assets.js || echo "PWA asset generation skipped"; \
    else \
      echo "PWA asset generation script not found"; \
    fi

# Ensure offline.html exists
RUN if [ ! -f "public/offline.html" ]; then \
      echo "Creating basic offline.html"; \
      echo '<!DOCTYPE html>' > public/offline.html && \
      echo '<html lang="en">' >> public/offline.html && \
      echo '<head>' >> public/offline.html && \
      echo '  <meta charset="UTF-8">' >> public/offline.html && \
      echo '  <meta name="viewport" content="width=device-width, initial-scale=1.0">' >> public/offline.html && \
      echo '  <title>Offline - Calcounta</title>' >> public/offline.html && \
      echo '  <style>' >> public/offline.html && \
      echo '    body { font-family: sans-serif; text-align: center; padding: 20px; }' >> public/offline.html && \
      echo '    .container { max-width: 500px; margin: 0 auto; }' >> public/offline.html && \
      echo '  </style>' >> public/offline.html && \
      echo '</head>' >> public/offline.html && \
      echo '<body>' >> public/offline.html && \
      echo '  <div class="container">' >> public/offline.html && \
      echo '    <h1>You are Offline</h1>' >> public/offline.html && \
      echo '    <p>Please check your internet connection and try again.</p>' >> public/offline.html && \
      echo '    <button onclick="window.location.reload()">Try Again</button>' >> public/offline.html && \
      echo '  </div>' >> public/offline.html && \
      echo '</body>' >> public/offline.html && \
      echo '</html>' >> public/offline.html; \
    fi

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Install dependencies for PWA asset generation
RUN apk add --no-cache nodejs npm imagemagick

# Copy the build output
COPY --from=build /app/dist /usr/share/nginx/html

# Copy source logos for PWA asset generation
COPY --from=build /app/src/assets/logos /usr/share/nginx/html/assets/logos

# Ensure PWA directories exist
RUN mkdir -p /usr/share/nginx/html/icons

# Copy nginx configuration template
COPY nginx.conf.template /etc/nginx/conf.d/default.conf.template

# Copy entrypoint script
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# Set proper permissions for nginx
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:80/ || exit 1

# Expose port 80
EXPOSE 80

# Set entrypoint and command
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
