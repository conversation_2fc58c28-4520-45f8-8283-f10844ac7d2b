# PWA Icons for Calcounta

This directory contains the icons used for the Progressive Web App (PWA) functionality of Calcounta.

## Icon Sizes

The following icon sizes are required for a complete PWA experience:

- `icon-72x72.png` - Basic Android icon
- `icon-96x96.png` - Basic Android icon
- `icon-128x128.png` - Basic icon
- `icon-144x144.png` - Android/Windows icon
- `icon-152x152.png` - iOS icon
- `icon-192x192.png` - Android home screen icon
- `icon-384x384.png` - Android icon
- `icon-512x512.png` - PWA icon
- `maskable-icon-512x512.png` - Android adaptive icon with safe zone
- `apple-touch-icon.png` - Default Apple touch icon
- `apple-touch-icon-152x152.png` - iPad touch icon
- `apple-touch-icon-167x167.png` - iPad Pro touch icon
- `apple-touch-icon-180x180.png` - iPhone touch icon

## Splash Screens

The following splash screen sizes are used for iOS devices:

- `apple-splash-2048-2732.png` - 12.9" iPad Pro
- `apple-splash-1668-2388.png` - 11" iPad Pro
- `apple-splash-1536-2048.png` - 9.7" iPad
- `apple-splash-1125-2436.png` - iPhone X/XS
- `apple-splash-1242-2688.png` - iPhone XS Max
- `apple-splash-828-1792.png` - iPhone XR
- `apple-splash-750-1334.png` - iPhone 8/7/6s/6
- `apple-splash-640-1136.png` - iPhone SE

## Generation

These icons are automatically generated during the Docker build process using the `scripts/generate-pwa-assets.js` script, which takes the source logo from `src/assets/logos/logo.svg` and creates all the necessary sizes and formats.

If you need to manually generate these icons, you can run:

```bash
npm run generate-pwa-assets
```

## Customization

To customize the icons, replace the source logo file at `src/assets/logos/logo.svg` with your own SVG logo and run the generation script again.

For the splash screens, you can modify the background color in the `scripts/generate-pwa-assets.js` file by changing the `SPLASH_BG_COLOR` constant.
