<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - Calcounta</title>
  <link rel="manifest" href="/manifest.json">
  <meta name="theme-color" content="#0ea5e9">
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f3f4f6;
      color: #1f2937;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
    }
    
    .container {
      max-width: 500px;
      padding: 30px;
      background-color: white;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .logo {
      width: 80px;
      height: 80px;
      margin-bottom: 20px;
    }
    
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #0ea5e9;
    }
    
    p {
      font-size: 16px;
      line-height: 1.5;
      margin-bottom: 24px;
    }
    
    .button {
      display: inline-block;
      background-color: #0ea5e9;
      color: white;
      padding: 12px 24px;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    
    .button:hover {
      background-color: #0284c7;
    }
    
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #1f2937;
        color: #f9fafb;
      }
      
      .container {
        background-color: #111827;
      }
      
      h1 {
        color: #38bdf8;
      }
      
      .button {
        background-color: #38bdf8;
      }
      
      .button:hover {
        background-color: #0ea5e9;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="/icons/icon-192x192.png" alt="Calcounta Logo" class="logo">
    <h1>You're Offline</h1>
    <p>It looks like you're not connected to the internet. Some features of Calcounta may not be available while offline.</p>
    <p>You can still view previously loaded content and add meals, which will sync when you're back online.</p>
    <a href="/" class="button">Try Again</a>
  </div>
  
  <script>
    // Check if we're back online and redirect to the homepage
    window.addEventListener('online', () => {
      window.location.href = '/';
    });
  </script>
</body>
</html>
