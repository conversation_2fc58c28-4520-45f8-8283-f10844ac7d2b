#!/bin/sh
set -e

# Log to stdout for debugging
exec > >(tee -a /tmp/entrypoint.log) 2>&1
echo "Starting entrypoint script at $(date)"

# Replace environment variables in the template and copy to nginx conf
envsubst '${HOST}' < /etc/nginx/conf.d/default.conf.template > /etc/nginx/conf.d/default.conf

# Check if PWA assets exist
if [ ! -f /usr/share/nginx/html/manifest.json ]; then
  echo "Warning: manifest.json not found. PWA functionality may be limited."
fi

# Ensure icons directory exists
mkdir -p /usr/share/nginx/html/icons

# Check for icons directory and create if needed
if [ ! -d /usr/share/nginx/html/icons ] || [ -z "$(ls -A /usr/share/nginx/html/icons 2>/dev/null)" ] || [ ! -f /usr/share/nginx/html/icons/icon-192x192.png ]; then
  echo "Icons directory is empty or not found. Checking for source logos..."

  # Create icons directory if it doesn't exist
  mkdir -p /usr/share/nginx/html/icons

  # Check if we have the source logos in the container
  if [ -f /usr/share/nginx/html/assets/logos/logo.svg ]; then
    echo "Found logo.svg in assets directory. Generating PWA icons..."

    # Create a temporary directory for the script
    mkdir -p /tmp/pwa-generator

    # Create a simple script to generate icons
    cat > /tmp/pwa-generator/generate.js << 'EOF'
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Source logo paths
const SOURCE_LOGO = '/usr/share/nginx/html/assets/logos/logo.svg';
const DARK_MODE_LOGO = '/usr/share/nginx/html/assets/logos/logo-for-darkmode.svg';
const OUTPUT_DIR = '/usr/share/nginx/html/icons';

// Icon sizes to generate
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];

// Apple touch icon sizes
const APPLE_ICON_SIZES = [57, 60, 72, 76, 114, 120, 144, 152, 167, 180];

async function generateIcons() {
  console.log('Generating PWA icons...');

  for (const size of ICON_SIZES) {
    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .png()
      .toFile(path.join(OUTPUT_DIR, `icon-${size}x${size}.png`));

    console.log(`Generated: icon-${size}x${size}.png`);
  }

  // Generate maskable icon
  const maskableSize = 512;
  const padding = Math.floor(maskableSize * 0.1); // Ensure integer value
  const innerSize = Math.floor(maskableSize - (padding * 2)); // Ensure integer value

  await sharp(SOURCE_LOGO)
    .resize(innerSize, innerSize)
    .extend({
      top: padding,
      bottom: padding,
      left: padding,
      right: padding,
      background: { r: 14, g: 165, b: 233, alpha: 1 }
    })
    .png()
    .toFile(path.join(OUTPUT_DIR, `maskable-icon-${maskableSize}x${maskableSize}.png`));

  // Generate apple touch icons
  for (const size of APPLE_ICON_SIZES) {
    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .png()
      .toFile(path.join(OUTPUT_DIR, `apple-touch-icon-${size}x${size}.png`));

    console.log(`Generated: apple-touch-icon-${size}x${size}.png`);
  }

  // Generate default apple touch icon
  await sharp(SOURCE_LOGO)
    .resize(180, 180)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'apple-touch-icon.png'));

  // Generate precomposed apple touch icon for older iOS versions
  await sharp(SOURCE_LOGO)
    .resize(180, 180)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'apple-touch-icon-precomposed.png'));

  // Generate favicon
  await sharp(SOURCE_LOGO)
    .resize(32, 32)
    .png()
    .toFile('/usr/share/nginx/html/favicon.png');
}

// Run the generator
generateIcons().catch(err => {
  console.error('Error generating icons:', err);
  process.exit(1);
});
EOF

    # Install sharp if not already installed
    if ! command -v npm >/dev/null 2>&1; then
      echo "npm not found, cannot generate icons automatically."
    else
      cd /tmp/pwa-generator
      npm install --no-save sharp
      node generate.js
      cd -
    fi
  else
    echo "Source logos not found. Using default icons."
    # Copy a placeholder icon
    echo "Creating placeholder icons..."
    for size in 72 96 128 144 152 192 384 512; do
      echo "Creating placeholder icon-${size}x${size}.png"
      # Create a simple colored square as placeholder
      convert -size ${size}x${size} xc:#0ea5e9 /usr/share/nginx/html/icons/icon-${size}x${size}.png 2>/dev/null || echo "ImageMagick not available, skipping placeholder creation"
    done
  fi
fi

# Check for service worker
if [ ! -f /usr/share/nginx/html/sw.js ] && [ ! -f /usr/share/nginx/html/service-worker.js ] && [ ! -f /usr/share/nginx/html/workbox-*.js ]; then
  echo "Warning: No service worker found. PWA functionality will be limited."
fi

# Ensure offline page exists
if [ ! -f /usr/share/nginx/html/offline.html ]; then
  echo "Warning: offline.html not found. Creating basic offline page."
  cat > /usr/share/nginx/html/offline.html << EOF
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - Calcounta</title>
  <style>
    body { font-family: sans-serif; text-align: center; padding: 20px; }
    .container { max-width: 500px; margin: 0 auto; }
  </style>
</head>
<body>
  <div class="container">
    <h1>You're Offline</h1>
    <p>Please check your internet connection and try again.</p>
    <button onclick="window.location.reload()">Try Again</button>
  </div>
</body>
</html>
EOF
fi

# Manual icon generation for PWA (fallback method)
if [ ! -f /usr/share/nginx/html/icons/icon-192x192.png ] && [ -f /usr/share/nginx/html/assets/logos/logo.svg ]; then
  echo "Attempting manual icon generation using ImageMagick..."

  # Create basic icons using ImageMagick
  for size in 72 96 128 144 152 192 384 512; do
    echo "Creating icon-${size}x${size}.png"
    convert -background none -resize ${size}x${size} /usr/share/nginx/html/assets/logos/logo.svg /usr/share/nginx/html/icons/icon-${size}x${size}.png || echo "Failed to create icon-${size}x${size}.png"
  done

  # Create apple touch icons
  for size in 57 60 72 76 114 120 144 152 167 180; do
    echo "Creating apple-touch-icon-${size}x${size}.png"
    convert -background none -resize ${size}x${size} /usr/share/nginx/html/assets/logos/logo.svg /usr/share/nginx/html/icons/apple-touch-icon-${size}x${size}.png || echo "Failed to create apple-touch-icon-${size}x${size}.png"
  done

  # Create default apple touch icon
  convert -background none -resize 180x180 /usr/share/nginx/html/assets/logos/logo.svg /usr/share/nginx/html/icons/apple-touch-icon.png || echo "Failed to create apple-touch-icon.png"

  # Create precomposed apple touch icon for older iOS versions
  convert -background none -resize 180x180 /usr/share/nginx/html/assets/logos/logo.svg /usr/share/nginx/html/icons/apple-touch-icon-precomposed.png || echo "Failed to create apple-touch-icon-precomposed.png"

  # Create maskable icon (using integer dimensions)
  convert -background '#0ea5e9' -resize 460x460 -gravity center -extent 512x512 /usr/share/nginx/html/assets/logos/logo.svg /usr/share/nginx/html/icons/maskable-icon-512x512.png || echo "Failed to create maskable-icon-512x512.png"

  echo "Manual icon generation completed"
fi

echo "Entrypoint script completed at $(date)"

# Execute the CMD from the Dockerfile
exec "$@"
