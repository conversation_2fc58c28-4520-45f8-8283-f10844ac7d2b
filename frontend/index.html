<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/src/assets/logos/logo.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Calcounta - AI-Powered Calorie Tracking</title>
    <meta name="description" content="Track your calories and nutrition with AI-powered food recognition" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#000000" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Calcounta" />
    <link rel="manifest" href="/manifest.json" />

    <!-- Additional meta tags for iOS notch area -->
    <meta name="viewport-fit" content="cover" />

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
    <link rel="apple-touch-icon-precomposed" href="/icons/apple-touch-icon-precomposed.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/icons/apple-touch-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/icons/apple-touch-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/apple-touch-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/icons/apple-touch-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/apple-touch-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/apple-touch-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/apple-touch-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/apple-touch-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/apple-touch-icon-167x167.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon-180x180.png" />

    <!-- Splash Screen Images -->
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-2048-2732.png" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-1668-2388.png" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-1536-2048.png" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-1125-2436.png" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-1242-2688.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-828-1792.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-750-1334.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/icons/apple-splash-640-1136.png" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Preconnect to TensorFlow.js CDN -->
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://storage.googleapis.com">

    <!-- Load TensorFlow.js and MobileNet directly with defer attribute to ensure they load properly -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@4.17.0/dist/tf.min.js" defer></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@2.1.1/dist/mobilenet.min.js" defer></script>

    <!-- Additional styles for iOS notch area -->
    <style>
      @supports (-webkit-touch-callout: none) {
        /* iOS specific styles */
        html {
          --sat: env(safe-area-inset-top);
          background-color: transparent;
        }

        body {
          padding-top: 0;
          margin-top: 0;
          position: relative;
          min-height: 100vh;
          min-height: -webkit-fill-available;
        }

        /* Make status bar transparent to allow our custom styling */
        :root {
          --status-bar-height: env(safe-area-inset-top, 0);
        }

        /* Scanner-specific styles */
        .scanner-page body::before {
          display: none !important;
        }

        .scanner-page body::after {
          content: '';
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          width: 100%;
          height: env(safe-area-inset-top, 0);
          background-color: rgba(0, 0, 0, 0.25) !important;
          z-index: 99999 !important;
          pointer-events: none;
        }
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- Preload TensorFlow.js models in the background -->
    <script>
      // This script preloads the TensorFlow.js models when the app starts
      window.addEventListener('load', function() {
        // Function to check if libraries are loaded and retry if not
        function checkAndLoadModel(retryCount = 0, maxRetries = 5) {
          // Check if TensorFlow.js and MobileNet are available
          if (typeof tf === 'undefined' || typeof mobilenet === 'undefined') {
            console.warn(`TensorFlow.js or MobileNet not loaded yet. Retry attempt: ${retryCount + 1}/${maxRetries}`);

            if (retryCount < maxRetries) {
              // Retry after a delay
              setTimeout(() => checkAndLoadModel(retryCount + 1, maxRetries), 1000);
            } else {
              console.error('Failed to load TensorFlow.js or MobileNet after multiple attempts');
              // Set a flag to indicate we should allow capture even without food detection
              window.foodDetectionUnavailable = true;
            }
            return;
          }

          // Preload the model
          async function preloadModel() {
            try {
              console.log('Preloading TensorFlow.js MobileNet model...');
              await tf.ready();
              const model = await mobilenet.load({
                version: 2,
                alpha: 0.5
              });
              console.log('TensorFlow.js MobileNet model preloaded successfully');

              // Store the model for later use
              window.tfPreloadedModel = model;

              // Set a flag to indicate food detection is available
              window.foodDetectionAvailable = true;
            } catch (error) {
              console.error('Error preloading TensorFlow.js model:', error);
              window.foodDetectionUnavailable = true;
            }
          }

          // Start preloading
          preloadModel();
        }

        // Start the check and load process
        checkAndLoadModel();
      });
    </script>
  </body>
</html>
