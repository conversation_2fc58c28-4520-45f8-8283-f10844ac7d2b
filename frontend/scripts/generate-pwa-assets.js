/**
 * <PERSON><PERSON><PERSON> to generate PWA assets from the source logo
 *
 * This script uses <PERSON> to generate various icon sizes and splash screens
 * required for a Progressive Web App.
 *
 * Usage:
 * node scripts/generate-pwa-assets.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Source logo paths (SVG preferred for best quality)
const SOURCE_LOGO = path.join(__dirname, '../src/assets/logos/logo.svg');
const DARK_MODE_LOGO = path.join(__dirname, '../src/assets/logos/logo-for-darkmode.svg');

// Output directory for generated icons
const OUTPUT_DIR = path.join(__dirname, '../public/icons');

// Create output directory if it doesn't exist
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

// Icon sizes to generate
const ICON_SIZES = [72, 96, 128, 144, 152, 192, 384, 512];

// Apple touch icon sizes
const APPLE_ICON_SIZES = [152, 167, 180];

// Additional Apple touch icon sizes that might be requested by iOS devices
const ADDITIONAL_APPLE_ICON_SIZES = [57, 60, 72, 76, 114, 120, 144];

// Apple splash screen configurations
const APPLE_SPLASH_SCREENS = [
  { width: 2048, height: 2732 }, // 12.9" iPad Pro
  { width: 1668, height: 2388 }, // 11" iPad Pro
  { width: 1536, height: 2048 }, // 9.7" iPad
  { width: 1125, height: 2436 }, // iPhone X/XS
  { width: 1242, height: 2688 }, // iPhone XS Max
  { width: 828, height: 1792 },  // iPhone XR
  { width: 750, height: 1334 },  // iPhone 8/7/6s/6
  { width: 640, height: 1136 },  // iPhone SE
];

// Background color for splash screens
const SPLASH_BG_COLOR = '#0ea5e9'; // Primary color

// Generate standard icons
async function generateIcons() {
  console.log('Generating standard icons...');

  for (const size of ICON_SIZES) {
    const outputPath = path.join(OUTPUT_DIR, `icon-${size}x${size}.png`);

    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .png()
      .toFile(outputPath);

    console.log(`Generated: ${outputPath}`);
  }

  // Generate maskable icon (with padding for safe area)
  const maskableSize = 512;
  const padding = Math.floor(maskableSize * 0.1); // 10% padding as integer
  const innerSize = Math.floor(maskableSize - (padding * 2)); // Ensure integer value

  await sharp(SOURCE_LOGO)
    .resize(innerSize, innerSize)
    .extend({
      top: padding,
      bottom: padding,
      left: padding,
      right: padding,
      background: { r: 14, g: 165, b: 233, alpha: 1 } // Primary color
    })
    .png()
    .toFile(path.join(OUTPUT_DIR, `maskable-icon-${maskableSize}x${maskableSize}.png`));

  console.log(`Generated: maskable-icon-${maskableSize}x${maskableSize}.png`);
}

// Generate Apple touch icons
async function generateAppleTouchIcons() {
  console.log('Generating Apple touch icons...');

  // Generate primary Apple touch icons
  for (const size of APPLE_ICON_SIZES) {
    const outputPath = path.join(OUTPUT_DIR, `apple-touch-icon-${size}x${size}.png`);

    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .png()
      .toFile(outputPath);

    console.log(`Generated: ${outputPath}`);
  }

  // Generate additional Apple touch icons that might be requested
  for (const size of ADDITIONAL_APPLE_ICON_SIZES) {
    const outputPath = path.join(OUTPUT_DIR, `apple-touch-icon-${size}x${size}.png`);

    await sharp(SOURCE_LOGO)
      .resize(size, size)
      .png()
      .toFile(outputPath);

    console.log(`Generated: ${outputPath}`);
  }

  // Default Apple touch icon (180x180 is the standard size)
  await sharp(SOURCE_LOGO)
    .resize(180, 180)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'apple-touch-icon.png'));

  console.log('Generated: apple-touch-icon.png');

  // Generate apple-touch-icon-precomposed.png for older iOS versions
  await sharp(SOURCE_LOGO)
    .resize(180, 180)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'apple-touch-icon-precomposed.png'));

  console.log('Generated: apple-touch-icon-precomposed.png');
}

// Generate Apple splash screens
async function generateAppleSplashScreens() {
  console.log('Generating Apple splash screens...');

  // Create a square logo with a larger size for better visibility
  const logoSize = 300; // Increased size for the logo

  for (const { width, height } of APPLE_SPLASH_SCREENS) {
    const outputPath = path.join(OUTPUT_DIR, `apple-splash-${width}-${height}.png`);

    // Create a blank canvas with the background color
    const canvas = sharp({
      create: {
        width,
        height,
        channels: 4,
        background: { r: 14, g: 165, b: 233, alpha: 1 } // Primary color
      }
    });

    // Resize the logo - use the light or dark logo based on the background
    // For dark backgrounds, use the dark mode logo for better contrast
    const logoPath = DARK_MODE_LOGO;

    const logo = await sharp(logoPath)
      .resize(logoSize, logoSize)
      .toBuffer();

    // Calculate position to center the logo (ensure integer values)
    const left = Math.floor((width - logoSize) / 2);
    const top = Math.floor((height - logoSize) / 2);

    // Composite the logo onto the canvas
    await canvas
      .composite([
        {
          input: logo,
          left,
          top
        }
      ])
      .png()
      .toFile(outputPath);

    console.log(`Generated: ${outputPath}`);
  }
}

// Generate shortcut icons
async function generateShortcutIcons() {
  console.log('Generating shortcut icons...');

  // Generate meal icon
  await sharp(SOURCE_LOGO)
    .resize(192, 192)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'meal-icon-192x192.png'));

  console.log('Generated: meal-icon-192x192.png');

  // Generate dashboard icon
  await sharp(SOURCE_LOGO)
    .resize(192, 192)
    .png()
    .toFile(path.join(OUTPUT_DIR, 'dashboard-icon-192x192.png'));

  console.log('Generated: dashboard-icon-192x192.png');
}

// Copy logo to public directory for favicon
async function copyFavicon() {
  console.log('Copying favicon...');

  // Generate favicon.ico (typically 32x32)
  await sharp(SOURCE_LOGO)
    .resize(32, 32)
    .png()
    .toFile(path.join(__dirname, '../public/favicon.png'));

  console.log('Generated: favicon.png');
}

// Main function
async function main() {
  try {
    // Check if source files exist
    if (!fs.existsSync(SOURCE_LOGO)) {
      console.error(`Source logo not found: ${SOURCE_LOGO}`);
      process.exit(1);
    }

    if (!fs.existsSync(DARK_MODE_LOGO)) {
      console.warn(`Dark mode logo not found: ${DARK_MODE_LOGO}`);
      console.warn('Using regular logo for all assets');
    }

    // Create output directory if it doesn't exist
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
      console.log(`Created output directory: ${OUTPUT_DIR}`);
    }

    // Generate all assets
    await generateIcons();
    await generateAppleTouchIcons();
    await generateAppleSplashScreens();
    await generateShortcutIcons();
    await copyFavicon();

    // Create a README file in the icons directory
    fs.writeFileSync(
      path.join(OUTPUT_DIR, 'README.md'),
      '# PWA Icons\n\nThese icons are automatically generated from the source logo. Do not edit them directly.\n'
    );

    console.log('All PWA assets generated successfully!');
  } catch (error) {
    console.error('Error generating PWA assets:', error);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the main function
main();
