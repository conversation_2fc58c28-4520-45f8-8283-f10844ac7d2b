// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                   String       @id @default(uuid())
  email                String       @unique
  passwordHash         String
  gender               String?
  dob                  DateTime?
  heightCm             Float?
  weightKg             Float?
  startingWeightKg     Float?       // Initial weight from signup - cannot be changed
  goalType             String?
  targetWeight         Float?
  activityLevel        String?
  heightUnit           String?      @default("cm") // "cm" or "ft"
  weightUnit           String?      @default("kg") // "kg" or "lbs"
  apiProvider          String?      // "openai", "gemini", or "ollama"
  apiModel             String?      // Selected model name
  hasApiKeyConfigured  Boolean      @default(false) // Track if user has configured API credentials
  encryptedApiKey      String?      // Encrypted API key stored securely (for OpenAI/Gemini)
  encryptedClientId    String?      // Encrypted Client ID stored securely (for Ollama)
  encryptedClientSecret String?     // Encrypted Client Secret stored securely (for Ollama)

  // Onboarding tracking fields
  onboardingCompleted  Boolean      @default(false) // tracks if user finished all onboarding steps
  onboardingStep       Int?         // stores the current step number (0-15) where user left off
  onboardingData       Json?        // stores partial onboarding form data to preserve user input
  onboardingStartedAt  DateTime?    // timestamp when user first accessed onboarding
  onboardingCompletedAt DateTime?   // timestamp when user completed onboarding

  createdAt            DateTime     @default(now())
  updatedAt            DateTime     @updatedAt

  goals       Goal[]
  foods       Food[]
  mealsLogged MealLogged[]
  exercises   Exercise[]
  waterLogs   WaterLog[]
  weightLogs  WeightLog[]

  @@map("users")
}

model Goal {
  id               String   @id @default(uuid())
  userId           String
  startDate        DateTime
  endDate          DateTime
  targetChange     Float
  pace             Float
  dailyCalorieGoal Int
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("goals")
}

model Food {
  id        String   @id @default(uuid())
  userId    String?
  name      String
  calories  Int
  proteinG  Float
  carbsG    Float
  fatsG     Float
  imageUrl  String?
  source    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user        User?        @relation(fields: [userId], references: [id])
  mealsLogged MealLogged[]

  @@map("foods")
}

model MealLogged {
  id              String   @id @default(uuid())
  userId          String
  foodId          String?
  name            String?
  calories        Int?
  proteinG        Float?
  carbsG          Float?
  fatsG           Float?
  fiberG          Float?
  sugarG          Float?
  sodiumMg        Float?
  cholesterolMg   Float?
  saturatedFatG   Float?
  transFatG       Float?
  vitaminCMg      Float?
  calciumMg       Float?
  ironMg          Float?
  potassiumMg     Float?
  vitaminDMcg     Float?
  servingSizeG    String?
  imageUrl        String?
  items           Json?    // Store food breakdown items as JSON
  timestamp       DateTime
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User  @relation(fields: [userId], references: [id])
  food Food? @relation(fields: [foodId], references: [id])

  @@map("meals_logged")
}

model Exercise {
  id             String   @id @default(uuid())
  userId         String
  type           String
  description    String?
  caloriesBurned Int
  durationMinutes Int
  timestamp      DateTime
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("exercises")
}

model WaterLog {
  id         String   @id @default(uuid())
  userId     String
  mlConsumed Int
  timestamp  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("water_logs")
}

model WeightLog {
  id        String   @id @default(uuid())
  userId    String
  weightKg  Float
  timestamp DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("weight_logs")
}
