-- Add Ollama client credentials fields to users table
-- This migration adds support for storing encrypted client ID and client secret for Ollama authentication

-- Add new columns for Ollama client credentials
ALTER TABLE "users" ADD COLUMN "encryptedClientId" TEXT;
ALTER TABLE "users" ADD COLUMN "encryptedClientSecret" TEXT;

-- Update the comment for hasApiKeyConfigured to reflect it now handles all credential types
COMMENT ON COLUMN "users"."hasApiKeyConfigured" IS 'Track if user has configured API credentials (API key or client credentials)';

-- Update the comment for apiProvider to include Ollama
COMMENT ON COLUMN "users"."apiProvider" IS 'AI provider: openai, gemini, or ollama';
