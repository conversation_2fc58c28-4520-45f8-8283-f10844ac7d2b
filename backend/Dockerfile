FROM node:18-alpine

WORKDIR /app

# Install OpenSSL for Prisma, postgresql-client for database health checks, and wget for container health checks
RUN apk add --no-cache openssl postgresql-client wget

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy prisma schema
COPY prisma ./prisma/

# Generate Prisma client
RUN npx prisma generate

# Copy the rest of the application
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Copy startup script
COPY scripts/start.sh ./scripts/start.sh
RUN chmod +x ./scripts/start.sh

# Expose the port the app runs on
EXPOSE 86

# Command to run the application with database initialization
CMD ["/bin/sh", "./scripts/start.sh"]
