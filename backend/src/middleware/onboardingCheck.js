const jwt = require('jsonwebtoken');

/**
 * Middleware to check if user has completed onboarding
 * Redirects to onboarding if not completed
 */
const requireOnboardingComplete = async (req, res, next) => {
  try {
    // First verify the token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;

    // Check user's onboarding status
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        onboardingCompleted: true,
        onboardingStep: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // If onboarding is not completed, return onboarding required response
    if (!user.onboardingCompleted) {
      return res.status(403).json({
        error: 'Onboarding required',
        onboardingRequired: true,
        currentStep: user.onboardingStep || 0
      });
    }

    // Onboarding is completed, proceed to next middleware
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(403).json({ error: 'Token expired' });
    }
    
    console.error('Error in onboarding check middleware:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

/**
 * Middleware to check if user has completed onboarding (optional)
 * Adds onboarding status to request but doesn't block access
 */
const checkOnboardingStatus = async (req, res, next) => {
  try {
    // First verify the token
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.user = decoded;

    // Check user's onboarding status
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        onboardingCompleted: true,
        onboardingStep: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Add onboarding status to request
    req.onboardingStatus = {
      completed: user.onboardingCompleted,
      currentStep: user.onboardingStep
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(403).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(403).json({ error: 'Token expired' });
    }
    
    console.error('Error in onboarding status check middleware:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  requireOnboardingComplete,
  checkOnboardingStatus
};
