const crypto = require('crypto');

// Encryption configuration - using simpler AES-256-CBC for compatibility
const ALGORITHM = 'aes-256-cbc';
const IV_LENGTH = 16; // 128 bits

/**
 * Generate encryption key from environment variable
 * In production, this should be a secure random key stored in environment variables
 */
function getEncryptionKey() {
  const key = process.env.API_KEY_ENCRYPTION_SECRET || 'calcounta-api-encryption-key-2024-secure';
  
  // Create a consistent 32-byte key from the secret
  return crypto.createHash('sha256').update(key).digest();
}

/**
 * Encrypt an API key
 * @param {string} apiKey - The API key to encrypt
 * @returns {string} - Base64 encoded encrypted data with IV
 */
function encryptApiKey(apiKey) {
  if (!apiKey || typeof apiKey !== 'string') {
    throw new Error('API key must be a non-empty string');
  }

  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipher(ALGORITHM, key);

    let encrypted = cipher.update(apiKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Combine IV and encrypted data
    const combined = iv.toString('hex') + ':' + encrypted;
    
    return Buffer.from(combined).toString('base64');
  } catch (error) {
    console.error('Error encrypting API key:', error);
    throw new Error('Failed to encrypt API key');
  }
}

/**
 * Decrypt an API key
 * @param {string} encryptedData - Base64 encoded encrypted data
 * @returns {string} - The decrypted API key
 */
function decryptApiKey(encryptedData) {
  if (!encryptedData || typeof encryptedData !== 'string') {
    throw new Error('Encrypted data must be a non-empty string');
  }

  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64').toString();
    
    // Split IV and encrypted data
    const [ivHex, encrypted] = combined.split(':');
    const iv = Buffer.from(ivHex, 'hex');

    const decipher = crypto.createDecipher(ALGORITHM, key);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Error decrypting API key:', error);
    throw new Error('Failed to decrypt API key');
  }
}

/**
 * Validate that an encrypted API key can be decrypted
 * @param {string} encryptedData - Base64 encoded encrypted data
 * @returns {boolean} - True if valid, false otherwise
 */
function validateEncryptedApiKey(encryptedData) {
  try {
    const decrypted = decryptApiKey(encryptedData);
    return typeof decrypted === 'string' && decrypted.length > 0;
  } catch (error) {
    return false;
  }
}

module.exports = {
  encryptApiKey,
  decryptApiKey,
  validateEncryptedApiKey
};
