const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();

// Middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid token' });
  }
};

// Get all foods (user's custom foods and common foods)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const foods = await req.prisma.food.findMany({
      where: {
        OR: [
          { user: { id: req.user.userId } },
          { userId: null } // Common foods
        ]
      },
      orderBy: {
        name: 'asc'
      }
    });

    res.json(foods);
  } catch (error) {
    console.error('Error fetching foods:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Search foods
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const foods = await req.prisma.food.findMany({
      where: {
        OR: [
          { user: { id: req.user.userId } },
          { userId: null } // Common foods
        ],
        name: {
          contains: query,
          mode: 'insensitive'
        }
      },
      orderBy: {
        name: 'asc'
      },
      take: 20
    });

    res.json(foods);
  } catch (error) {
    console.error('Error searching foods:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create a new food
router.post('/', 
  authenticateToken,
  [
    body('name').notEmpty().withMessage('Name is required'),
    body('calories').isInt({ min: 0 }).withMessage('Calories must be a positive integer'),
    body('proteinG').isFloat({ min: 0 }).withMessage('Protein must be a positive number'),
    body('carbsG').isFloat({ min: 0 }).withMessage('Carbs must be a positive number'),
    body('fatsG').isFloat({ min: 0 }).withMessage('Fats must be a positive number'),
    body('source').notEmpty().withMessage('Source is required')
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const food = await req.prisma.food.create({
        data: {
          user: {
            connect: {
              id: req.user.userId
            }
          },
          name: req.body.name,
          calories: req.body.calories,
          proteinG: req.body.proteinG,
          carbsG: req.body.carbsG,
          fatsG: req.body.fatsG,
          imageUrl: req.body.imageUrl,
          source: req.body.source
        }
      });

      res.status(201).json(food);
    } catch (error) {
      console.error('Error creating food:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete a food
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const food = await req.prisma.food.findUnique({
      where: { id: req.params.id }
    });

    if (!food) {
      return res.status(404).json({ error: 'Food not found' });
    }

    // Only allow deletion of user's own foods
    if (food.userId !== req.user.userId) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    await req.prisma.food.delete({
      where: { id: req.params.id }
    });

    res.status(204).send();
  } catch (error) {
    console.error('Error deleting food:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
