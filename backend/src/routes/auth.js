const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');

const router = express.Router();

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post(
  '/register',
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters long'),
    body('gender').optional().isIn(['male', 'female', 'other']).withMessage('Gender must be male, female, or other'),
    body('dob').optional().isISO8601().withMessage('Date of birth must be a valid date'),
    body('height_cm').optional().isFloat({ min: 50, max: 300 }).withMessage('Height must be between 50 and 300 cm'),
    body('weight_kg').optional().isFloat({ min: 20, max: 500 }).withMessage('Weight must be between 20 and 500 kg'),
    body('goal_type').optional().isIn(['lose', 'gain', 'maintain']).withMessage('Goal type must be lose, gain, or maintain'),
    body('target_weight').optional().isFloat({ min: 20, max: 500 }).withMessage('Target weight must be between 20 and 500 kg'),
    body('activity_level').optional().isIn(['sedentary', 'light', 'moderate', 'active', 'very_active']).withMessage('Invalid activity level')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { 
        email, 
        password, 
        gender, 
        dob, 
        height_cm, 
        weight_kg, 
        goal_type, 
        target_weight, 
        activity_level 
      } = req.body;

      // Check if user already exists
      const existingUser = await req.prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        return res.status(400).json({ error: 'User already exists' });
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const passwordHash = await bcrypt.hash(password, salt);

      // Create user
      const user = await req.prisma.user.create({
        data: {
          email,
          passwordHash,
          gender,
          dob: dob ? new Date(dob) : null,
          heightCm: height_cm,
          weightKg: weight_kg,
          startingWeightKg: weight_kg, // Set starting weight - this cannot be changed later
          goalType: goal_type,
          targetWeight: target_weight,
          activityLevel: activity_level
        }
      });

      // Create initial weight log entry if weight was provided
      if (weight_kg) {
        try {
          await req.prisma.weightLog.create({
            data: {
              user: {
                connect: {
                  id: user.id
                }
              },
              weightKg: weight_kg,
              timestamp: new Date() // Use signup date as the initial weight timestamp
            }
          });
          console.log('Initial weight log created for user:', user.id, 'Weight:', weight_kg, 'kg');
        } catch (weightLogError) {
          console.error('Error creating initial weight log:', weightLogError);
          // Don't fail registration if weight log creation fails
        }
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id },
        process.env.JWT_SECRET,
        { expiresIn: '7d' }
      );

      res.status(201).json({
        id: user.id,
        email: user.email,
        token,
        onboardingCompleted: user.onboardingCompleted || false,
        onboardingStep: user.onboardingStep || null
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ error: 'Server error' });
    }
  }
);

/**
 * @route GET /api/auth/check-email
 * @desc Check if email is already registered
 * @access Public
 */
router.get('/check-email', async (req, res) => {
  try {
    const { email } = req.query;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // Basic email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ error: 'Invalid email format' });
    }

    // Check if user exists
    const existingUser = await req.prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      select: { id: true } // Only select id to minimize data exposure
    });

    res.json({
      exists: !!existingUser,
      email: email.toLowerCase()
    });
  } catch (error) {
    console.error('Email check error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

/**
 * @route POST /api/auth/login
 * @desc Login user
 * @access Public
 */
router.post(
  '/login',
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').exists().withMessage('Password is required')
  ],
  async (req, res) => {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, password } = req.body;

      // Find user
      const user = await req.prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          passwordHash: true,
          onboardingCompleted: true,
          onboardingStep: true
        }
      });

      if (!user) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Verify password
      const isMatch = await bcrypt.compare(password, user.passwordHash);
      if (!isMatch) {
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Generate JWT token
      const token = jwt.sign(
        { userId: user.id },
        process.env.JWT_SECRET,
        { expiresIn: '7d' }
      );

      res.json({
        id: user.id,
        email: user.email,
        token,
        onboardingCompleted: user.onboardingCompleted,
        onboardingStep: user.onboardingStep
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ error: 'Server error' });
    }
  }
);

module.exports = router;
