const express = require('express');
const { body, query, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get current weight (latest by timestamp)
router.get('/current',
  authenticateToken,
  async (req, res) => {
    try {
      // Get the latest weight log by timestamp
      const latestWeightLog = await req.prisma.weightLog.findFirst({
        where: {
          user: {
            id: req.user.userId
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      if (!latestWeightLog) {
        return res.status(404).json({ error: 'No weight data found' });
      }

      res.json({
        weightKg: latestWeightLog.weightKg,
        timestamp: latestWeightLog.timestamp,
        id: latestWeightLog.id
      });
    } catch (error) {
      console.error('Error fetching current weight:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get historical weight data
router.get('/historical',
  authenticateToken,
  [
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : 100;

      let dateFilter = {};
      if (req.query.startDate) {
        dateFilter.gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        dateFilter.lte = new Date(req.query.endDate);
      }

      const whereClause = {
        user: {
          id: req.user.userId
        }
      };

      if (Object.keys(dateFilter).length > 0) {
        whereClause.timestamp = dateFilter;
      }

      const weightLogs = await req.prisma.weightLog.findMany({
        where: whereClause,
        orderBy: {
          timestamp: 'asc' // Chronological order for historical data
        },
        take: limit
      });

      res.json(weightLogs);
    } catch (error) {
      console.error('Error fetching historical weight data:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get weight logs (legacy endpoint - kept for backward compatibility)
router.get('/',
  authenticateToken,
  [
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const limit = req.query.limit ? parseInt(req.query.limit) : 30;

      let dateFilter = {};
      if (req.query.startDate) {
        dateFilter.gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        dateFilter.lte = new Date(req.query.endDate);
      }

      const whereClause = {
        user: {
          id: req.user.userId
        }
      };

      if (Object.keys(dateFilter).length > 0) {
        whereClause.timestamp = dateFilter;
      }

      const weightLogs = await req.prisma.weightLog.findMany({
        where: whereClause,
        orderBy: {
          timestamp: 'desc'
        },
        take: limit
      });

      res.json(weightLogs.reverse()); // Reverse to get chronological order
    } catch (error) {
      console.error('Error fetching weight logs:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Log weight
router.post('/',
  authenticateToken,
  [
    body('weightKg').isFloat({ min: 20, max: 500 }),
    body('timestamp').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      // Check if there's already a weight log for today
      const today = new Date();
      const startOfDay = new Date(today.setHours(0, 0, 0, 0));
      const endOfDay = new Date(today.setHours(23, 59, 59, 999));

      const existingLog = await req.prisma.weightLog.findFirst({
        where: {
          user: {
            id: req.user.userId
          },
          timestamp: {
            gte: startOfDay,
            lte: endOfDay
          }
        }
      });

      let weightLog;
      if (existingLog) {
        // Update existing log
        weightLog = await req.prisma.weightLog.update({
          where: { id: existingLog.id },
          data: {
            weightKg: req.body.weightKg,
            timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
          }
        });
      } else {
        // Create new log
        weightLog = await req.prisma.weightLog.create({
          data: {
            user: {
              connect: {
                id: req.user.userId
              }
            },
            weightKg: req.body.weightKg,
            timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
          }
        });
      }

      // Only update the user's current weight if this is the latest weight log by timestamp
      const latestWeightLog = await req.prisma.weightLog.findFirst({
        where: {
          user: {
            id: req.user.userId
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      // If this new/updated log is the latest by timestamp, update user's current weight
      if (latestWeightLog && latestWeightLog.id === weightLog.id) {
        await req.prisma.user.update({
          where: { id: req.user.userId },
          data: { weightKg: req.body.weightKg }
        });
      }

      res.status(201).json(weightLog);
    } catch (error) {
      console.error('Error logging weight:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete a weight log
router.delete('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const weightLog = await req.prisma.weightLog.findUnique({
        where: { id: req.params.id }
      });

      if (!weightLog) {
        return res.status(404).json({ error: 'Weight log not found' });
      }

      if (weightLog.userId !== req.user.userId) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      await req.prisma.weightLog.delete({
        where: { id: req.params.id }
      });

      // After deletion, update user's current weight to the latest remaining weight log
      const latestWeightLog = await req.prisma.weightLog.findFirst({
        where: {
          user: {
            id: req.user.userId
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      // Update user's current weight to the latest weight log, or null if no logs remain
      await req.prisma.user.update({
        where: { id: req.user.userId },
        data: { weightKg: latestWeightLog ? latestWeightLog.weightKg : null }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting weight log:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Utility endpoint to recalculate current weight based on latest timestamp
router.post('/recalculate-current',
  authenticateToken,
  async (req, res) => {
    try {
      // Find the latest weight log by timestamp
      const latestWeightLog = await req.prisma.weightLog.findFirst({
        where: {
          user: {
            id: req.user.userId
          }
        },
        orderBy: {
          timestamp: 'desc'
        }
      });

      // Update user's current weight to match the latest weight log
      const updatedUser = await req.prisma.user.update({
        where: { id: req.user.userId },
        data: { weightKg: latestWeightLog ? latestWeightLog.weightKg : null }
      });

      res.json({
        message: 'Current weight recalculated successfully',
        currentWeight: updatedUser.weightKg,
        latestLogTimestamp: latestWeightLog ? latestWeightLog.timestamp : null
      });
    } catch (error) {
      console.error('Error recalculating current weight:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
