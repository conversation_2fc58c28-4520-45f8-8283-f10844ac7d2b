const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { encryptApiKey, decryptApiKey, validateEncryptedApiKey } = require('../services/encryptionService');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        email: true,
        gender: true,
        dob: true,
        heightCm: true,
        weightKg: true,
        startingWeightKg: true,
        goalType: true,
        targetWeight: true,
        activityLevel: true,
        heightUnit: true,
        weightUnit: true,
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true,
        onboardingCompleted: true,
        onboardingStep: true,
        onboardingStartedAt: true,
        onboardingCompletedAt: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile
router.put('/profile',
  authenticateToken,
  [
    body('gender').optional().isString(),
    body('dob').optional().isISO8601(),
    body('heightCm').optional().isFloat({ min: 50, max: 300 }),
    body('weightKg').optional().isFloat({ min: 20, max: 500 }),
    body('goalType').optional().isIn(['lose', 'maintain', 'gain']),
    body('targetWeight').optional().isFloat({ min: 20, max: 500 }),
    body('activityLevel').optional().isIn(['sedentary', 'light', 'moderate', 'active', 'very_active']),
    body('heightUnit').optional().isIn(['cm', 'ft']),
    body('weightUnit').optional().isIn(['kg', 'lbs']),
    body('apiProvider').optional().isIn(['openai', 'gemini']),
    body('apiModel').optional().isString(),
    body('hasApiKeyConfigured').optional().isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const updatedUser = await req.prisma.user.update({
        where: { id: req.user.userId },
        data: {
          gender: req.body.gender,
          dob: req.body.dob ? new Date(req.body.dob) : undefined,
          heightCm: req.body.heightCm,
          weightKg: req.body.weightKg,
          // Note: startingWeightKg is intentionally excluded - it cannot be modified after signup
          goalType: req.body.goalType,
          targetWeight: req.body.targetWeight,
          activityLevel: req.body.activityLevel,
          heightUnit: req.body.heightUnit,
          weightUnit: req.body.weightUnit,
          apiProvider: req.body.apiProvider,
          apiModel: req.body.apiModel,
          hasApiKeyConfigured: req.body.hasApiKeyConfigured
        },
        select: {
          id: true,
          email: true,
          gender: true,
          dob: true,
          heightCm: true,
          weightKg: true,
          startingWeightKg: true,
          goalType: true,
          targetWeight: true,
          activityLevel: true,
          heightUnit: true,
          weightUnit: true,
          apiProvider: true,
          apiModel: true,
          hasApiKeyConfigured: true,
          updatedAt: true
        }
      });

      res.json(updatedUser);
    } catch (error) {
      console.error('Error updating user profile:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get user streak
router.get('/streak', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const today = new Date();

    // Calculate streak by checking consecutive days with activity
    let streak = 0;
    let currentDate = new Date(today);

    // Check each day going backwards from today
    while (true) {
      const startOfDay = new Date(currentDate);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);

      // Check if user had any activity on this day
      const [mealsCount, exercisesCount, weightLogsCount] = await Promise.all([
        req.prisma.mealLogged.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }),
        req.prisma.exercise.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        }),
        req.prisma.weightLog.count({
          where: {
            userId: userId,
            timestamp: {
              gte: startOfDay,
              lte: endOfDay
            }
          }
        })
      ]);

      const hasActivity = mealsCount > 0 || exercisesCount > 0 || weightLogsCount > 0;

      if (hasActivity) {
        streak++;
        // Move to previous day
        currentDate.setDate(currentDate.getDate() - 1);
      } else {
        // No activity found, break the streak
        break;
      }

      // Safety check to prevent infinite loop (max 365 days)
      if (streak >= 365) {
        break;
      }
    }

    res.json({ streak });
  } catch (error) {
    console.error('Error calculating user streak:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Store API configuration (API key or client credentials)
router.post('/api-key', authenticateToken, [
  body('provider').isIn(['openai', 'gemini', 'ollama']).withMessage('Provider must be openai, gemini, or ollama'),
  body('model').isString().notEmpty().withMessage('Model is required'),
  // Conditional validation based on provider
  body('apiKey').optional().isString().withMessage('API key must be a string'),
  body('clientId').optional().isString().withMessage('Client ID must be a string'),
  body('clientSecret').optional().isString().withMessage('Client Secret must be a string')
], async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { provider, model, apiKey, clientId, clientSecret } = req.body;

    // Validate credentials based on provider
    if (provider === 'ollama') {
      if (!clientId || !clientSecret) {
        return res.status(400).json({ error: 'Client ID and Client Secret are required for Ollama' });
      }
    } else {
      if (!apiKey) {
        return res.status(400).json({ error: 'API key is required for this provider' });
      }
    }

    let updateData = {
      apiProvider: provider,
      apiModel: model,
      hasApiKeyConfigured: true
    };

    // Handle different credential types
    if (provider === 'ollama') {
      // For Ollama, store client credentials
      const encryptedClientId = encryptApiKey(clientId);
      const encryptedClientSecret = encryptApiKey(clientSecret);
      updateData.encryptedApiKey = null; // Clear API key
      updateData.encryptedClientId = encryptedClientId;
      updateData.encryptedClientSecret = encryptedClientSecret;
    } else {
      // For other providers, store API key
      const encryptedApiKey = encryptApiKey(apiKey);
      updateData.encryptedApiKey = encryptedApiKey;
      updateData.encryptedClientId = null; // Clear client credentials
      updateData.encryptedClientSecret = null;
    }

    // Update user with API configuration
    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: updateData,
      select: {
        id: true,
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true
      }
    });

    res.json({
      success: true,
      user: updatedUser
    });
  } catch (error) {
    console.error('Error storing API configuration:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get API configuration (decrypted for use)
router.get('/api-key', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        apiProvider: true,
        apiModel: true,
        hasApiKeyConfigured: true,
        encryptedApiKey: true,
        encryptedClientId: true,
        encryptedClientSecret: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (!user.hasApiKeyConfigured) {
      return res.status(404).json({ error: 'No API configuration found' });
    }

    try {
      let responseData = {
        provider: user.apiProvider,
        model: user.apiModel,
        isConfigured: true
      };

      // Handle different credential types based on provider
      if (user.apiProvider === 'ollama') {
        if (!user.encryptedClientId || !user.encryptedClientSecret) {
          return res.status(404).json({ error: 'No Ollama credentials configured' });
        }
        // Decrypt client credentials
        const decryptedClientId = decryptApiKey(user.encryptedClientId);
        const decryptedClientSecret = decryptApiKey(user.encryptedClientSecret);
        responseData.clientId = decryptedClientId;
        responseData.clientSecret = decryptedClientSecret;
      } else {
        if (!user.encryptedApiKey) {
          return res.status(404).json({ error: 'No API key configured' });
        }
        // Decrypt the API key
        const decryptedApiKey = decryptApiKey(user.encryptedApiKey);
        responseData.apiKey = decryptedApiKey;
      }

      res.json(responseData);
    } catch (decryptError) {
      console.error('Error decrypting credentials:', decryptError);
      res.status(500).json({ error: 'Failed to decrypt credentials' });
    }
  } catch (error) {
    console.error('Error retrieving API configuration:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete API configuration
router.delete('/api-key', authenticateToken, async (req, res) => {
  try {
    await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        apiProvider: null,
        apiModel: null,
        hasApiKeyConfigured: false,
        encryptedApiKey: null,
        encryptedClientId: null,
        encryptedClientSecret: null
      }
    });

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting API configuration:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Mark onboarding as completed
router.post('/complete-onboarding', authenticateToken, async (req, res) => {
  try {
    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        onboardingCompleted: true,
        onboardingCompletedAt: new Date()
      },
      select: {
        id: true,
        onboardingCompleted: true,
        onboardingCompletedAt: true
      }
    });

    res.json({
      success: true,
      message: 'Onboarding marked as completed',
      user: updatedUser
    });
  } catch (error) {
    console.error('Error completing onboarding:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
