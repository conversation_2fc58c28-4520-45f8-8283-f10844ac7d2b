const express = require('express');
const { body, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

/**
 * @route GET /api/onboarding/status
 * @desc Get user's onboarding status and progress
 * @access Private
 */
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: {
        id: true,
        onboardingCompleted: true,
        onboardingStep: true,
        onboardingData: true,
        onboardingStartedAt: true,
        onboardingCompletedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      onboardingCompleted: user.onboardingCompleted,
      currentStep: user.onboardingStep,
      onboardingData: user.onboardingData,
      startedAt: user.onboardingStartedAt,
      completedAt: user.onboardingCompletedAt
    });
  } catch (error) {
    console.error('Error fetching onboarding status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route POST /api/onboarding/start
 * @desc Mark onboarding as started
 * @access Private
 */
router.post('/start', authenticateToken, async (req, res) => {
  try {
    const user = await req.prisma.user.findUnique({
      where: { id: req.user.userId },
      select: { onboardingStartedAt: true }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Only set startedAt if it hasn't been set before
    const updateData = {};
    if (!user.onboardingStartedAt) {
      updateData.onboardingStartedAt = new Date();
    }

    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: updateData,
      select: {
        onboardingCompleted: true,
        onboardingStep: true,
        onboardingData: true,
        onboardingStartedAt: true
      }
    });

    res.json({
      success: true,
      onboarding: {
        completed: updatedUser.onboardingCompleted,
        currentStep: updatedUser.onboardingStep,
        data: updatedUser.onboardingData,
        startedAt: updatedUser.onboardingStartedAt
      }
    });
  } catch (error) {
    console.error('Error starting onboarding:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

/**
 * @route PUT /api/onboarding/progress
 * @desc Save onboarding progress (step and data)
 * @access Private
 */
router.put('/progress',
  authenticateToken,
  [
    body('step').isInt({ min: 0, max: 15 }).withMessage('Step must be between 0 and 15'),
    body('data').optional().isObject().withMessage('Data must be an object')
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { step, data } = req.body;

      const updatedUser = await req.prisma.user.update({
        where: { id: req.user.userId },
        data: {
          onboardingStep: step,
          onboardingData: data || {},
          // Set startedAt if not already set
          onboardingStartedAt: {
            setIfNull: new Date()
          }
        },
        select: {
          onboardingCompleted: true,
          onboardingStep: true,
          onboardingData: true,
          onboardingStartedAt: true
        }
      });

      res.json({
        success: true,
        onboarding: {
          completed: updatedUser.onboardingCompleted,
          currentStep: updatedUser.onboardingStep,
          data: updatedUser.onboardingData,
          startedAt: updatedUser.onboardingStartedAt
        }
      });
    } catch (error) {
      console.error('Error saving onboarding progress:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * @route POST /api/onboarding/complete
 * @desc Mark onboarding as completed and save final data
 * @access Private
 */
router.post('/complete',
  authenticateToken,
  [
    body('data').isObject().withMessage('Final onboarding data is required')
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { data } = req.body;
      const completedAt = new Date();

      // Update user with completion status and final data
      const updatedUser = await req.prisma.user.update({
        where: { id: req.user.userId },
        data: {
          onboardingCompleted: true,
          onboardingStep: 15, // Final step
          onboardingData: data,
          onboardingCompletedAt: completedAt,
          // Set startedAt if somehow not set
          onboardingStartedAt: {
            setIfNull: completedAt
          }
        },
        select: {
          id: true,
          onboardingCompleted: true,
          onboardingStep: true,
          onboardingCompletedAt: true
        }
      });

      res.json({
        success: true,
        message: 'Onboarding completed successfully',
        onboarding: {
          completed: updatedUser.onboardingCompleted,
          currentStep: updatedUser.onboardingStep,
          completedAt: updatedUser.onboardingCompletedAt
        }
      });
    } catch (error) {
      console.error('Error completing onboarding:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

/**
 * @route DELETE /api/onboarding/reset
 * @desc Reset onboarding progress (for development/testing)
 * @access Private
 */
router.delete('/reset', authenticateToken, async (req, res) => {
  try {
    const updatedUser = await req.prisma.user.update({
      where: { id: req.user.userId },
      data: {
        onboardingCompleted: false,
        onboardingStep: null,
        onboardingData: null,
        onboardingStartedAt: null,
        onboardingCompletedAt: null
      },
      select: {
        id: true,
        onboardingCompleted: true
      }
    });

    res.json({
      success: true,
      message: 'Onboarding reset successfully',
      onboarding: {
        completed: updatedUser.onboardingCompleted
      }
    });
  } catch (error) {
    console.error('Error resetting onboarding:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
