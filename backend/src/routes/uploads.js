const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { getAbsoluteUrl } = require('../utils/urlUtils');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../uploads/meals');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const ext = path.extname(file.originalname);
    cb(null, `${uuidv4()}${ext}`);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    console.log('=== FILE FILTER DEBUG (UPLOADS) ===');
    console.log('Original name:', file.originalname);
    console.log('MIME type:', file.mimetype);
    console.log('Field name:', file.fieldname);

    const allowedTypes = /jpeg|jpg|png|gif/;
    const ext = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    console.log('Extension test result:', ext);
    console.log('MIME type test result:', mimetype);

    if (ext && mimetype) {
      console.log('File accepted');
      return cb(null, true);
    }

    console.log('File rejected - Only image files are allowed');
    cb(new Error('Only image files are allowed'));
  }
});

/**
 * @route POST /api/uploads/meal-image
 * @desc Upload a meal image
 * @access Private
 */
router.post('/meal-image', authenticateToken, upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    // Get the file path
    const filePath = req.file.path;

    // Create a URL for the uploaded file
    // First, create a relative URL
    const relativeUrl = `/uploads/meals/${path.basename(filePath)}`;

    // Convert to an absolute URL
    const absoluteUrl = getAbsoluteUrl(relativeUrl);

    // Log the file path and URLs for debugging
    console.log('===== MEAL IMAGE UPLOADED =====');
    console.log('File path:', filePath);
    console.log('Relative URL:', relativeUrl);
    console.log('Absolute URL:', absoluteUrl);
    console.log('User ID:', req.user.userId);

    // Return the URLs to the client
    res.json({
      imageUrl: absoluteUrl,
      relativeUrl: relativeUrl,
      message: 'Image uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading meal image:', error);
    res.status(500).json({
      error: 'Failed to upload image',
      details: error.message
    });
  }
});

module.exports = router;
