const express = require('express');
const { body, query, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get water logs for a specific date
router.get('/',
  authenticateToken,
  [
    query('date').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const date = req.query.date ? new Date(req.query.date) : new Date();
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const waterLogs = await req.prisma.waterLog.findMany({
        where: {
          user: {
            id: req.user.userId
          },
          timestamp: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        orderBy: {
          timestamp: 'asc'
        }
      });

      // Calculate total water consumed for the day
      const totalWater = waterLogs.reduce((sum, log) => sum + log.mlConsumed, 0);

      res.json({
        logs: waterLogs,
        totalMl: totalWater
      });
    } catch (error) {
      console.error('Error fetching water logs:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Log water consumption
router.post('/',
  authenticateToken,
  [
    body('mlConsumed').isInt({ min: 0 }),
    body('timestamp').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const waterLog = await req.prisma.waterLog.create({
        data: {
          user: {
            connect: {
              id: req.user.userId
            }
          },
          mlConsumed: req.body.mlConsumed,
          timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
        }
      });

      res.status(201).json(waterLog);
    } catch (error) {
      console.error('Error logging water consumption:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete a water log
router.delete('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const waterLog = await req.prisma.waterLog.findUnique({
        where: { id: req.params.id }
      });

      if (!waterLog) {
        return res.status(404).json({ error: 'Water log not found' });
      }

      if (waterLog.userId !== req.user.userId) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      await req.prisma.waterLog.delete({
        where: { id: req.params.id }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting water log:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
