const express = require('express');
const { body, query, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get exercises for a specific date
router.get('/',
  authenticateToken,
  [
    query('date').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const date = req.query.date ? new Date(req.query.date) : new Date();
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const exercises = await req.prisma.exercise.findMany({
        where: {
          user: {
            id: req.user.userId
          },
          timestamp: {
            gte: startOfDay,
            lte: endOfDay
          }
        },
        orderBy: {
          timestamp: 'asc'
        }
      });

      res.json(exercises);
    } catch (error) {
      console.error('Error fetching exercises:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Log a new exercise
router.post('/',
  authenticateToken,
  [
    body('type').isString(),
    body('description').optional().isString(),
    body('caloriesBurned').isInt({ min: 0 }),
    body('durationMinutes').isInt({ min: 0 }),
    body('timestamp').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const exercise = await req.prisma.exercise.create({
        data: {
          user: {
            connect: {
              id: req.user.userId
            }
          },
          type: req.body.type,
          description: req.body.description,
          caloriesBurned: req.body.caloriesBurned,
          durationMinutes: req.body.durationMinutes,
          timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
        }
      });

      res.status(201).json(exercise);
    } catch (error) {
      console.error('Error logging exercise:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Delete an exercise
router.delete('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const exercise = await req.prisma.exercise.findUnique({
        where: { id: req.params.id }
      });

      if (!exercise) {
        return res.status(404).json({ error: 'Exercise not found' });
      }

      if (exercise.userId !== req.user.userId) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      await req.prisma.exercise.delete({
        where: { id: req.params.id }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting exercise:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
