const express = require('express');
const { body, query, validationResult } = require('express-validator');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { formatLocalDateToISO, logDateInfo } = require('../utils/dateUtils');

// Middleware to verify JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    req.user = user;
    next();
  });
};

// Get meals for a specific date or date range
router.get('/',
  authenticateToken,
  [
    query('date').optional().isISO8601(),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      console.log('===== REQUEST GET /api/meals =====');
      console.log('Headers:', JSON.stringify(req.headers, null, 2));
      console.log('Query params:', JSON.stringify(req.query, null, 2));

      let startDate, endDate;

      // Handle date range parameters
      if (req.query.start_date && req.query.end_date) {
        startDate = new Date(req.query.start_date);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(req.query.end_date);
        endDate.setHours(23, 59, 59, 999);
      }
      // Handle single date parameter
      else if (req.query.date) {
        const date = new Date(req.query.date);
        startDate = new Date(date);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(date);
        endDate.setHours(23, 59, 59, 999);
      }
      // Default to today
      else {
        const today = new Date();
        startDate = new Date(today);
        startDate.setHours(0, 0, 0, 0);

        endDate = new Date(today);
        endDate.setHours(23, 59, 59, 999);
      }

      const meals = await req.prisma.mealLogged.findMany({
        where: {
          user: {
            id: req.user.userId
          },
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          timestamp: 'asc'
        },
        include: {
          food: true
        }
      });

      console.log(`Found ${meals.length} meals between ${startDate.toISOString()} and ${endDate.toISOString()}`);

      // Log image URLs for debugging
      if (meals.length > 0) {
        console.log('Meal image URLs:');
        meals.forEach(meal => {
          console.log(`- Meal ID ${meal.id}: ${meal.imageUrl || 'No image'}`);
        });
      }

      console.log('===== RESPONSE GET /api/meals =====');
      console.log('Status: 200');

      res.json(meals);
    } catch (error) {
      console.error('Error fetching meals:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Log a new meal
router.post('/',
  authenticateToken,
  [
    body('foodId').optional().isString(),
    body('name').optional().isString(),
    // Custom validation for all numeric fields to handle null values
    body('calories').optional().custom(value => {
      if (value === null) return true;
      return Number.isInteger(value) && value >= 0;
    }),
    // Accept both snake_case and camelCase field names
    body('proteinG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('protein_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('carbsG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('carbs_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('fatsG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('fats_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('fiberG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('fiber_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('sugarG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('sugar_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('sodiumMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('sodium_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('cholesterolMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('cholesterol_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('saturatedFatG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('saturated_fat_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('transFatG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('trans_fat_g').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('vitaminCMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('vitamin_c_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('calciumMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('calcium_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('ironMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('iron_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('potassiumMg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('potassium_mg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('vitaminDMcg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('vitamin_d_mcg').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('servingSizeG').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    body('serving_size').optional().custom(value => {
      if (value === null) return true;
      return !isNaN(parseFloat(value)) && parseFloat(value) >= 0;
    }),
    // Make imageUrl validation more flexible - allow null values or strings
    body('imageUrl').optional().custom(value => {
      // Accept null, undefined, or string values
      if (value === null || value === undefined || typeof value === 'string') {
        return true;
      }
      return false;
    }),
    body('image_url').optional().custom(value => {
      // Accept null, undefined, or string values
      if (value === null || value === undefined || typeof value === 'string') {
        return true;
      }
      return false;
    }),
    body('items').optional().isArray().withMessage('Items must be an array'),
    body('items.*.name').optional().isString().withMessage('Item name must be a string'),
    body('items.*.calories').optional().isNumeric().withMessage('Item calories must be numeric'),
    body('items.*.portion').optional().isString().withMessage('Item portion must be a string'),
    body('timestamp').optional().isISO8601()
  ],
  async (req, res) => {
    console.log('===== ADD MEAL REQUEST RECEIVED =====');
    console.log('Request body:', JSON.stringify(req.body, null, 2));
    console.log('User ID:', req.user.userId);

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.error('===== ADD MEAL VALIDATION ERROR =====');
      console.error('Validation errors:', JSON.stringify(errors.array(), null, 2));

      // Check for specific validation issues and provide more detailed logging
      const errorFields = errors.array().map(err => err.path);
      console.error('Error fields:', errorFields);

      // Log the expected vs received types for problematic fields
      errors.array().forEach(err => {
        console.error(`Field ${err.path}: Expected ${err.type}, received ${typeof req.body[err.path]} with value:`, req.body[err.path]);

        // Special handling for imageUrl errors
        if (err.path === 'imageUrl') {
          console.error('Image URL validation failed. If this is a blob URL, it should be handled by the frontend.');
          console.error('URL format:', req.body[err.path]);
          console.error('URL type:', typeof req.body[err.path]);

          // Check if it's a blob URL that wasn't properly handled
          if (req.body[err.path] && req.body[err.path].toString().startsWith('blob:')) {
            console.error('This appears to be a blob URL which should be converted to null or a proper URL by the frontend');
          }
          // Check if it's an object that should be null
          else if (req.body[err.path] !== null && typeof req.body[err.path] === 'object') {
            console.error('The imageUrl is an object but should be null or a string');
          }
        }
      });

      return res.status(400).json({
        errors: errors.array(),
        message: 'Validation failed. Please check the data format.',
        details: 'If you are uploading an image, make sure it is in a valid format.'
      });
    }

    try {
      console.log('===== CREATING MEAL IN DATABASE =====');
      console.log('Request body:', JSON.stringify(req.body, null, 2));





      // Debug field mapping
      console.log('===== FIELD MAPPING DEBUG =====');
      console.log('protein_g from request:', req.body.protein_g);
      console.log('proteinG from request:', req.body.proteinG);
      console.log('carbs_g from request:', req.body.carbs_g);
      console.log('carbsG from request:', req.body.carbsG);
      console.log('fats_g from request:', req.body.fats_g);
      console.log('fatsG from request:', req.body.fatsG);

      // Create a data object for better debugging
      // Handle both snake_case (from frontend) and camelCase (from other sources) field names
      const mealData = {
        user: {
          connect: {
            id: req.user.userId
          }
        },
        foodId: req.body.foodId,
        name: req.body.name,
        calories: req.body.calories,
        // Handle both snake_case and camelCase field names
        proteinG: req.body.protein_g !== undefined ? req.body.protein_g : req.body.proteinG,
        carbsG: req.body.carbs_g !== undefined ? req.body.carbs_g : req.body.carbsG,
        fatsG: req.body.fats_g !== undefined ? req.body.fats_g : req.body.fatsG,
        fiberG: req.body.fiber_g !== undefined ? req.body.fiber_g : (req.body.fiberG || null),
        sugarG: req.body.sugar_g !== undefined ? req.body.sugar_g : (req.body.sugarG || null),
        sodiumMg: req.body.sodium_mg !== undefined ? req.body.sodium_mg : (req.body.sodiumMg || null),
        cholesterolMg: req.body.cholesterol_mg !== undefined ? req.body.cholesterol_mg : (req.body.cholesterolMg || null),
        saturatedFatG: req.body.saturated_fat_g !== undefined ? req.body.saturated_fat_g : (req.body.saturatedFatG || null),
        transFatG: req.body.trans_fat_g !== undefined ? req.body.trans_fat_g : (req.body.transFatG || null),
        vitaminCMg: req.body.vitamin_c_mg !== undefined ? req.body.vitamin_c_mg : (req.body.vitaminCMg || null),
        calciumMg: req.body.calcium_mg !== undefined ? req.body.calcium_mg : (req.body.calciumMg || null),
        ironMg: req.body.iron_mg !== undefined ? req.body.iron_mg : (req.body.ironMg || null),
        potassiumMg: req.body.potassium_mg !== undefined ? req.body.potassium_mg : (req.body.potassiumMg || null),
        vitaminDMcg: req.body.vitamin_d_mcg !== undefined ? req.body.vitamin_d_mcg : (req.body.vitaminDMcg || null),
        servingSizeG: req.body.serving_size !== undefined ? String(req.body.serving_size) : (req.body.servingSizeG !== undefined ? String(req.body.servingSizeG) : null),
        imageUrl: req.body.image_url !== undefined ? req.body.image_url : req.body.imageUrl,
        items: req.body.items || null,
        timestamp: req.body.timestamp ? new Date(req.body.timestamp) : new Date()
      };

      // Debug mapped values
      console.log('===== MAPPED VALUES DEBUG =====');
      console.log('Mapped proteinG:', mealData.proteinG);
      console.log('Mapped carbsG:', mealData.carbsG);
      console.log('Mapped fatsG:', mealData.fatsG);
      console.log('Mapped items:', JSON.stringify(mealData.items, null, 2));

      // Log timestamp information for debugging
      console.log('===== TIMESTAMP DEBUG =====');
      if (req.body.timestamp) {
        console.log('Original timestamp from request:', req.body.timestamp);
        console.log('Parsed timestamp:', new Date(req.body.timestamp).toISOString());
        console.log('Local date from timestamp:', new Date(req.body.timestamp).toLocaleString());
      } else {
        console.log('No timestamp provided, using current time');
      }
      logDateInfo(mealData.timestamp, 'Meal Timestamp');

      console.log('Meal data to be inserted:', JSON.stringify(mealData, null, 2));

      // All fields are now optional, so we don't need to check for required fields
      // Just log what fields are present for debugging
      console.log('===== MEAL FIELDS PRESENT =====');
      const nutrientFields = ['calories', 'proteinG', 'carbsG', 'fatsG', 'servingSizeG',
                             'fiberG', 'sugarG', 'sodiumMg', 'cholesterolMg', 'saturatedFatG',
                             'transFatG', 'vitaminCMg', 'calciumMg', 'ironMg', 'potassiumMg', 'vitaminDMcg'];
      const presentFields = nutrientFields.filter(field => mealData[field] !== undefined && mealData[field] !== null);
      console.log('Present fields:', presentFields);

      const meal = await req.prisma.mealLogged.create({
        data: mealData
      });

      console.log('===== MEAL CREATED SUCCESSFULLY =====');
      console.log('Created meal:', JSON.stringify(meal, null, 2));

      res.status(201).json(meal);
    } catch (error) {
      console.error('===== ERROR LOGGING MEAL =====');
      console.error('Error type:', error.constructor.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);

      if (error.code) {
        console.error('Prisma error code:', error.code);

        // Handle specific Prisma error codes
        if (error.code === 'P2002') {
          console.error('Unique constraint violation');
        } else if (error.code === 'P2003') {
          console.error('Foreign key constraint violation');
        } else if (error.code === 'P2025') {
          console.error('Record not found');
        }
      }

      res.status(500).json({
        error: 'Internal server error',
        message: error.message,
        code: error.code
      });
    }
  }
);

// Delete a meal
router.delete('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const meal = await req.prisma.mealLogged.findUnique({
        where: { id: req.params.id }
      });

      if (!meal) {
        return res.status(404).json({ error: 'Meal not found' });
      }

      if (meal.userId !== req.user.userId) {
        return res.status(403).json({ error: 'Forbidden' });
      }

      await req.prisma.mealLogged.delete({
        where: { id: req.params.id }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting meal:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

// Get meal summary for a date range
router.get('/summary',
  authenticateToken,
  [
    query('start_date').isISO8601(),
    query('end_date').isISO8601()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      console.log('===== REQUEST GET /api/meals/summary =====');
      console.log('Query params:', JSON.stringify(req.query, null, 2));

      const startDate = new Date(req.query.start_date);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(req.query.end_date);
      endDate.setHours(23, 59, 59, 999);

      console.log(`Fetching meal summary between ${startDate.toISOString()} and ${endDate.toISOString()}`);

      // Get all meals in the date range
      const meals = await req.prisma.mealLogged.findMany({
        where: {
          user: {
            id: req.user.userId
          },
          timestamp: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          timestamp: 'asc'
        }
      });

      // Group meals by date
      const mealsByDate = {};

      meals.forEach(meal => {
        const dateStr = meal.timestamp.toISOString().split('T')[0];

        if (!mealsByDate[dateStr]) {
          mealsByDate[dateStr] = {
            date: dateStr,
            totalCalories: 0,
            totalProtein: 0,
            totalCarbs: 0,
            totalFats: 0,
            mealCount: 0
          };
        }

        mealsByDate[dateStr].totalCalories += meal.calories || 0;
        mealsByDate[dateStr].totalProtein += meal.proteinG || 0;
        mealsByDate[dateStr].totalCarbs += meal.carbsG || 0;
        mealsByDate[dateStr].totalFats += meal.fatsG || 0;
        mealsByDate[dateStr].mealCount += 1;
      });

      // Convert to array and sort by date
      const summaryData = Object.values(mealsByDate).sort((a, b) =>
        new Date(a.date) - new Date(b.date)
      );

      console.log(`Generated summary for ${summaryData.length} days`);

      res.json(summaryData);
    } catch (error) {
      console.error('Error fetching meal summary:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
);

module.exports = router;
