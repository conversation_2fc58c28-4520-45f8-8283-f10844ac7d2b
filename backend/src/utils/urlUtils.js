/**
 * Utility functions for handling URLs
 */

/**
 * Get the base URL for the server
 * @returns {string} The base URL for the server
 */
const getBaseUrl = () => {
  // In production, use the environment variable or default to the domain
  if (process.env.NODE_ENV === 'production') {
    return process.env.BASE_URL || 'https://calcounta.airkohsm.co.in';
  }
  
  // In development, use localhost with the port
  const port = process.env.PORT || 86;
  return `http://localhost:${port}`;
};

/**
 * Convert a relative URL to an absolute URL
 * @param {string} relativeUrl - The relative URL to convert
 * @returns {string} The absolute URL
 */
const getAbsoluteUrl = (relativeUrl) => {
  if (!relativeUrl) return null;
  
  // If it's already an absolute URL, return it as is
  if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://')) {
    return relativeUrl;
  }
  
  // Make sure the relative URL starts with a slash
  const normalizedUrl = relativeUrl.startsWith('/') ? relativeUrl : `/${relativeUrl}`;
  
  // Combine the base URL with the relative URL
  return `${getBaseUrl()}${normalizedUrl}`;
};

module.exports = {
  getBaseUrl,
  getAbsoluteUrl
};
