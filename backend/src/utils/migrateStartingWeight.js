const { PrismaClient } = require('@prisma/client');

/**
 * Migration script to populate startingWeightKg for existing users
 * This script should be run once after adding the startingWeightKg field
 */
async function migrateStartingWeight() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 Starting migration of starting weights...');

    // First, check if the startingWeightKg field exists in the database
    try {
      await prisma.user.findFirst({
        select: { startingWeightKg: true }
      });
    } catch (error) {
      if (error.message.includes('Unknown field') || error.message.includes('startingWeightKg')) {
        console.log('⚠️  startingWeightKg field not found in database schema. Skipping migration.');
        console.log('   This is normal for new installations or if the schema hasn\'t been updated yet.');
        return;
      }
      throw error;
    }

    // Get all users who don't have startingWeightKg set but have weightKg
    const usersToMigrate = await prisma.user.findMany({
      where: {
        startingWeightKg: null,
        weightKg: {
          not: null
        }
      },
      include: {
        weightLogs: {
          orderBy: {
            timestamp: 'asc'
          },
          take: 1
        }
      }
    });
    
    console.log(`📊 Found ${usersToMigrate.length} users to migrate`);

    if (usersToMigrate.length === 0) {
      console.log('✅ No users need migration. All users already have starting weights set.');
      return;
    }

    let migratedCount = 0;
    let createdWeightLogs = 0;
    
    for (const user of usersToMigrate) {
      try {
        let startingWeight = null;
        
        // Strategy 1: Use the earliest weight log if it exists
        if (user.weightLogs.length > 0) {
          startingWeight = user.weightLogs[0].weightKg;
          console.log(`📝 User ${user.email}: Using earliest weight log (${startingWeight}kg) as starting weight`);
        }
        // Strategy 2: Use current weightKg as starting weight
        else if (user.weightKg) {
          startingWeight = user.weightKg;
          console.log(`📝 User ${user.email}: Using current weight (${startingWeight}kg) as starting weight`);
          
          // Create an initial weight log entry with the user's creation date
          try {
            await prisma.weightLog.create({
              data: {
                userId: user.id,
                weightKg: startingWeight,
                timestamp: user.createdAt // Use signup date as the initial weight timestamp
              }
            });
            createdWeightLogs++;
            console.log(`   ✅ Created initial weight log for user ${user.email}`);
          } catch (weightLogError) {
            console.error(`   ❌ Failed to create weight log for user ${user.email}:`, weightLogError.message);
          }
        }
        
        if (startingWeight) {
          // Update the user's startingWeightKg
          await prisma.user.update({
            where: { id: user.id },
            data: { startingWeightKg: startingWeight }
          });
          
          migratedCount++;
          console.log(`   ✅ Set starting weight for user ${user.email}: ${startingWeight}kg`);
        } else {
          console.log(`   ⚠️  Skipped user ${user.email}: No weight data available`);
        }
        
      } catch (userError) {
        console.error(`❌ Failed to migrate user ${user.email}:`, userError.message);
      }
    }
    
    console.log('\n🎉 Starting weight migration completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Users migrated: ${migratedCount}/${usersToMigrate.length}`);
    console.log(`   - Weight logs created: ${createdWeightLogs}`);
    console.log(`   - System ready for new user registrations with permanent starting weights`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateStartingWeight()
    .then(() => {
      console.log('✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateStartingWeight };
