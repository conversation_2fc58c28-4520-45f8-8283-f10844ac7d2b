/**
 * Unit conversion utilities for height and weight
 */

// Conversion constants
const CM_TO_INCHES = 2.54;
const INCHES_PER_FOOT = 12;
const KG_TO_LBS = 2.20462;

/**
 * Convert centimeters to feet and inches
 * @param {number} cm - Height in centimeters
 * @returns {Object} - Object with feet and inches properties
 */
const cmToFeetInches = (cm) => {
  if (!cm || cm <= 0) return { feet: 0, inches: 0 };
  
  const totalInches = cm / CM_TO_INCHES;
  const feet = Math.floor(totalInches / INCHES_PER_FOOT);
  const inches = Math.round((totalInches % INCHES_PER_FOOT) * 10) / 10; // Round to 1 decimal place
  
  return { feet, inches };
};

/**
 * Convert feet and inches to centimeters
 * @param {number} feet - Height in feet
 * @param {number} inches - Additional inches
 * @returns {number} - Height in centimeters
 */
const feetInchesToCm = (feet, inches = 0) => {
  if (!feet && !inches) return 0;
  
  const totalInches = (feet || 0) * INCHES_PER_FOOT + (inches || 0);
  return Math.round(totalInches * CM_TO_INCHES * 10) / 10; // Round to 1 decimal place
};

/**
 * Convert kilograms to pounds
 * @param {number} kg - Weight in kilograms
 * @returns {number} - Weight in pounds
 */
const kgToLbs = (kg) => {
  if (!kg || kg <= 0) return 0;
  return Math.round(kg * KG_TO_LBS * 10) / 10; // Round to 1 decimal place
};

/**
 * Convert pounds to kilograms
 * @param {number} lbs - Weight in pounds
 * @returns {number} - Weight in kilograms
 */
const lbsToKg = (lbs) => {
  if (!lbs || lbs <= 0) return 0;
  return Math.round((lbs / KG_TO_LBS) * 10) / 10; // Round to 1 decimal place
};

/**
 * Convert height based on target unit
 * @param {number} value - Current height value
 * @param {string} fromUnit - Current unit ("cm" or "ft")
 * @param {string} toUnit - Target unit ("cm" or "ft")
 * @returns {number|Object} - Converted height value
 */
const convertHeight = (value, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;
  
  if (fromUnit === "cm" && toUnit === "ft") {
    return cmToFeetInches(value);
  } else if (fromUnit === "ft" && toUnit === "cm") {
    // Assume value is in cm when stored as "ft" unit (we store everything in cm)
    return value;
  }
  
  return value;
};

/**
 * Convert weight based on target unit
 * @param {number} value - Current weight value
 * @param {string} fromUnit - Current unit ("kg" or "lbs")
 * @param {string} toUnit - Target unit ("kg" or "lbs")
 * @returns {number} - Converted weight value
 */
const convertWeight = (value, fromUnit, toUnit) => {
  if (!value || fromUnit === toUnit) return value;
  
  if (fromUnit === "kg" && toUnit === "lbs") {
    return kgToLbs(value);
  } else if (fromUnit === "lbs" && toUnit === "kg") {
    return lbsToKg(value);
  }
  
  return value;
};

/**
 * Format height for display based on unit preference
 * @param {number} heightCm - Height in centimeters (stored value)
 * @param {string} unit - Display unit ("cm" or "ft")
 * @returns {string} - Formatted height string
 */
const formatHeight = (heightCm, unit = "cm") => {
  if (!heightCm) return "";
  
  if (unit === "ft") {
    const { feet, inches } = cmToFeetInches(heightCm);
    return `${feet}'${inches}"`;
  }
  
  return `${heightCm} cm`;
};

/**
 * Format weight for display based on unit preference
 * @param {number} weightKg - Weight in kilograms (stored value)
 * @param {string} unit - Display unit ("kg" or "lbs")
 * @returns {string} - Formatted weight string
 */
const formatWeight = (weightKg, unit = "kg") => {
  if (!weightKg) return "";
  
  if (unit === "lbs") {
    const lbs = kgToLbs(weightKg);
    return `${lbs} lbs`;
  }
  
  return `${weightKg} kg`;
};

/**
 * Convert user data when unit preferences change
 * @param {Object} userData - Current user data
 * @param {string} newHeightUnit - New height unit preference
 * @param {string} newWeightUnit - New weight unit preference
 * @returns {Object} - Updated user data with converted values
 */
const convertUserDataForUnitChange = (userData, newHeightUnit, newWeightUnit) => {
  const updatedData = { ...userData };
  
  // Convert height if unit changed
  if (userData.heightUnit !== newHeightUnit && userData.heightCm) {
    // Height is always stored in cm, so no conversion needed for storage
    // Just update the unit preference
    updatedData.heightUnit = newHeightUnit;
  }
  
  // Convert weight if unit changed
  if (userData.weightUnit !== newWeightUnit) {
    updatedData.weightUnit = newWeightUnit;
    
    // Convert current weight and target weight if they exist
    if (userData.weightKg) {
      // Weight is always stored in kg, so no conversion needed for storage
      // Just update the unit preference
    }
    
    if (userData.targetWeight) {
      // Target weight is always stored in kg, so no conversion needed for storage
      // Just update the unit preference
    }
  }
  
  return updatedData;
};

module.exports = {
  cmToFeetInches,
  feetInchesToCm,
  kgToLbs,
  lbsToKg,
  convertHeight,
  convertWeight,
  formatHeight,
  formatWeight,
  convertUserDataForUnitChange,
  // Export constants for use in validation
  CM_TO_INCHES,
  INCHES_PER_FOOT,
  KG_TO_LBS
};
