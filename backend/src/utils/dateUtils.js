/**
 * Date and timezone utility functions for the backend
 */

/**
 * Get the application timezone from environment variables
 * Defaults to 'Asia/Kolkata' (Indian Standard Time) if not specified
 * 
 * @returns {string} The timezone identifier
 */
const getAppTimezone = () => {
  return process.env.TIMEZONE || 'Asia/Kolkata';
};

/**
 * Creates a date in the application's timezone
 * 
 * @returns {Date} Current date in the application's timezone
 */
const getCurrentDate = () => {
  return new Date();
};

/**
 * Formats a date to ISO string while preserving the local date
 * This ensures the date remains the same when stored in UTC format
 * 
 * @param {Date} date - The date to format (defaults to current date)
 * @returns {string} ISO string with the date preserved in local timezone
 */
const formatLocalDateToISO = (date = new Date()) => {
  // Get the timezone offset in minutes
  const timezoneOffsetMinutes = date.getTimezoneOffset();
  
  // Create a new date by adjusting for the timezone offset
  // This ensures the date remains the same in the local timezone when converted to ISO
  const localDate = new Date(date.getTime() - (timezoneOffsetMinutes * 60000));
  
  // Format to ISO string - this will be in UTC but with the correct local date
  return localDate.toISOString();
};

/**
 * Logs date information for debugging purposes
 * 
 * @param {Date} date - The date to log information about
 * @param {string} label - Optional label for the log
 */
const logDateInfo = (date = new Date(), label = 'Date Info') => {
  console.log(`===== ${label} =====`);
  console.log('Date object:', date);
  console.log('Local string:', date.toLocaleString());
  console.log('ISO string:', date.toISOString());
  console.log('Timezone offset (minutes):', date.getTimezoneOffset());
  console.log('Local date with offset adjustment:', 
    new Date(date.getTime() - (date.getTimezoneOffset() * 60000)).toISOString());
};

module.exports = {
  getAppTimezone,
  getCurrentDate,
  formatLocalDateToISO,
  logDateInfo
};
