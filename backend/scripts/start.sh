#!/bin/sh

# Backend startup script with automatic database initialization
# This script ensures the database is ready and migrations are applied before starting the application

set -e

echo "🚀 Starting Calcounta Backend..."

# Function to wait for PostgreSQL to be ready
wait_for_postgres() {
    echo "⏳ Waiting for PostgreSQL to be ready..."

    # Extract database connection details from DATABASE_URL
    # Format: postgresql://user:password@host:port/database
    DB_HOST=$(echo $DATABASE_URL | sed -n 's/.*@\([^:]*\):.*/\1/p')
    DB_PORT=$(echo $DATABASE_URL | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
    DB_USER=$(echo $DATABASE_URL | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
    DB_NAME=$(echo $DATABASE_URL | sed -n 's/.*\/\([^?]*\).*/\1/p')

    echo "📊 Database connection details:"
    echo "   Host: $DB_HOST"
    echo "   Port: $DB_PORT"
    echo "   User: $DB_USER"
    echo "   Database: $DB_NAME"

    # Wait for PostgreSQL to accept connections
    max_attempts=30
    attempt=1

    while [ $attempt -le $max_attempts ]; do
        echo "   Attempt $attempt/$max_attempts: Checking PostgreSQL connection..."

        if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" > /dev/null 2>&1; then
            echo "✅ PostgreSQL is ready!"
            return 0
        fi

        echo "   PostgreSQL not ready yet, waiting 2 seconds..."
        sleep 2
        attempt=$((attempt + 1))
    done

    echo "❌ Failed to connect to PostgreSQL after $max_attempts attempts"
    exit 1
}

# Function to run database migrations
run_migrations() {
    echo "🔄 Running database migrations..."

    # First, try to check if the database has the _prisma_migrations table
    if node -e "
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();

        async function checkMigrations() {
            try {
                await prisma.\$connect();
                // Try to query the migrations table
                await prisma.\$queryRaw\`SELECT * FROM _prisma_migrations LIMIT 1\`;
                console.log('MIGRATIONS_TABLE_EXISTS');
                await prisma.\$disconnect();
                process.exit(0);
            } catch (error) {
                console.log('MIGRATIONS_TABLE_NOT_EXISTS');
                await prisma.\$disconnect();
                process.exit(0);
            }
        }

        checkMigrations();
    " 2>/dev/null | grep -q "MIGRATIONS_TABLE_EXISTS"; then
        echo "📊 Database has migration history, running migrate deploy..."

        # Database has migration history, run migrate deploy
        if npx prisma migrate deploy; then
            echo "✅ Database migrations up to date!"

            # Also push any schema changes that aren't in migration files
            echo "🔄 Synchronizing schema with any direct changes..."
            if npx prisma db push --accept-data-loss; then
                echo "✅ Database schema fully synchronized!"
            else
                echo "❌ Failed to synchronize schema changes"
                exit 1
            fi
        else
            echo "❌ Failed to apply pending migrations"
            exit 1
        fi
    else
        echo "📝 Database schema not initialized, setting up from scratch..."

        # Database doesn't have migration history, need to baseline or push
        # First try to push the schema
        if npx prisma db push --accept-data-loss; then
            echo "✅ Database schema pushed successfully!"

            # Mark the current state as the baseline
            if npx prisma migrate resolve --applied 20250512063620_init; then
                echo "✅ Baseline migration marked as applied!"
            fi
            if npx prisma migrate resolve --applied 20250117000000_add_unit_preferences; then
                echo "✅ Unit preferences migration marked as applied!"
            fi
            if npx prisma migrate resolve --applied 20250512185033_add_nutrition_fields; then
                echo "✅ Nutrition fields migration marked as applied!"
            fi
            if npx prisma migrate resolve --applied 20250516160119_make_meal_fields_nullable; then
                echo "✅ Nullable fields migration marked as applied!"
            fi
        else
            echo "❌ Failed to push database schema"
            exit 1
        fi
    fi
}

# Function to run data migrations
run_data_migrations() {
    echo "🔄 Running data migrations..."

    # Run the starting weight migration for existing users
    if node -e "
        const { migrateStartingWeight } = require('./src/utils/migrateStartingWeight');

        async function runMigration() {
            try {
                await migrateStartingWeight();
                console.log('✅ Starting weight migration completed successfully!');
                process.exit(0);
            } catch (error) {
                console.error('❌ Starting weight migration failed:', error.message);
                // Don't fail the startup if migration fails - log and continue
                console.log('⚠️  Continuing startup despite migration failure...');
                process.exit(0);
            }
        }

        runMigration();
    "; then
        echo "✅ Data migrations completed!"
    else
        echo "⚠️  Data migrations had issues but continuing..."
    fi
}

# Function to verify database schema
verify_database() {
    echo "🔍 Verifying database schema..."

    # Generate Prisma client to ensure schema is in sync
    if npx prisma generate; then
        echo "✅ Prisma client generated successfully!"
    else
        echo "❌ Failed to generate Prisma client"
        exit 1
    fi

    # Test database connection with a simple query
    if node -e "
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();

        async function testConnection() {
            try {
                await prisma.\$connect();
                console.log('✅ Database connection test successful!');
                await prisma.\$disconnect();
                process.exit(0);
            } catch (error) {
                console.error('❌ Database connection test failed:', error.message);
                process.exit(1);
            }
        }

        testConnection();
    "; then
        echo "✅ Database verification completed!"
    else
        echo "❌ Database verification failed"
        exit 1
    fi
}

# Function to start the application
start_application() {
    echo "🎯 Starting the application..."
    exec npm start
}

# Main execution flow
main() {
    echo "🔧 Initializing Calcounta Backend..."
    echo "   Environment: ${NODE_ENV:-development}"
    echo "   Database URL: ${DATABASE_URL}"
    echo ""

    # Step 1: Wait for PostgreSQL to be ready
    wait_for_postgres
    echo ""

    # Step 2: Run database migrations
    run_migrations
    echo ""

    # Step 3: Verify database schema
    verify_database
    echo ""

    # Step 4: Run data migrations
    run_data_migrations
    echo ""

    # Step 5: Start the application
    echo "🎉 Database initialization completed successfully!"
    echo "🚀 Starting Calcounta Backend application..."
    echo ""
    start_application
}

# Execute main function
main
