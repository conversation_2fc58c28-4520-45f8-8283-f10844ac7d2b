{"name": "calcounta-backend", "version": "1.0.0", "description": "Backend API for Calcounta - AI-powered calorie tracking app", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "lint": "eslint ."}, "dependencies": {"@prisma/client": "^5.0.0", "axios": "^1.6.0", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.1", "pg": "^8.11.1", "uuid": "^9.0.0"}, "devDependencies": {"eslint": "^8.44.0", "jest": "^29.6.1", "nodemon": "^3.0.1", "prisma": "^5.0.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "prisma": {"schema": "prisma/schema.prisma"}}